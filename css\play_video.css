  /* 视频展示区域样式 */
  .video-container {
    width: 100%;
    margin: 0 auto;
    padding-top: 80px;
    padding-bottom: 80px;
}

.video-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.video-section h1 {
    font-size: 48px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 20px;
    color: #1d1d1f;
}

.video-section p {
    font-size: 21px;
    line-height: 1.5;
    text-align: center;
    margin-bottom: 40px;
    color: #515154;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.video-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.video-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-item:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    position: relative;
    width: 100%;
    padding-top: 56.25%; /* 16:9 比例 */
    background-size: cover;
    background-position: center;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.play-button:hover {
    background-color: rgba(255, 255, 255, 0.9);
}

.play-button::after {
    content: '';
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-left: 25px solid #0071e3;
    border-bottom: 15px solid transparent;
    margin-left: 5px;
}

.video-info {
    padding: 20px;
    background-color: #fff;
}

.video-info h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1d1d1f;
}

.video-info p {
    font-size: 14px;
    color: #515154;
    text-align: left;
    margin-bottom: 0;
}

/* 视频播放弹窗 */
.video-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.video-modal.active {
    display: flex;
}

.modal-content {
    position: relative;
    width: 80%;
    max-width: 1000px;
}

.close-button {
    position: absolute;
    top: -40px;
    right: 0;
    color: #fff;
    font-size: 30px;
    cursor: pointer;
}

.modal-video {
    width: 100%;
    aspect-ratio: 16/9;
}

/* 英雄区域 - 基础样式 */
.hero-section {
    position: relative;
    height: 80vh;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}

/* 第一个 hero-section - 径向渐变 */
.hero-section:first-of-type {
    background-image: radial-gradient(circle at center, #2981e5, #0a0a0a);
}

/* 第二个 hero-section - 线性渐变（上下） */
.hero-section:nth-of-type(2) {
    background-image: linear-gradient(to top, #91b9e7, #09396d);
}

.hero-content {
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-section h1 {
    font-size: 56px;
    font-weight: 600;
    margin-bottom: 20px;
}

.hero-section p {
    font-size: 24px;
    line-height: 1.5;
    margin-bottom: 30px;
}

.hero-play-button {
    display: inline-flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: 2px solid white;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.hero-play-button:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.hero-play-button svg {
    margin-right: 10px;
}

/* 添加菜单区域背景颜色 */
.sc-73348620-5 {
    background-image: url('../images/about_bg_header.jpg');
    background-size: cover;
    /* 覆盖整个区域 */
    background-position: top;
    /* 居中显示 */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    /* 添加阴影效果 */
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}