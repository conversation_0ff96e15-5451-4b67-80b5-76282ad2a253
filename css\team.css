/* Team section */
#team {
    position: relative;
    min-width: 100%;
    text-align: center;
    background-color: #f8f9fa;
    display: flex;
    justify-content: center;
    top: 0;
    z-index: 1;
}

#team h2 {
    font-size: 32px;
    margin-bottom: 32px;
}

.team-content {
    width: 100%;
    max-width: 1200px;
    padding: 20px 15px;
    box-sizing: border-box;
}

.product-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.feature-item {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    text-align: left;
    transition: all 0.3s ease;
}

.feature-icon {
    display: flex;
    align-items: flex-start;
    margin-top: 5px;
}

.feature-icon span {
    font-size: 60px;
    margin-right: 20px;
    line-height: 1;
    background-image: linear-gradient(135deg, #00c6ff, #0072ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.feature-text {
    flex: 1;
}

.feature-item h3 {
    font-size: 21px;
    margin: 0 0 10px 0;
    color: #333;
}

.feature-item p {
    font-size: 14px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}
