/* 用户指南样式 */
.user-guide-container {
    display: flex;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
    height: calc(100vh - 80px);
    position: fixed;
    top: 80px;
    left: 0;
    margin-top: 0;
    padding-bottom: 0; /* 移除底部填充，让内容区域控制填充 */
}

/* 左侧目录区域 */
.sidebar {
    width: 250px;
    background-color: #f5f5f5;
    padding: 30px 0;
    border-right: 1px solid #e0e0e0;
    height: 100%; /* 使用100%高度填充整个容器 */
    overflow-y: auto;
    position: relative; /* 改为相对定位 */
    top: 0; /* 移除顶部偏移 */
}

.sidebar h2 {
    padding: 0 0 20px;
    margin: 0;
    font-size: 20px;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
    text-align: center; /* 添加文本居中 */
}

.menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu li {
    margin: 0;
    padding: 0;
}

.menu li a {
    display: block;
    padding: 12px 0; /* 移除左右内边距 */
    color: #555;
    text-decoration: none;
    font-size: 15px;
    transition: all 0.3s;
    border-right: 3px solid transparent;
    text-align: center; /* 添加文本居中 */
}

.menu li.active a {
    background-color: #e8f4fc;
    color: #007bff;
    border-right: 3px solid #007bff;
    font-weight: 500;
    text-align: center; /* 确保活动项也居中 */
}

.menu li a:hover {
    background-color: #e8f4fc;
    color: #007bff;
}

.submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: #f0f0f0;
    display: none;
}

.menu li.active .submenu {
    display: block;
}

.submenu li a {
    padding: 10px 0; /* 移除左右内边距 */
    font-size: 14px;
    text-align: center; /* 添加文本居中 */
}

/* 右侧内容区域-滚动条显示与否 */
.content-area {
    flex: 1;
    padding: 20px 40px 1px 40px; /* 增加底部填充到80px */
    overflow-y: auto !important; /* 强制启用垂直滚动 */
    height: 100% !important; /* 确保高度为100% */
    box-sizing: border-box;
    margin-top: 0;
    scroll-behavior: smooth;
}

section {
    margin-bottom: 60px;
    scroll-margin-top: 20px;
}

section h1 {
    font-size: 32px;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

section h2 {
    font-size: 28px;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

section h3 {
    font-size: 22px;
    color: #444;
    margin-bottom: 15px;
}

section p {
    font-size: 16px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 15px;
}

section ul {
    padding-left: 20px;
    margin-bottom: 20px;
}

section ul li {
    font-size: 16px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 8px;
    list-style-type: disc;
}

/* 特性卡片 */
.feature-highlight {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 30px;
}

.feature-card {
    flex: 1;
    min-width: 250px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-card img {
    width: 60px;
    height: 60px;
    margin-bottom: 15px;
}

.feature-card h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #333;
}

.feature-card p {
    font-size: 14px;
    color: #666;
}

/* 步骤指南 */
.step-guide {
    margin-top: 30px;
}

.step {
    display: flex;
    margin-bottom: 30px;
    position: relative;
}

.step:not(:last-child):after {
    content: '';
    position: absolute;
    left: 20px;
    top: 50px;
    bottom: -20px;
    width: 2px;
    background-color: #e0e0e0;
}

.step-number {
    width: 40px;
    height: 40px;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.guide-img {
    max-width: 100%;
    border-radius: 8px;
    margin-top: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 功能详解 */
.feature-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #eee;
}

.feature-section:last-child {
    border-bottom: none;
}

.feature-img {
    max-width: 100%;
    border-radius: 8px;
    margin-top: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 高级功能 */
.advanced-features {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.advanced-feature {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
}

.advanced-feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.learn-more {
    display: inline-block;
    margin-top: 15px;
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.learn-more:hover {
    text-decoration: underline;
}

/* FAQ样式 */
.faq-container {
    margin-top: 30px;
}

.faq-item {
    margin-bottom: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.faq-question {
    padding: 15px 20px;
    background-color: #f5f5f5;
    font-weight: 500;
    cursor: pointer;
    position: relative;
}

.faq-question:after {
    content: '+';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    color: #007bff;
}

.faq-item.active .faq-question:after {
    content: '-';
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s;
}

.faq-item.active .faq-answer {
    padding: 20px;
    max-height: 500px;
}

/* 故障排除 */
.troubleshooting-container {
    margin-top: 30px;
}

.trouble-item {
    margin-bottom: 30px;
    padding: 25px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 更新日志 */
.updates-container {
    margin-top: 30px;
}

.update-item {
    margin-bottom: 25px;
    border-left: 3px solid #007bff;
    padding-left: 20px;
}

.update-version {
    font-weight: 500;
    font-size: 18px;
    margin-bottom: 10px;
    color: #333;
}

/* 联系我们 */
.contact-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 30px;
}

.contact-info {
    flex: 1;
    min-width: 300px;
}

.contact-form {
    flex: 1;
    min-width: 300px;
    background-color: #f9f9f9;
    padding: 25px;
    border-radius: 8px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
}

.submit-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.submit-btn:hover {
    background-color: #0056b3;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .user-guide-container {
        flex-direction: column;
        margin: 80px auto 40px;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        top: 0;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }

    .content-area {
        max-height: none;
    }
}

@media (max-width: 768px) {
    .feature-highlight {
        flex-direction: column;
    }

    .step {
        flex-direction: column;
    }

    .step-number {
        margin-bottom: 15px;
    }

    .step:not(:last-child):after {
        display: none;
    }

    .contact-container {
        flex-direction: column;
    }
}

/* JavaScript交互相关样式 */
.faq-item.active .faq-answer {
    padding: 20px;
    max-height: 500px;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    color: #333;
    /* overflow-x: hidden; */
    /* 移除或注释掉，让 user-guide-container 控制滚动 */
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    width: 150px;
    height: 60px;
}

.scrolled {
    background-color: rgba(200, 200, 200, 0.3);
    /* 滚动后不透明 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 添加菜单区域背景颜色 */
.sc-73348620-5 {
    background-image: url('../images/about_bg_header.jpg');
    background-size: cover;
    /* 覆盖整个区域 */
    background-position: top;
    /* 居中显示 */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    /* 添加阴影效果 */
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* 为下拉菜单项添加背景色 */
.sc-28b433d-0 {
    background-color: rgba(255, 255, 255, 0.98) !important;
    /* 更强的白色背景 */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    /* 更明显的阴影 */
}

/* 菜单项悬停效果 */
.sc-b6ff7819-2:hover {
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 4px;
}

.nav-buttons {
    display: flex;
    align-items: center;
}


.btn-secondary:hover {
    background-color: #007bff;
    color: #fff;
}

/* 下拉菜单样式 */
.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: rgba(255, 255, 255, 0.8);
    /* 白半透明 */
    list-style-type: none;
    margin: 0;
    min-width: 600px;
    /* Adjust width as needed */
    columns: 2;
    /* Split into 2 columns */
    column-gap: 20px;
    /* Space between columns */
}

.dropdown-content li {
    break-inside: avoid-column;
    /* Prevent items from breaking across columns */
}

.dropdown-content li a {
    width: 100%;
    color: black;
    text-decoration: none;
    font-weight: normal;

    display: flex;
    align-items: center;
}

.dropdown-content li a:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dropdown:hover .dropdown-content {
    display: block;
}

/* 移除下拉菜单项的下划线 */
.dropdown-content a::after {
    display: none;
}


*/

/* 为下拉菜单的主链接添加一个小箭头 */
.dropdown>a::after {
    content: '';
    font-size: 10px;
    margin-left: 5px;
    vertical-align: middle;
}

.nav-buttons .login-btn {
    background-color: transparent;
    color: #007bff;
    border: 1px solid #007bff;
    padding: 0 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 550;
    text-decoration: none;
    height: 36px;
    line-height: 36px;
}

.nav-buttons .login-btn:hover {
    background-color: #007bff;
    color: #fff;
}





nav .left-links {
    display: flex;
    align-items: center;
}






.right-links {
    display: flex;
    align-items: center;
}

.right-links button {
    background-color: #2981e5;
    color: white;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    border-radius: 5px;
    margin-left: 10px;
}

nav .right-links .register {
    margin-right: 50px;
}

/* 每屏的通用样式 */
.screen {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    box-sizing: border-box;
    position: relative;
}

/* 每屏的通用样式 */
.screen_half {
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    box-sizing: border-box;
    position: relative;
}

/* 每屏的通用样式 */
.screen_one_third {
    height: 620px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-sizing: border-box;
    position: relative;
}

/* 第一屏：新小切 */
#screen1 {
    background-image: url('images/background.png');
    background-size: cover;
    background-position: center;
    color: white;
    animation: moveBackground 15s linear infinite;
    /* 背景滚动动画 */
}

@keyframes moveBackground {
    from {
        background-position: 0 0;
    }

    to {
        background-position: 100% 0;
        /* 水平滚动 */
    }
}


#screen1 .left-content {
    position: absolute;
    left: 1%;
    top: 30%;
    max-width: 50%;
    padding-top: 50px;
    padding-left: 10%;
}

#screen1 h1 {
    font-size: 50px;
    margin-bottom: 50px;
    color: #333;
    text-align: left;
}

#screen1 p {
    font-size: 1.2rem;
    margin-bottom: 50px;
    color: #333;
    text-align: left;
}

#screen1 button {
    background-color: #2981e5;
    color: white;
    border: none;
    padding: 10px 20px;
    margin-right: 50px;
    cursor: pointer;
    font-size: 16px;
    border-radius: 5px;
    float: left;
}

/* 右侧圆角矩形区域 */
#screen1 .animation-area {
    position: absolute;
    right: 180px;
    top: 65%;
    transform: translateY(-50%);
    width: 650px;
    height: 780px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    border: 15px solid #eeeeee;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 10px 4px 8px rgba(0, 0, 0, 0.2);
    animation: fadeIn 2s ease-in-out;
}

/* 视频样式 */
#screen1 .animation-area video {
    width: 100%;
    height: 100%;
    box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0);
    object-fit: cover;
    /*保持视频比例 */
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-50%) translateX(50px);
    }

    to {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}

/* 第二屏：体验互动的力量 */
#screen2 {
    background-color: #f8f9fa;

}

#screen2 h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

#screen2 .features {
    display: flex;
    justify-content: space-around;
    margin-top: 30px;
}

#screen2 .feature {
    width: 30%;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

#screen2 .feature h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

#screen2 .feature p {
    font-size: 1rem;
}

/* 第三屏：The Legislative Journey */
#screen3_a {
    background-color: #ffffff;
}

#screen3_b {
    background-color: #ffffff;
}


/* 媒体查询：适配手机端 */
@media (max-width: 768px) {
    nav a {
        margin: 0 5px;
        /* 手机端链接间距减小 */
        font-size: 12px;
        /* 手机端字体更小 */
    }

    nav .right-links button {
        padding: 4px 8px;
        /* 手机端按钮更小 */
        font-size: 10px;
    }

    #screen1 .left-content {
        max-width: 100%;
        /* 手机端占满宽度 */
        padding-left: 10px;
        /* 手机端左侧留白减小 */
    }

    #screen1 h1 {
        font-size: 2rem;
        /* 手机端标题更小 */
    }

    #screen1 p {
        font-size: 0.9rem;
        /* 手机端文字更小 */
    }

    #screen1 .animation-area {
        width: 150px;
        /* 手机端宽度更小 */
        height: 100px;
        /* 手机端高度更小 */
    }
}

a:hover {
    text-decoration: none;
}

.about-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.about-title {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}

.about-title h2 {
    font-size: 36px;
    color: #2981e5;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.about-title h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #007bff);
}

.about-section {
    margin-bottom: 50px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    transition: all 0.3s ease;
}

.about-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.about-section h3 {
    font-size: 24px;
    color: #007bff;
    margin-bottom: 20px;
    font-style: normal !important;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.about-section h3:before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 3px;
    background: #007bff;
    transition: all 0.3s ease;
    /* 添加过渡效果 */
}

.about-section h3:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 60px;
    right: 0;
    height: 1px;
    background: #007bff;
    transition: all 0.3s ease;
    /* 添加过渡效果 */
}

/* 修改悬停效果，保持边框不变 */
.about-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 确保悬停时边框不变粗 */
.about-section:hover h3:before,
.about-section:hover h3:after {
    height: 3px;
    /* 保持 before 元素高度不变 */
}

.about-section:hover h3:after {
    height: 1px;
    /* 保持 after 元素高度不变 */
}

.about-section p {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
    margin-bottom: 20px;
}

.about-section ul {
    padding-left: 20px;
}

.about-section ul li {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
    margin-bottom: 10px;
    position: relative;
    list-style-type: none;
    padding-left: 25px;
}

.about-section ul li:before {
    content: '✓';
    color: #007bff;
    position: absolute;
    left: 0;
    font-weight: bold;
}

.product-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.product-card {
    flex: 1 1 calc(50% - 10px);
    min-width: 300px;
    background: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #007bff;
}

.product-card strong {
    display: block;
    font-size: 18px;
    color: #007bff;
    margin-bottom: 10px;
}

.vision-box {
    background: linear-gradient(135deg, rgba(56, 168, 111, 0.05), rgba(47, 136, 148, 0.05));
    border-radius: 10px;
    padding: 30px;
    text-align: center;
}

.vision-box p {
    font-size: 18px;
    line-height: 1.8;
    color: #444;
}

@media (max-width: 768px) {
    .product-card {
        flex: 1 1 100%;
    }

    .about-section {
        padding: 20px;
    }
}

/* 确保最后一个部分有足够的底部边距 */
#updates {
    margin-bottom: 10px; /* 增加到100px确保有足够空间 */
}