<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    <title>版慎通-智能审校</title> 
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            display: flex;
            min-height: 100vh;
            overflow-x: hidden;
        }
        .left-panel {
            width: 30%;
            background: linear-gradient(to bottom,  #582e8d, #419bad 100%);
            color: white;
            padding: 40px;
            padding-top: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            height: auto;
            min-height: 100vh;
            box-sizing: border-box;
        }
        .left-panel h2 {
            margin-top: 0;
        }
        .right-panel {
            width: 70%;
            padding: 20px;
            padding-top: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            min-height: 100vh;
            box-sizing: border-box;
        }
        .right-panel h2 {
            margin-top: 0;
        }
        .testimonial {
            margin-bottom: 30px;
        }
        .testimonial img {
            width: 40px;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
        }
        .form-container {
            max-width: 450px;
            width: 100%;
            font-size: 14px;
        }
        .form-container input, .form-container select, .form-container textarea {
            width: 100%;
            padding: 6px;
            margin: 2px 0 6px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-field {
            text-align: left;
            margin-bottom: 3px;
            width: 100%;
        }
        .form-field span {
            vertical-align: middle;
            display: inline-block;
        }
        .form-field label {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
        }
        .hs-form-required {
            color: red;
            margin-left: 3px;
            font-size: 16px;
        }
        .form-container button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px;
            width: 100%;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            font-size: 12px;
            color: #666;
            position: absolute;
            bottom: 1px;
            /* top: 50px; */
            /* left: 15px; */
        }
        .hidden-field {
            display: none;
        }
        .char-counter {
            font-size: 12px;
            color: #666;
            text-align: right;
            margin-top: -6px;
            margin-bottom: 1px;
            height: 12px; /* 与 field-spacing 保持一致 */
        }
        /* 添加一个统一的间距控制类 */
        .field-spacing {
            height: 12px;
            margin-bottom: 4px;
        }
        .legal-consent-container {
            margin: 1px 0;
            font-size: 12px;
            color: #666;
            text-align: left;
            line-height: 1.1;
        }
        
        .legal-consent-container a {
            color: #007bff;
            text-decoration: none;
        }
        
        .legal-consent-container a:hover {
            text-decoration: underline;
        }
    </style>
    <script>
        // 从服务器获取令牌
        function getTokenFromServer() {
            return fetch('/api/get-token')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取信息失败');
                    }
                    return response.json().then(data => {
                        return data.token;
                    });
                })
                .catch(error => {
                    console.error('获取信息失败:', error);
                });
        }
        
        // 表单提交处理
        function handleSubmit(event) {
            event.preventDefault();
            
            if (document.getElementById('website').value !== '') {
                return false;
            }
            
            // 验证时间戳
            const now = new Date().getTime();
            const formTime = parseInt(document.getElementById('timestamp').value);
            if (now - formTime < 3000) { // 至少3秒填写时间
                alert('请不要过快提交表单');
                return false;
            }
            
            // 验证表单字段
            const requiredFields = document.querySelectorAll('.required-field');
            let isValid = true;
            let hasEmptyFields = false;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    hasEmptyFields = true;
                }
            });
            
            // 验证电子邮箱格式
            const emailField = document.getElementById('email');
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (emailField.value && !emailRegex.test(emailField.value)) {
                isValid = false;
                alert('请输入有效的电子邮箱格式');
                return false;
            }
            
            // 验证手机号码格式
            const phoneField = document.getElementById('phone');
            const phoneRegex = /^1\d{10}$/;
            if (phoneField.value && !phoneRegex.test(phoneField.value)) {
                isValid = false;
                alert('请输入有效的手机号码（1开头的11位数字）');
                return false;
            }
            
            if (hasEmptyFields) {
                alert('请填写必要信息');
                return false;
            }
            
            if (!isValid) {
                return false;
            }
            
            // 提交表单
            const formData = new FormData(document.getElementById('info-form'));
            
            // 使用fetch API发送POST请求到指定端点
            fetch('/api/send-email', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    alert('表单提交成功，感谢您的申请！');
                } else {
                    alert('表单提交失败，请稍后再试或通过邮件发送申请信息');
                }
                document.getElementById('info-form').reset();
            })
            .catch(error => {
                console.error('提交出错:', error);
                alert('表单提交异常，请通过邮件发送申请信息');
                document.getElementById('info-form').reset();
            });
            
            // 重新生成令牌和时间戳
            getTokenFromServer().then(token => {
                document.getElementById('csrf-token').value = token.token;
                document.getElementById('timestamp').value = new Date().getTime();
            });
            
            return false;
        }
        
        // 字符计数器函数
        function updateCharCounter(inputId, maxLength) {
            const input = document.getElementById(inputId);
            const counter = document.getElementById(inputId + '-counter');
            
            if (!input || !counter) return; // 如果元素不存在就直接返回
            
            const currentLength = input.value.length;
            counter.textContent = currentLength + '/' + maxLength;
            
            if (currentLength > maxLength) {
                counter.style.color = 'red';
                input.style.borderColor = 'red';
            } else {
                counter.style.color = '#666';
                input.style.borderColor = '#ccc';
            }
        }
        
        // 页面加载时初始化
        window.onload = function() {
            getTokenFromServer().then(token => {
                document.getElementById('csrf-token').value = token;
                document.getElementById('timestamp').value = new Date().getTime();
            });
            
            // 添加表单提交事件
            document.getElementById('info-form').addEventListener('submit', handleSubmit);
            
            // 添加字符限制和计数器
            const nameField = document.getElementById('name');
            nameField.setAttribute('maxlength', '10');
            nameField.addEventListener('input', function() {
                updateCharCounter('name', 10);
            });
            
            const companyField = document.getElementById('company');
            companyField.setAttribute('maxlength', '20');
            companyField.addEventListener('input', function() {
                updateCharCounter('company', 20);
            });
            
            const positionField = document.getElementById('position');
            positionField.setAttribute('maxlength', '10');
            positionField.addEventListener('input', function() {
                updateCharCounter('position', 10);
            });
            
            const purposeField = document.getElementById('purpose');
            purposeField.setAttribute('maxlength', '100');
            purposeField.addEventListener('input', function() {
                updateCharCounter('purpose', 100);
            });
            
            // 初始化计数器
            updateCharCounter('name', 10);
            updateCharCounter('company', 20);
            updateCharCounter('position', 10);
            updateCharCounter('purpose', 100);
        }
    </script>
</head>
<body>
    <div class="left-panel">
        <div>
            <h2>体验智能校对系统的全部功能</h2>
            <p>智能校对系统帮助我节省了大量工作时间...</p>
        </div>
    </div>
    <div class="right-panel">
        <h2>申请智能校对系统试用权限</h2>
        <div class="form-container">
            <form id="info-form" method="post">
                <!-- 隐藏字段用于安全 -->
                <input type="hidden" id="csrf-token" name="csrf_token">
                <input type="hidden" id="timestamp" name="timestamp">
                <div class="hidden-field">
                    <input type="text" id="website" name="website">
                </div>
                
                <div class="form-field">
                    <label>
                        <span>姓名</span>
                        <span class="hs-form-required" style="position:relative; top:3px; line-height:0;">*</span>
                    </label>
                    <input type="text" id="name" name="name" class="required-field" maxlength="10">
                    <div id="name-counter" class="char-counter">0/10</div>
                </div>
                <div class="form-field">
                    <label>
                        <span>电子邮箱</span>
                        <span class="hs-form-required" style="position:relative; top:3px; line-height:0;">*</span>
                    </label>
                    <input type="email" id="email" name="email" class="required-field">
                    <div class="field-spacing"></div>
                </div>
                <div class="form-field">
                    <label>
                        <span>手机号码</span>
                        <span class="hs-form-required" style="position:relative; top:3px; line-height:0;">*</span>
                    </label>
                    <input type="text" id="phone" name="phone" class="required-field" maxlength="11">
                    <div class="field-spacing"></div>
                </div>
                <div class="form-field">
                    <label>
                        <span>公司名称</span>
                        <span class="hs-form-required" style="position:relative; top:3px; line-height:0;">*</span>
                    </label>
                    <input type="text" id="company" name="company" class="required-field" maxlength="20">
                    <div id="company-counter" class="char-counter">0/20</div>
                </div>
                <div class="form-field">
                    <label>
                        <span>行业</span>
                        <span class="hs-form-required" style="position:relative; top:3px; line-height:0;">*</span>
                    </label>
                    <select id="industry" name="industry" class="required-field">
                        <option value="">- 请选择 -</option>
                        <option>互联网/IT/软件</option>
                        <option>教育/培训</option>
                        <option>金融/银行/保险</option>
                        <option>媒体/出版/文化</option>
                        <option>医疗/健康</option>
                        <option>制造业</option>
                        <option>零售/电商</option>
                        <option>房地产/建筑</option>
                        <option>政府/公共服务</option>
                        <option>咨询/法律/专业服务</option>
                        <option>旅游/酒店</option>
                        <option>交通/物流</option>
                        <option>能源/环保</option>
                        <option>农业/食品</option>
                        <option>其他</option>
                    </select>
                    <div class="field-spacing"></div>
                </div>
                <div class="form-field">
                    <label>
                        <span>公司规模</span>
                        <span class="hs-form-required" style="position:relative; top:3px; line-height:0;">*</span>
                    </label>
                    <select id="company-size" name="company_size" class="required-field">
                        <option value="">- 请选择 -</option>
                        <option>1-10人</option>
                        <option>11-50人</option>
                        <option>51-100人</option>
                        <option>101-200人</option>
                        <option>201-500人</option>
                        <option>501-1000人</option>
                        <option>1001-2000人</option>
                        <option>2000人以上</option>
                    </select>
                    <div class="field-spacing"></div>
                </div>
                <div class="form-field">
                    <label>
                        <span>职位</span>
                        <span class="hs-form-required" style="position:relative; top:3px; line-height:0;">*</span>
                    </label>
                    <input type="text" id="position" name="position" class="required-field" maxlength="10">
                    <div id="position-counter" class="char-counter">0/10</div>
                </div>
                <div class="form-field">
                    <label>
                        <span>您想用 智能审校 做什么？</span>
                    </label>
                    <textarea id="purpose" name="purpose" maxlength="100"></textarea>
                    <div id="purpose-counter" class="char-counter">0/100</div>
                </div>
                
                <div class="legal-consent-container">
                    <div class="hs-richtext">
                        <p>提交您的联系信息，即表示您同意接收来自智能校对系统的产品信息通讯。您可以随时撤回您的同意。请参阅我们的<strong><a href="privacy.html">隐私政策</a></strong>或通过*************************与我们联系。</p>
                    </div>
                </div>
                
                <button type="submit">申请试用</button>
            </form>
        </div>
        <div class="footer">
            <p style="letter-spacing: 1px;"><img src="images/batb.png" width="16" height="16" style="vertical-align:text-bottom; margin-right: 5px;">
                <a href="https://beian.mps.gov.cn/#/query/webSearch?code=36012202000553" rel="noreferrer" target="_blank" style="letter-spacing: 1px; font-size: 12px; color: inherit; text-decoration: none;">赣公网安备36012202000553号</a>
                <a href="https://beian.miit.gov.cn/#/Integrated/index" rel="noreferrer" target="_blank" style="letter-spacing: 1px; font-size: 12px; color: inherit; text-decoration: none;">赣ICP备2025056979号-2</a>    
                江西朗知中文数智科技有限公司©2011-2025</p>
        </div>
    </div>
</body>
</html>
