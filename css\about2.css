@charset "UTF-8";
ol,
ul {
  list-style: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.p-0 {
  padding: 0 !important;
}
.m-0 {
  margin: 0 !important;
}
.d-flex {
  display: flex;
}
.f-wrap {
  flex-wrap: wrap;
}
@media only screen and (max-width: 480px) {
  .hidden-phone {
    display: none;
  }
}
.g-header {
  padding: 120px 0 100px;
  background-image: url(../images/bg_header.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  text-align: center;
}
.g-header__title {
  font-weight: 300;
  font-size: 49px;
  line-height: 60px;
  max-width: 840px;
  margin: 0 auto;
}
@media only screen and (max-width: 480px) {
  .g-header__title {
    font-size: 42px;
    line-height: 48px;
  }
}
.g-header__description {
  font-size: 15px;
  font-weight: 500;
  line-height: 25px;
  max-width: 460px;
  margin: 20px auto 15px;
}
.g-header__embed {
  max-width: 700px;
  min-height: 470px;
  margin: 35px auto;
}
@media only screen and (max-width: 480px) {
  .g-header__embed {
    min-height: 280px;
  }
}
.g-header__embed .infogram-embed {
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.33);
  border-radius: 4px;
  background: rgba(71, 59, 107, 0.5);
}
.g-button {
  cursor: pointer;
  font-family: inherit;
  box-sizing: border-box;
  font-size: 15px;
  font-weight: 500;
  background-color: #3195cb;
  border-radius: 4px;
  width: 220px;
  color: #fff;
  height: 40px;
  line-height: 40px;
  transition: 0.25s all cubic-bezier(0.165, 0.84, 0.44, 1);
}
.g-button:active,
.g-button:focus,
.g-button:hover {
  color: #fff;
  text-decoration: none;
  background-color: #58aad7;
}
.g-button--accent {
  background-color: #ce2533;
}
.g-button--accent:active,
.g-button--accent:focus,
.g-button--accent:hover {
  background-color: #de4854;
  color: #fff;
}
.g-button--large {
  width: 300px;
  height: 50px;
  line-height: 47px;
  font-size: 17px;
  box-sizing: border-box;
  padding: 0 20px;
}
@media screen and (max-width: 360px) {
  .g-button--large {
    width: 100%;
    font-size: 15px;
  }
}
.g-button--transparent {
  border: 1px solid #fff;
  background-color: transparent;
}
.g-button--transparent:active,
.g-button--transparent:focus,
.g-button--transparent:hover {
  background-color: #fff;
  color: #656565;
}
.g-button--gray {
  border: 2px solid #656565;
  color: #656565;
  background-color: transparent;
}
.g-button--gray:active,
.g-button--gray:focus,
.g-button--gray:hover {
  color: #fff;
  background-color: #656565;
}
.g-button--centered {
  display: block;
  margin: 0 auto;
}
.g-link {
  color: #3796c9;
}
.g-link:hover {
  color: #3896c9;
}
.g-paragraph {
  font-weight: 500;
  color: #656565;
  font-size: 15px;
  line-height: 25px;
}
.fade-in {
  opacity: 0;
  -webkit-animation: fadeIn ease-in 1;
  -moz-animation: fadeIn ease-in 1;
  animation: fadeIn ease-in 1;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 0.6s;
  -moz-animation-duration: 0.6s;
  animation-duration: 0.6s;
}
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-moz-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
img {
  max-width: 100%;
}
a {
  color: #3195cb;
}
a:hover {
  color: #58aad7;
}
.frontpage .section {
  padding: 50px 0;
}
.frontpage .section .container {
  max-width: 1000px;
  box-sizing: border-box;
  margin: 0 auto;
}
@media only screen and (max-width: 1199px) {
  .frontpage .section .container {
    padding: 0 20px;
  }
}
.frontpage .section--gray {
  background-color: #ebebee;
}
.frontpage .section--dark {
  color: #fff;
}
.frontpage .section--light {
  color: #656565;
}
.frontpage .section__paragraph {
  font-size: 15px;
  line-height: 25px;
  color: inherit;
  font-weight: 400;
}
.frontpage .section__title {
  color: inherit;
  font-size: 29px;
  font-weight: 300;
  line-height: 34px;
}
.frontpage .section__description {
  color: inherit;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  padding-top: 0;
  margin-top: 20px;
  margin-bottom: 15px;
}
html {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}
body {
  font-family: Roboto, sans-serif;
  font-size: 100%;
  line-height: 120%;
  font-weight: 500;
  color: #434343;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility !important;
  position: relative;
}
span {
  display: inline-block;
}
a {
  display: inline-block;
  color: #2c78a0;
  text-decoration: none;
}
a:focus,
a:hover {
  color: #146088;
  text-decoration: underline;
}
a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.clear {
  clear: both;
}
.absolute {
  position: absolute;
}
.body {
  position: relative;
  margin: 0 auto;
}
.body_inner {
  position: relative;
  top: 0;
  z-index: 1;
}
.head {
  background: #b1d6b1 url(../images/gradient.png) repeat-x;
  text-align: center;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-height: 685px !important;
  transition: top 0.33s cubic-bezier(0.694, 0.0482, 0.335, 1) 0s;
  top: 0;
}
.frontpage .head {
  min-height: 565px !important;
}
.head h1 {
  color: #434343;
  font-weight: 300;
  padding: 190px 25px 6px;
  position: relative;
  font-size: 41px;
  line-height: 60px;
}
.frontpage .head .ig_counter,
.frontpage .head h1,
.frontpage .head h2 {
  color: #fff;
}
.frontpage .head h1 {
  padding-top: 120px;
}
.frontpage .head .ig_counter {
  padding: 0 0 20px;
}
.head_height {
  position: relative;
  z-index: -3;
  display: none;
}
.logo {
  width: 146px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: auto;
  position: relative;
  top: 7px;
}
.menu_ico {
  position: absolute;
  top: 23px;
  right: 20px;
  z-index: 1;
  width: 37px;
  height: 37px;
  background-image: url("../images/img_sprite.png");
  background-position: -104px -151px;
  cursor: pointer;
}
.h_img {
  position: relative;
  width: 92%;
  margin: 0 4%;
}
.h_img img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: auto;
}
.h_img.n2 {
  position: absolute;
  left: 50%;
  height: 590px;
}
.h_img.n2 img {
  margin-left: -54%;
}
.h_link {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  background-image: url("../images/img_sprite.png");
  background-position: -262px -272px;
  cursor: pointer;
}
.n2 .h_link {
  left: -4%;
}
.mobile_menu {
  position: fixed;
  top: 0;
  right: 0;
  width: 272px;
  height: 100%;
  z-index: 999999;
  -webkit-overflow-scrolling: touch !important;
  overflow: hidden;
  background-color: #434343;
  margin-right: -272px;
  transition-property: margin-right, padding-top;
  transition-duration: 0.3s;
  transition-timing-function: ease-in-out;
}
.mobile_menu.active {
  margin-right: 0;
}
.mobile_menu__close-wrapper {
  pointer-events: none;
  box-sizing: border-box;
  top: 0;
  right: 0;
  background: #434343;
  position: absolute;
  z-index: 10;
  height: 80px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 21px;
}
.mobile_menu__close-wrapper.sticky {
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}
.mobile_menu__close {
  pointer-events: all;
  cursor: pointer;
  width: 37px;
  height: 37px;
  background-size: 40%;
  background-image: url("../images/close.svg");
  background-repeat: no-repeat;
  background-position: center;
}
.mobile_menu .btn-holder {
  margin-top: 25px;
  padding: 0 33px;
}
.mobile_menu .btn-holder .btn {
  width: 100%;
  max-width: 145px;
  margin-bottom: 10px;
  border-radius: 4px;
  display: inline-block;
  text-decoration: none;
  text-align: center;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.1s ease-out;
}
.mobile_menu .btn-holder .btn.btn-get-started {
  color: #444;
  background-color: #fff;
  font-size: 15px;
  padding: 11px 32px 11px;
  opacity: 0.7;
}
.mobile_menu .btn-holder .btn.btn-login {
  padding: 9px 30px 9px;
  font-size: 15px;
  opacity: 0.7;
  color: #fff;
}
.mobile_menu .btn-holder .btn.btn-transparent {
  border: 2px solid #fff;
}
.mobile_menu .btn-holder .btn:focus,
.mobile_menu .btn-holder .btn:hover {
  opacity: 1;
}
.mm_scroll {
  width: 320px;
  height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
}
.mm_inner {
  width: 275px;
  position: relative;
}
.mm_a {
  display: block;
  font-size: 17px;
  line-height: 17px;
  color: #3796c9;
  padding: 16px 0 17px 17px;
}
.mm_spacer {
  height: 50px;
}
.m_txt {
  font-size: 17px;
  line-height: 17px;
  color: #767676;
  margin: 17px 0 0 67px;
}
.touch a.active:hover .m_txt,
a.active .m_txt,
a:hover .m_txt {
  color: #fff;
}
.touch a:hover .m_txt {
  color: #767676;
}
.m_border {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 100%;
  background-color: #3796c9;
  transition: width 0.2s ease-out;
}
.touch a.active:hover .m_border,
a.active .m_border,
a:hover .m_border {
  width: 5px;
}
.touch a:hover .m_border {
  width: 0;
}

.languages-line {
  margin-top: 14px;
  text-align: center;
}
.languages-line a {
  font-size: 13px;
  color: #757575;
  margin: 0 10px;
}
.mn_wrap {
  position: relative;
  z-index: 1;
  background-color: #fff;
  overflow: hidden;
}
.a_wrap {
  width: 25%;
  float: left;
  cursor: pointer;
}
.a_wrap a {
  display: block;
  position: relative;
  height: 50px;
  border-left: 2px solid #e1e1e1;
  margin-top: 20px;
  text-decoration: none;
}
.a_wrap:first-child a {
  border-left: 0;
}
.a_wrap.active .a_hover,
.a_wrap:hover .a_hover {
  height: 5px;
}
.a_txt {
  display: none;
  font-size: 17px;
  line-height: 19px;
  color: #434343;
  text-align: center;
}
.a_ico {
  position: absolute;
  top: 13px;
  left: 50%;
  margin-left: -20px;
  width: 39px;
  height: 25px;
  background-image: url("../images/img_sprite.png");
}
.a_ico.n1 {
  background-position: -104px 0;
}
.a_ico.n2 {
  background-position: -104px -25px;
}
.a_ico.n3 {
  background-position: -104px -52px;
}
.a_ico.n4 {
  background-position: -104px -106px;
}
.a_hover {
  position: absolute;
  top: -20px;
  left: -2px;
  right: -2px;
  height: 0;
  background-color: #3796c9;
  transition: height 0.2s ease-out;
}
.slider {
  position: relative;
  z-index: 1;
  text-align: center;
  background-color: #fff;
  padding-top: 30px;
}
.block,
.slide {
  position: relative;
  width: 100%;
}
.block h2,
.slide h2 {
  font-size: 37px;
  line-height: 1.2;
  font-weight: 100;
  padding: 0 25px;
  margin-bottom: 13px;
}
.block h2 br,
.slide h2 br {
  display: none;
}
.block p,
.slide p {
  font-size: 15px;
  line-height: 30px;
  padding: 0 25px;
}
.block img,
.slide img {
  width: 334px;
  height: auto;
}
.block .c_request,
.slide .c_request {
  margin: 21px 0 6px;
}
@media only screen and (min-width: 410px) {
  .block h2,
  .slide h2 {
    width: 70%;
    margin: 0 auto 13px;
  }
  .block p,
  .slide p {
    width: 70%;
    margin: 0 auto;
  }
}
@media only screen and (min-width: 540px) {
  .block img,
  .slide img {
    width: 513px;
  }
  .head {
    min-height: 645px !important;
  }
}
.s_nav {
  text-align: center;
  cursor: default;
  padding: 10px 0;
}
.s_nav span {
  width: 16px;
  height: 16px;
  background-image: url("../images/img_sprite.png");
  background-position: -104px -133px;
  cursor: pointer;
}
.s_nav span.swiper-active-switch,
.s_nav span:hover {
  background-position: -122px -133px;
}
.f_wrap {
  position: relative;
  z-index: 1;
  background-color: #fff;
  overflow: hidden;
}
.featured {
  max-width: 1012px;
  margin: 0 auto;
  text-align: center;
}
.featured h2 {
  font-size: 37px;
  line-height: 55px;
  font-weight: 100;
  margin: 20px 0 17px;
  padding: 0 20px;
}
.featured h3 {
  font-size: 15px;
  line-height: 17px;
  font-weight: 500;
  margin-bottom: 35px;
  padding: 0 20px;
}
.community {
  position: relative;
  height: 490px;
  overflow: hidden;
}
.community img {
  position: relative;
  top: -63px;
  left: 50%;
  z-index: 0;
  width: 1041px;
  height: auto;
  margin-left: -520px;
}
.c_btns {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  text-align: center;
  padding-top: 25px;
}
.c_btns a {
  position: relative;
}
.c_btns .btn_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  opacity: 0.9;
}
.sec_header {
  font-size: 17px;
  color: #656565;
  padding-bottom: 20px;
}
.socialicons {
  position: relative;
  text-align: center;
  margin: 0 auto 20px;
  line-height: 0;
}
.socialicons > iframe + div {
  position: relative;
  left: 6px;
}
#___plusone_0 > iframe {
  margin-top: -4px !important;
}
.c_request {
  min-width: 274px;
  height: 46px;
  border: 2px solid #b7b7b7;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  margin: 0 0 25px;
}
.c_request > span {
  font-size: 15px;
  line-height: 17px;
  color: #434343;
  margin-top: 14px;
}
.c_request:hover {
  border-color: #b7b7b7;
  background: #b7b7b7;
}
.c_request:hover > span {
  color: #fff;
}
.c_request {
  position: relative;
  border-color: #b7b7b7;
  background: 0 0;
}
.c_request img {
  position: absolute;
  left: 17px;
  top: 13px;
}
.c_request span {
  margin-left: 22px;
}
@media only screen and (min-width: 410px) {
  .quote p {
    width: 80%;
    margin: 0 auto;
  }
  .c_request {
    min-width: 286px;
    margin: 0 12px 25px;
  }
}
@media only screen and (min-width: 520px) {
  .qa_txt br {
    display: none;
  }
}
@media only screen and (min-width: 540px) {
  .h_img {
    width: 80%;
    margin: 0 10%;
  }
  .h_img.n2 img {
    margin-left: -62.5%;
  }
  .n2 .h_link {
    left: -12.5%;
  }
}
@media only screen and (min-width: 599px) {
  .head_height {
    display: block;
  }
  .top_line {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 80px;
    background: rgba(107, 170, 151, 0.87);
    z-index: 9999;
  }
  .support {
    display: none;
  }
  .user {
    position: relative;
    top: auto;
    right: auto;
    left: auto;
    float: right;
    margin: 20px 18px 0 40px;
    width: auto;
    min-width: 107px;
  }
  .head h1 {
    padding: 139px 25px 20px;
  }
  .frontpage .head h1 {
    padding-top: 119px;
  }
  .charts h2,
  .featured h2 {
    font-size: 41px;
  }
  .s_nav {
    padding: 10px 0 54px;
  }
}
@media only screen and (min-width: 700px) {
  .head h1 br {
    display: none;
  }
  .h_img {
    width: 76%;
    margin: 0 12%;
  }
  .h_img.n2 img {
    margin-left: -65.9%;
  }
  .h_link {
    top: 42%;
  }
  .n2 .h_link {
    left: -15.8%;
  }
  .charts br.br_410 {
    display: none;
  }
  .charts br.br_700 {
    display: block;
  }
}
@media only screen and (min-width: 839px) {
  .a_txt {
    display: block;
    padding: 14px 0 0 20%;
  }
  .a_ico {
    left: 17%;
  }
  .a_ico.n3 {
    background-position: -104px -79px;
  }
}
@media only screen and (min-width: 1230px) {
  .menu_ico {
    display: none;
  }
  .visible {
    display: block;
  }
  .h_img {
    width: 72%;
    margin: 0 14%;
  }
  .h_img.n2 img {
    margin-left: -69.7%;
  }
  .n2 .h_link {
    left: -19.5%;
  }
  .community.fixed_img img {
    position: fixed;
    top: 90px;
  }
}
@media only screen and (min-width: 1120px) {
  .community img {
    width: 100%;
    margin-left: -50%;
  }
  .slider {
    width: 100%;
    margin: 0 auto;
    position: relative;
    text-align: right;
    padding-top: 10px;
  }
  .slide_content {
    position: relative;
    width: 1020px;
    margin: 0 auto;
  }
  .slider_txt {
    position: absolute;
    top: 38px;
    left: 0;
    width: 380px;
    text-align: left;
  }
  .slide.n1 img {
    margin-right: -20px;
  }
  .nav_left {
    left: -83px;
  }
  .nav_right {
    right: -83px;
  }
  .block h2,
  .slide h2 {
    padding: 0;
    width: 100%;
    margin-left: -3px;
  }
  .block h2 br,
  .slide h2 br {
    display: block;
  }
  .block p,
  .slide p {
    padding: 0;
    width: 100%;
  }
  .block {
    overflow: hidden;
    padding: 140px 0;
  }
  .block .content {
    position: relative;
    width: 1020px;
    margin: 0 auto;
  }
  .block .text {
    position: absolute;
    left: 0;
    width: 500px;
    text-align: left;
  }
  .block h2 {
    width: 380px;
  }
  .block img {
    float: right;
    margin: 0;
  }
  .block:first-child {
    padding: 90px 0 160px;
  }
  .block.dark {
    padding: 90px 0 175px;
  }
  .block.dark .text {
    top: 68px;
    left: auto;
    right: 0;
    width: 380px;
  }
  .block.dark img {
    float: left;
  }
  .s_nav {
    padding: 57px 0 54px;
  }
}
@media only screen and (min-width: 1291px) {
  .h_img {
    width: 1020px;
    margin: 0 auto;
  }
  .h_img.n2 img {
    margin-left: -50%;
  }
  .n2 .h_link {
    left: 0;
  }
}
.award,
.charts,
.com_block,
.community,
.f_wrap,
.footer,
.mn_wrap,
.mobile_menu,
.quote_wrap,
.slider,
.steps_wrap,
.top_line {
  -webkit-transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;
}
@media only screen and (min-width: 990px) {
  .top_line {
    transform: translateY(0);
    -webkit-webkit-transform: translateY(0);
  }
  .top_line.hidden {
    transform: translateY(-84px);
    -webkit-transform: translateY(-84px);
  }
}
.pushable,
.top_line {
  transition-property: transform, top, margin-left;
  transition-timing-function: ease-in-out;
  transition-duration: 0.25s;
}
.ress {
  position: fixed;
  top: 5px;
  left: 10px;
  z-index: 9999;
  font-size: 15px;
  line-height: 17px;
  color: #000;
  background-color: #fff;
  padding: 3px 5px;
}
#verticals {
  position: relative;
  z-index: 1;
  text-align: center;
  -webkit-transform: translateZ(0);
}
#verticals .quote_wrap {
  padding-top: 25px;
}
.block {
  background: #fff;
  padding: 30px 0;
}
.block .content {
  max-width: 1020px;
  margin: 0 auto;
}
.block .content img {
  height: auto;
  width: 70%;
}
.block:first-child {
  padding: 0 0 35px;
}
.block.dark {
  background: #434343;
  padding: 30px 0;
}
.block.dark h2,
.block.dark p {
  color: #fff;
}
.block.dark .c_request {
  border-color: #fff;
}
.block.dark .c_request span {
  color: #fff;
}
.block.dark .c_request:hover {
  background-color: #fff;
}
.block.dark .c_request:hover span {
  color: #434343;
}
.help-block {
  background: #e3e3e3;
  padding: 30px 0;
}
.help-block p {
  font: 500 15px/30px Roboto;
  margin-top: 18px;
  padding: 0 20px;
}
.help-block .buttons {
  margin-top: 32px;
}
.custom-designs-block {
  background: #fff;
  padding-bottom: 40px;
  text-align: center;
}
.h_brands .custom-designs-block,
.h_org .custom-designs-block {
  padding: 47px 0 47px;
}
.custom-designs-block .content {
  max-width: 1020px;
  margin: 0 auto;
  padding: 35px 0 45px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
}
.custom-designs-block .contact-sales {
  background: #e3e3e3;
  margin: 0 auto;
  padding: 35px 0 45px;
}
.custom-designs-block h2 {
  font: 500 19px/1.2 Roboto;
  margin: 20px 20px 10px;
}
.custom-designs-block p {
  font: 500 15px/1.5 Roboto;
  margin: 0 20%;
}
.custom-designs-block .c_request {
  margin: 40px 0 0;
}
@media only screen and (min-width: 599px) {
  .block .content img {
    width: auto;
  }
  .block {
    padding: 70px 0;
  }
  .block:first-child {
    padding: 45px 0 80px;
  }
  .block.dark {
    padding: 45px 0 72px;
  }
  .help-block {
    padding: 45px 0 22px;
  }
  .help-block p {
    font: 300 29px/1.5 Roboto;
    margin-top: 30px;
  }
  #verticals .quote_wrap {
    padding-top: 45px;
  }
  .quote p {
    font-size: 29px;
    font-weight: 300;
    line-height: 40px;
  }
  .q_nav {
    margin: 24px 0 49px;
  }
  .q_ico {
    margin: 0 auto 43px;
  }
  .featured {
    margin: 0 auto 30px;
  }
  .featured h2 {
    margin-top: 42px;
  }
  .featured h3 {
    margin-bottom: 58px;
  }
}
@media only screen and (min-width: 990px) {
  .custom-designs-block .content {
    border: 2px solid #f1f1f1;
  }
}
.pricing {
  background-color: #fff;
}
.p_inner {
  max-width: 1012px;
  margin: -60px auto 0;
  padding: 0 20px;
}
.p_plans {
  text-align: center;
}
.p_plan {
  position: relative;
  max-width: 286px;
  height: 665px;
  border: 2px solid #e3e3e3;
  border-radius: 4px;
  margin: 0 0 26px;
}
.p_title {
  position: relative;
  width: 100%;
  height: 58px;
  background-color: #e3e3e3;
}
.pt_txt {
  font-size: 19px;
  line-height: 21px;
  color: #565656;
  margin-top: 17px;
}
.pro .pt_txt,
.w_label .pt_txt {
  color: #fff;
}
.pt_arrow {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 18px;
  height: 9px;
  margin-left: -9px;
  background-image: url("../images/img_sprite.png");
  background-position: -138px -227px;
}
.p_price {
  font-size: 55px;
  line-height: 60px;
  font-weight: 300;
  width: 100%;
  margin: 13px 0 1px;
}
.p_currency {
  font-size: 15px;
  line-height: 20px;
  font-weight: 500;
  margin-top: 10px;
}
.p_amount {
  letter-spacing: -2px;
}
.p_vat {
  font-size: 15px;
  line-height: 20px;
  font-weight: 500;
  margin: 37px 0 0 2px;
}
.p_period {
  font-size: 15px;
  line-height: 20px;
  color: #9d9d9d;
  margin-bottom: 20px;
}
.p_features {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
  text-align: left;
}
.p_features .pf_ico {
  position: absolute;
  top: 2px;
  left: 15px;
  width: 18px;
  height: 18px;
  background-image: url("../images/img_sprite.png");
  background-position: -104px -355px;
}
.p_features .pf_txt {
  font-size: 15px;
  line-height: 20px;
  padding-left: 41px;
}
.b_pro span {
  background-position: -124px -355px;
}
.b_label span {
  background-position: -144px -355px;
}
.p_start {
  position: absolute;
  bottom: 18px;
  left: 18px;
  right: 18px;
  height: 46px;
  border: 2px solid #b7b7b7;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
}
.p_start span {
  font-size: 15px;
  line-height: 17px;
  color: #434343;
  margin-top: 14px;
}
.p_start:hover {
  border-color: #b7b7b7;
  background: #b7b7b7;
}
.p_start:hover span {
  color: #fff;
}
.p_upgrade {
  position: absolute;
  bottom: 18px;
  left: 18px;
  right: 18px;
  height: 50px;
  background-color: #3796c9;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
}
.p_upgrade span {
  font-size: 15px;
  line-height: 17px;
  color: #fff;
  padding: 16px 20px 0;
}
.p_upgrade:hover {
  border: 0 none;
  background: #5fb7e5;
}
.p_plan.pro {
  border-color: #629a84;
}
.pro .p_title {
  background-color: #629a84;
}
.pro .p_off {
  color: #629a84;
}
.p_plan.w_label {
  border-color: #ac8fc2;
}
.w_label .p_title {
  background-color: #ac8fc2;
}
.w_label .p_off {
  color: #ac8fc2;
}
.acc_type_label {
  font-size: 14px;
  color: #9d9d9d;
  margin: 3px 0;
}
.acc_type {
  position: absolute;
  bottom: 88px;
  left: 18px;
  right: 20px;
  text-align: left;
}
.acc_type dl {
  cursor: pointer;
  position: relative;
}
.acc_type dt {
  width: 100%;
  font-size: 15px;
  line-height: 17px;
  color: #282727;
  background-color: #fff;
  border: 1px solid #7d7d7d;
  border-radius: 4px;
}
.acc_type dt > span:first-child {
  position: relative;
  line-height: inherit;
  display: block;
  padding: 10px 40px 10px 54px;
  text-transform: capitalize;
}
.acc_type dt > span:first-child + span {
  position: absolute;
  top: 18px;
  right: 13px;
  width: 10px;
  height: 6px;
  background-image: url("../images/img_sprite.png");
  background-position: -164px -355px;
}
.acc_type dd {
  display: none;
  position: absolute;
  top: 38px;
  left: 0;
  width: 100%;
  background-color: #fff;
  border: 1px solid #7d7d7d;
  border-radius: 0 0 4px 4px;
  z-index: 9999;
  max-height: 258px;
  overflow: auto;
}
.acc_type dd > span {
  display: block;
  font-size: 15px;
  line-height: 17px;
  color: #282727;
  padding: 10px 33px 10px 14px;
  text-transform: capitalize;
}
.acc_type dd > span:hover {
  background-color: #3796c9;
  color: #fff;
}
.acc_type dl.open span:first-child + span {
  background-position: -164px -361px;
}
.acc_type dl.open dt {
  border-radius: 4px 4px 0 0;
}
.acc_type dl.open dd {
  display: block;
}
dd .ts_ico {
  display: none;
}
.acc_type .ts_ico {
  position: absolute;
  top: 9px;
  left: 13px;
  width: 30px;
  height: 17px;
  background-image: url("../images/img_sprite.png");
  background-position: -136px -375px;
}
.acc_type .ts_ico.n2 {
  background-position: -104px -375px;
}
.p_discount {
  position: absolute;
  top: -34px;
  right: -17px;
  width: 108px;
  height: 108px;
}
.disclaimer {
  max-width: 1020px;
  font-size: 15px;
  line-height: 20px;
  color: #656565;
  margin: 0 auto 40px;
}
.custom_design {
  max-width: 1020px;
  border: 2px solid #f1f1f1;
  border-radius: 4px;
  text-align: center;
  margin: 0 auto;
}
.custom_design h2 {
  font-size: 19px;
  line-height: 20px;
  font-weight: 500;
  padding: 30px 0 14px;
}
.custom_design p {
  font-size: 15px;
  line-height: 20px;
  margin-bottom: 24px;
  padding: 0 20px;
}
.ps_ico {
  position: absolute;
  top: 9px;
  left: 13px;
  width: 16px;
  height: 17px;
  background-image: url("../images/img_sprite.png");
  background-position: -104px -394px;
}
.p_faq {
  max-width: 1020px;
  margin: -3px auto 17px;
}
.p_faq h2 {
  font-size: 19px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 30px;
}
.p_faq p {
  font-size: 15px;
  line-height: 20px;
  padding: 0 40px 0 40px;
}
.questions {
  float: left;
}
.q_item {
  position: relative;
  margin-bottom: 20px;
  min-height: 40px;
}
.q_item.active .item_q p,
.q_item:hover .item_q p {
  color: #3d97c7;
  cursor: pointer;
}
.q_item.active .item_a p {
  color: #434343;
}
.qi_ico {
  position: absolute;
  top: 1px;
  left: 0;
  width: 20px;
  height: 20px;
  background-image: url("../images/img_sprite.png");
  background-position: -122px -394px;
  cursor: pointer;
}
.q_item:hover .qi_ico {
  background-position: -122px -416px;
}
.q_item.active .qi_ico {
  background-position: -144px -394px;
}
.q_item.active .qi_ico:hover {
  background-position: -144px -416px;
}
.item_a {
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 0.33s cubic-bezier(0.694, 0.0482, 0.335, 1) 0s;
  -webkit-transition-property: max-height;
  -moz-transition: all 0.33s cubic-bezier(0.694, 0.0482, 0.335, 1) 0s;
  -moz-transition-property: max-height;
  -ms-transition: all 0.33s cubic-bezier(0.694, 0.0482, 0.335, 1) 0s;
  -ms-transition-property: max-height;
  -o-transition: all 0.33s cubic-bezier(0.694, 0.0482, 0.335, 1) 0s;
  -o-vtransition-property: max-height;
  transition: all 0.33s cubic-bezier(0.694, 0.0482, 0.335, 1) 0s;
  transition-property: max-height;
}
.q_item.active .item_a {
  max-height: 350px;
}
.item_a p:first-child {
  padding-top: 10px;
}
.color_toggle p {
  color: #d2d2d2;
}
@media only screen and (min-width: 348px) {
  .p_plan {
    width: 286px;
  }
}
@media only screen and (min-width: 599px) {
  .p_head h1 {
    padding: 98px 0 19px;
  }
  .top_line .support {
    display: none;
  }
}
@media only screen and (min-width: 640px) {
  .p_plan {
    margin: 0 13px 26px;
  }
}
@media only screen and (min-width: 972px) {
  .p_inner {
    padding: 0;
  }
  .questions {
    width: 465px;
  }
}
@media only screen and (max-width: 692px) {
  .p_plan {
    height: 392px;
  }
  .p_plan.pro {
    height: 560px;
  }
  .p_plan.w_label {
    height: 645px;
  }
}
.pop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
}
.pop .bg_layer {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(43, 43, 43, 0.8);
}
.pop .pop_content {
  position: absolute;
  width: 440px;
  min-height: 610px;
  top: 50%;
  left: 50%;
  margin-left: -220px;
  background-color: #fff;
  border-radius: 4px;
  margin-top: -362px;
}
.pop .pop_content.form_scroll {
  top: 20px;
  bottom: 20px;
  margin-top: 0;
  min-height: 0;
}
.form_scroll .pop_inner {
  position: relative;
  height: 100%;
  overflow: auto;
}
.pop h3 {
  font-size: 23px;
  line-height: 30px;
  color: #434343;
  font-weight: 500;
  padding: 32px 20px 23px;
  text-align: center;
}
.pop h4 {
  font-size: 29px;
  line-height: 32px;
  color: #434343;
  font-weight: 500;
  margin-bottom: 12px;
}
.pop p {
  font-size: 15px;
  line-height: 17px;
  color: #434343;
}
.pop label {
  font-size: 13px;
  line-height: 15px;
  display: block;
  margin: 0 0 9px -16px;
}
.pop input {
  position: relative;
  z-index: 1;
  font-size: 15px;
  height: 48px;
  color: #282727;
  border: 1px solid #7d7d7d;
  border-radius: 4px;
  background-color: #fff;
  width: 100%;
  padding: 0 10px;
  margin: 0 0 16px -10px;
}
.pop .radio_input {
  margin-left: -10px;
}
.pop input.radio_button {
  position: relative;
  z-index: 1;
  font-size: 15px;
  height: 21px;
  width: 20px;
  padding: 0 10px;
  margin: 0 4px 16px 0;
}
.pop textarea {
  position: relative;
  z-index: 1;
  font-size: 15px;
  line-height: 20px;
  color: #282727;
  background-color: #fff;
  border: 1px solid #7d7d7d;
  border-radius: 4px;
  width: 100%;
  min-height: 40px;
  padding: 10px;
  margin-left: -10px;
}
.pop input.focused {
  border-color: #3796c9;
  border-width: 2px;
  height: 46px;
  padding: 0 13px;
}
.input {
  position: relative;
}
.error_ico {
  display: none;
  position: absolute;
  top: 15px;
  right: -49px;
  width: 24px;
  height: 21px;
  background-image: url("../images/img_sprite.png");
  background-position: -187px -355px;
}
.false .error_ico {
  background-position: -214px -355px;
}
.false .error_ico,
.true .error_ico {
  display: inline-block;
}
.error_msg {
  display: none;
  position: absolute;
  top: 5px;
  right: 279px;
  font-size: 13px;
  line-height: 40px;
  height: 40px;
  color: #fff;
  background-color: #c32f2f;
  border-radius: 4px;
  white-space: nowrap;
  padding: 0 21px;
}
.em_arrow {
  position: absolute;
  top: 12px;
  right: -8px;
  width: 9px;
  height: 16px;
  background-image: url("../images/img_sprite.png");
  background-position: -176px -355px;
}
.false .error_msg {
  display: inline-block;
}
.form_scroll .false .error_msg {
  display: none;
}
.pop .false input {
  color: #ce2533;
}
.pop .fields {
  width: 259px;
  margin: 0 auto;
}
.education_form {
  padding-bottom: 20px;
}
.pop {
  opacity: 1;
  transition: opacity 0.2s;
}
.pop-hide {
  opacity: 0;
  display: none;
}
.cboxes {
  width: 300px;
  margin: 12px 0 0 -15px;
}
.cbox {
  float: left;
  width: 150px;
  margin-bottom: 9px;
  cursor: pointer;
}
.cb_ico {
  width: 20px;
  height: 20px;
  background-image: url("../images/img_sprite.png");
  background-position: -166px -394px;
  margin-right: 9px;
}
.checked .cb_ico {
  background-position: -166px -416px;
}
.cb_txt {
  font-size: 13px;
  line-height: 20px;
}
.submitted {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  margin-top: -270px;
  text-align: center;
}
.submitted .u_ico {
  width: 200px;
  height: 200px;
  background-image: url(../images/upgraded.png);
  margin: 90px 0 38px;
}
.pop_submit {
  display: block;
  width: 280px;
  height: 50px;
  margin: 0 auto 10px;
  background-color: #3195cb;
  font-size: 17px;
  line-height: 19px;
  color: #fff;
  text-align: center;
  border-radius: 4px;
}
.pop_submit:hover {
  background-color: #5fb7e5;
}
.sub_close {
  width: 256px;
  height: 46px;
  margin: 81px 3px 0;
  background-color: #fff;
  color: #434343;
  font-size: 17px;
  line-height: 19px;
  text-align: center;
  border: 2px solid #656565;
  border-radius: 4px;
}
.sub_close:hover {
  color: #fff;
  background-color: #7d7d7d;
  border-color: #7d7d7d;
}
.sub_txt {
  margin-top: 12px;
}
.pop .close {
  position: absolute;
  top: -15px;
  left: -15px;
  z-index: 1;
  width: 30px;
  height: 30px;
  background: #161616 url("../images/img_sprite.png") -105px -229px;
  border-radius: 4px;
  cursor: pointer;
}
.pop .close:hover {
  background-color: #333;
}
.pop_content.h_740 {
  top: 20px;
  margin-top: 0;
}
.pop_height {
  overflow: hidden;
}
@media only screen and (max-width: 440px) {
  .pop {
    position: absolute;
  }
  .pop .pop_content {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0 !important;
    border-radius: 0;
  }
  .pop .pop_content.form_scroll {
    top: 0;
    bottom: 0;
  }
  .pop .close {
    left: auto;
    right: 10px;
    top: 10px;
    background-color: #b1b1b1;
  }
  .pop .close:hover {
    background-color: #818181;
  }
  .submitted {
    top: 0;
    margin-top: 0;
  }
  .submitted .u_ico {
    width: 100px;
    height: 100px;
    background-size: 100% 100%;
    margin-top: 40px;
  }
  .pop .error_msg {
    display: none;
  }
  .pop_height {
    overflow: auto;
  }
  .pop_height .body_inner {
    height: 800px;
    overflow: hidden;
  }
}
.pop .login_popup,
.pop .register_popup {
  min-height: 490px;
  margin-top: -270px;
  display: none;
}
.pop .forgot_popup {
  min-height: 332px;
  margin-top: -160px;
  display: none;
}
.log_form {
  width: 259px;
  margin: 15px auto 0;
}
.log_form input {
  margin-bottom: 20px;
}
.remember_forget {
  width: 220px;
  margin: -10px auto 10px !important;
  height: 40px !important;
}
.tgl_remember_me {
  margin-left: auto !important;
}
#forgot-password {
  float: right;
  font-size: 13px;
  line-height: 40px;
  text-align: right;
  margin-right: -15px;
}
.sc_txt {
  display: block;
  font-size: 17px;
  line-height: 19px;
  margin-top: 13px;
}
.a_reg {
  text-align: center;
  margin-top: 29px;
  margin-bottom: 30px;
}
.a_reg a {
  font-size: 15px;
  line-height: 17px;
}
.login_popup .pop_submit {
  margin-top: 10px;
}
.register_popup .pop_submit {
  margin-top: 10px;
}
.forgot_popup .pop_submit {
  margin-top: 10px;
  margin-bottom: 11px;
}
.register_popup .terms_agreement {
  position: relative;
  max-width: 260px;
  margin: 0 auto;
  font-size: 9px;
  font-weight: 400;
  line-height: 1.4;
  color: #777;
  text-align: center;
}
.pop .processing {
  background-color: #9cc4d9;
  color: #c7dde9;
  cursor: default;
}
.pop .log_tip {
  width: 260px;
  margin: 0 auto;
  font-size: 11px;
  line-height: 20px;
}
.pop .forgot_popup h3 {
  padding-bottom: 19px;
}
.global_notification {
  position: fixed;
  bottom: 13px;
  right: 13px;
  z-index: 9999;
  width: 450px;
  border: 2px solid rgba(101, 101, 101, 0.5);
  border-radius: 5px;
  overflow: hidden;
  display: none;
}
.msg_head {
  position: relative;
  height: 50px;
  background-color: #bf3338;
}
.true .msg_head {
  background-color: #3ea771;
}
.msg_head p {
  font-size: 15px;
  line-height: 17px;
  color: #fff;
  padding: 17px 60px 0;
  text-align: center;
}
.msg_head .ico {
  position: absolute;
  top: 14px;
  left: 19px;
  z-index: 1;
  width: 27px;
  height: 20px;
  background-image: url("../images/img_sprite.png");
  background-position: -214px -378px;
}
.true .msg_head .ico {
  background-position: -187px -378px;
}
.msg_head .n_close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  width: 50px;
  height: 50px;
  cursor: pointer;
}
.msg_head .nc_inner {
  width: 30px;
  height: 30px;
  background-image: url("../images/img_sprite.png");
  background-position: -105px -229px;
  margin: 10px 0 0 8px;
  border-radius: 4px;
}
.msg_head:hover .nc_inner {
  background-color: rgba(0, 0, 0, 0.3);
}
.msg_body {
  background-color: #fff;
  overflow: hidden;
  padding: 19px 0 11px;
}
.msg_body:empty {
  display: none;
}
.msg_body .msg_p {
  position: relative;
  font-size: 15px;
  line-height: 20px;
  text-align: left;
  margin: 0 0 10px 21px;
  padding: 0 28px;
}
.msg_p .p_ico {
  position: absolute;
  top: 6px;
  left: 0;
  width: 10px;
  height: 10px;
  background-image: url("../images/img_sprite.png");
  background-position: -242px -355px;
}
.true .msg_p .p_ico {
  background-position: -242px -378px;
}
.true .msg_p .p_ico {
  background-position: -242px -378px;
}
.ls_or {
  width: 280px;
  height: 1px;
  margin: 20px auto;
  text-align: center;
}
.ls_line {
  height: 1px;
  background-color: #ccc;
}
.or_txt {
  width: 60px;
  font-size: 15px;
  line-height: 17px;
  color: #aaa;
  background-color: #fff;
  margin-top: -10px;
  position: inherit !important;
}
.bg_grey .pop_content {
  background-color: #282727;
}
.bg_grey h3 {
  color: #fff;
}
.bg_grey .ls_line {
  background-color: #434343;
}
.bg_grey .or_txt {
  color: #656565;
  background-color: #282727;
}
.bg_grey p {
  color: #fff;
}
@media only screen and (max-width: 440px), screen and (max-height: 740px) {
  .global_notification {
    display: none;
  }
}
#remember_me {
  margin-left: -15px;
  float: left;
}
.toggle_btn {
  position: relative;
  font-size: 13px;
  height: 20px;
  color: #434343;
  font-weight: 500;
  text-align: left;
  background: 0 0;
  padding: 0 10px 1px 33px;
  margin: 10px 0 2px;
  cursor: pointer;
}
.toggle_btn:hover {
  text-decoration: underline;
}
.toggle_btn:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background-image: url(../images/share_v4.png);
  background-position: -124px -42px;
}
.toggle_btn.active:before {
  background-image: url(../images/share_v4.png);
  background-position: -146px -42px;
}
.head .c_img {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 100%;
  min-width: 800px;
  height: auto;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}
@media only screen and (min-width: 600px) {
  .head .c_img {
    min-width: 1350px;
    max-width: 2100px;
  }
  .head {
    min-height: 775px !important;
  }
}
@media only screen and (min-width: 1375px) {
  .head {
    min-height: 760px !important;
  }
}
@media only screen and (min-width: 1500px) {
  .head {
    min-height: 814px !important;
  }
}
@media only screen and (min-width: 1700px) {
  .head {
    min-height: 880px !important;
  }
}
@media only screen and (min-width: 365px) {
  .location_img {
    width: 290px;
  }
  .lt_1 {
    position: relative;
    left: -7px;
  }
  .lt_2 {
    position: relative;
    right: -58px;
  }
  .lt_2 br {
    display: none;
  }
}
@media only screen and (min-width: 599px) {
  .team_content h1 {
    font-size: 49px;
    padding: 139px 25px 15px;
  }
  .location_img {
    width: 290px;
  }
}
@media only screen and (min-width: 745px) {
  .sub_box {
    margin: 0 13px 26px;
  }
}
@media only screen and (min-width: 900px) {
  .team_pic {
    height: 563px;
  }
}
@media only screen and (min-width: 1000px) {
  .tc_inner {
    width: 1020px;
    margin: 0 auto;
  }
  .sub_box {
    margin: 0 0 0 26px;
  }
  .sub_box:first-child {
    margin-left: 0;
  }
  .press_col {
    width: 449px;
  }
}
.login {
  display: flex;
  align-items: center;
  margin-left: 12px;
}
.login__link {
  height: 36px;
  min-width: 136px;
  border-radius: 4px;
  margin-right: 8px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.1s ease-out;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 15px;
}
.login__link:focus,
.login__link:hover {
  text-decoration: none;
  color: #fff;
}
.login__link--login {
  border-color: rgba(255, 255, 255, 0.2);
}
.login__link--login:focus,
.login__link--login:hover {
  text-decoration: none;
  color: #656565;
  border-color: #fff;
  background-color: #fff;
}
.login__link--signup {
  background-color: #ce2533;
}
.login__link--signup:focus,
.login__link--signup:hover {
  background-color: #de4854;
}
.false .error_msg {
  animation: slide_left 50ms linear;
  -webkit-animation: slide_left 50ms linear;
  -moz-animation: slide_left 50ms linear;
  -o-animation: slide_left 50ms linear;
  -ms-animation: slide_left 50ms linear;
}
.false .error_ico,
.true .error_ico {
  animation: slide_right 50ms linear;
  -webkit-animation: slide_right 50ms linear;
  -moz-animation: slide_right 50ms linear;
  -o-animation: slide_right 50ms linear;
  -ms-animation: slide_right 50ms linear;
}
@keyframes slide_left {
  0% {
    transform: translateX(245px);
  }
  100% {
    transform: translateX(0);
  }
}
@-webkit-keyframes slide_left {
  0% {
    -webkit-transform: translateX(245px);
  }
  100% {
    -webkit-transform: translateX(0);
  }
}
@-moz-keyframes slide_left {
  0% {
    -moz-transform: translateX(245px);
  }
  100% {
    -moz-transform: translateX(0);
  }
}
@-o-keyframes slide_left {
  0% {
    -o-transform: translateX(245px);
  }
  100% {
    -o-transform: translateX(0);
  }
}
@-ms-keyframes slide_left {
  0% {
    -ms-transform: translateX(245px);
  }
  100% {
    -ms-transform: translateX(0);
  }
}
@keyframes slide_right {
  0% {
    transform: translateX(-100px);
  }
  100% {
    transform: translateX(0);
  }
}
@-webkit-keyframes slide_right {
  0% {
    -webkit-transform: translateX(-100px);
  }
  100% {
    -webkit-transform: translateX(0);
  }
}
@-moz-keyframes slide_right {
  0% {
    -moz-transform: translateX(-100px);
  }
  100% {
    -moz-transform: translateX(0);
  }
}
@-o-keyframes slide_right {
  0% {
    -o-transform: translateX(-100px);
  }
  100% {
    -o-transform: translateX(0);
  }
}
@-ms-keyframes slide_right {
  0% {
    -ms-transform: translateX(-100px);
  }
  100% {
    -ms-transform: translateX(0);
  }
}
.frontpage .frontpage_header {
  top: 0;
  transition: all 0.4s;
}
.frontpage.team_page .pricing {
  text-align: left;
}

ol,
ul {
  list-style: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.p-0 {
  padding: 0 !important;
}
.m-0 {
  margin: 0 !important;
}
.d-flex {
  display: flex;
}
.f-wrap {
  flex-wrap: wrap;
}
@media only screen and (max-width: 480px) {
  .hidden-phone {
    display: none;
  }
}
.g-header {
  padding: 120px 0 100px;
  background-image: url(../images/bg_header.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  text-align: center;
}
.g-header__title {
  font-weight: 300;
  font-size: 49px;
  line-height: 60px;
  max-width: 840px;
  margin: 0 auto;
}
@media only screen and (max-width: 480px) {
  .g-header__title {
    font-size: 42px;
    line-height: 48px;
  }
}
.g-header__description {
  font-size: 15px;
  font-weight: 500;
  line-height: 25px;
  max-width: 460px;
  margin: 20px auto 15px;
}
.g-header__embed {
  max-width: 700px;
  min-height: 470px;
  margin: 35px auto;
}
@media only screen and (max-width: 480px) {
  .g-header__embed {
    min-height: 280px;
  }
}
.g-header__embed .infogram-embed {
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.33);
  border-radius: 4px;
  background: rgba(71, 59, 107, 0.5);
}
.g-button {
  cursor: pointer;
  font-family: inherit;
  box-sizing: border-box;
  font-size: 15px;
  font-weight: 500;
  background-color: #3195cb;
  border-radius: 4px;
  width: 220px;
  color: #fff;
  height: 40px;
  line-height: 40px;
  transition: 0.25s all cubic-bezier(0.165, 0.84, 0.44, 1);
}
.g-button:active,
.g-button:focus,
.g-button:hover {
  color: #fff;
  text-decoration: none;
  background-color: #58aad7;
}
.g-button--accent {
  background-color: #ce2533;
}
.g-button--accent:active,
.g-button--accent:focus,
.g-button--accent:hover {
  background-color: #de4854;
  color: #fff;
}
.g-button--large {
  width: 300px;
  height: 50px;
  line-height: 47px;
  font-size: 17px;
  box-sizing: border-box;
  padding: 0 20px;
}
@media screen and (max-width: 360px) {
  .g-button--large {
    width: 100%;
    font-size: 15px;
  }
}
.g-button--transparent {
  border: 1px solid #fff;
  background-color: transparent;
}
.g-button--transparent:active,
.g-button--transparent:focus,
.g-button--transparent:hover {
  background-color: #fff;
  color: #656565;
}
.g-button--gray {
  border: 2px solid #656565;
  color: #656565;
  background-color: transparent;
}
.g-button--gray:active,
.g-button--gray:focus,
.g-button--gray:hover {
  color: #fff;
  background-color: #656565;
}
.g-button--centered {
  display: block;
  margin: 0 auto;
}
.g-link {
  color: #3796c9;
}
.g-link:hover {
  color: #3896c9;
}
.g-paragraph {
  font-weight: 500;
  color: #656565;
  font-size: 15px;
  line-height: 25px;
}
.fade-in {
  opacity: 0;
  -webkit-animation: fadeIn ease-in 1;
  -moz-animation: fadeIn ease-in 1;
  animation: fadeIn ease-in 1;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 0.6s;
  -moz-animation-duration: 0.6s;
  animation-duration: 0.6s;
}
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-moz-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
img {
  max-width: 100%;
}
a {
  color: #3195cb;
}
a:hover {
  color: #58aad7;
}
.frontpage .section {
  padding: 50px 0;
}
.frontpage .section .container {
  max-width: 1000px;
  box-sizing: border-box;
  margin: 0 auto;
}
@media only screen and (max-width: 1199px) {
  .frontpage .section .container {
    padding: 0 20px;
  }
}
.frontpage .section--gray {
  background-color: #ebebee;
}
.frontpage .section--dark {
  color: #fff;
}
.frontpage .section--light {
  color: #656565;
}
.frontpage .section__paragraph {
  font-size: 15px;
  line-height: 25px;
  color: inherit;
  font-weight: 400;
}
.frontpage .section__title {
  color: inherit;
  font-size: 29px;
  font-weight: 300;
  line-height: 34px;
}
.frontpage .section__description {
  color: inherit;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  padding-top: 0;
  margin-top: 20px;
  margin-bottom: 15px;
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}
.footer,
.frontpage {
  position: relative;
  background: #fff;
  text-align: center;
}
.footer .f_section,
.frontpage .f_section {
  background: #f7f7f7;
}
.footer .f_section .f_links,
.frontpage .f_section .f_links {
  text-align: left;
  max-width: 930px;
  margin: 0 auto 40px;
  padding: 40px 0 30px;
}
.footer .f_section .f_links:after,
.frontpage .f_section .f_links:after {
  content: "";
  display: table;
  clear: both;
}
.footer .f_section .f_links a,
.frontpage .f_section .f_links a {
  display: block;
  font-size: 15px;
  line-height: 17px;
  margin-bottom: 13px;
}
.footer .f_section .f_links .col,
.frontpage .f_section .f_links .col {
  width: 165px;
  margin: 0 10px;
}
.footer .f_section .f_links .col:last-child,
.frontpage .f_section .f_links .col:last-child {
  margin: 0 0 0 10px;
}
@media screen and (min-width: 930px) {
  .footer .f_section .f_links .col,
  .frontpage .f_section .f_links .col {
    float: left;
  }
}
@media screen and (max-width: 930px) {
  .footer .f_section .f_links .col,
  .frontpage .f_section .f_links .col {
    display: block;
    margin: 0 auto 20px !important;
    text-align: center;
  }
}
@media screen and (max-width: 420px) {
  .footer .f_section .f_links,
  .frontpage .f_section .f_links {
    padding: 20px;
  }
}
.footer .contacts,
.frontpage .contacts {
  display: flex;
  justify-content: center;
}
.footer .contacts__icon,
.frontpage .contacts__icon {
  font-size: 15px;
  text-align: center;
  position: relative;
  padding-left: 23px;
  margin: 0 12px;
  background-position: left;
  background-repeat: no-repeat;
}
.footer .contacts__icon--email,
.frontpage .contacts__icon--email {
  background-image: url("../images/email-grey.svg");
  -ms-background-position-x: -39px;
}
.footer .contacts__icon--phone,
.frontpage .contacts__icon--phone {
  background-image: url("../images/phone-grey.svg");
  -ms-background-position-x: -63px;
}
.footer .contacts__icon--chat,
.frontpage .contacts__icon--chat {
  background-image: url("../images/chat-grey.svg");
  -ms-background-position-x: -33px;
}
.footer .copyright,
.frontpage .copyright {
  text-align: center;
  padding-bottom: 30px;
}
.footer .copyright p,
.frontpage .copyright p {
  font-size: 15px;
  color: #696969;
  line-height: 20px;
  padding: 30px 20px 25px;
}
.footer .copyright p span,
.frontpage .copyright p span {
  font-size: 11px;
  line-height: 20px;
}
.footer .copyright img,
.frontpage .copyright img {
  width: 65px;
  height: 20px;
}
@media screen and (max-width: 420px) {
  .footer .contacts ul li,
  .frontpage .contacts ul li {
    display: block;
    padding: 10px 0;
  }
  .footer .contacts a,
  .frontpage .contacts a {
    font-size: 13px;
  }
}
@media screen and (max-width: 360px) {
  .footer .contacts,
  .frontpage .contacts {
    flex-flow: row wrap;
  }
  .footer .contacts :last-child,
  .frontpage .contacts :last-child {
    margin-top: 12px;
  }
}
.soc_icons {
  padding-bottom: 30px;
  display: flex;
  justify-content: center;
}
.soc_icons__icon {
  flex-basis: auto;
  width: 40px;
  height: 40px;
  margin: 0 5px;
  transition: 0.25s all ease-in-out;
  border-radius: 4px;
  background-color: #ebebee;
  background-position: center;
  background-repeat: no-repeat;
}
.soc_icons__icon:hover {
  background-color: rgba(235, 235, 238, 0.6);
}
.soc_icons__icon--facebook {
  background-size: 45%;
  background-image: url("../images/facebook-grey.svg");
}
.soc_icons__icon--twitter {
  background-size: 51%;
  background-image: url("../images/twitter-grey.svg");
}
.soc_icons__icon--linkedin {
  background-size: 48%;
  background-image: url("../images/linkedin-grey.svg");
}
.soc_icons__icon--instagram {
  background-size: 48%;
  background-image: url("../images/instagram.svg");
}
.soc_icons__icon--youtube {
  background-size: 48%;
  background-image: url("../images/youtube.svg");
}
.frontpage {
  text-align: left;
}
.frontpage .p_head {
  top: 0;
  left: 0;
  width: 100%;
  min-height: 299px;
  text-align: center;
}
.frontpage .p_head h1 {
  font-size: 41px;
  line-height: 60px;
  color: #fff;
  font-weight: 300;
  text-align: center;
  padding: 135px 0 10px;
  margin-bottom: 30px;
}
.frontpage .p_head h2 {
  max-width: 610px;
  font-size: 18px;
  line-height: 29px;
  color: #fff;
  text-align: center;
  font-weight: 500;
  margin: 0 auto 30px;
}
h3 {
  /* text-align: center;
  padding: 50px 0 20px;
  color: #656565; */
  font-size: 29px;
  line-height: 35px;
  font-weight: 300;
}
.frontpage h4 {
  text-align: center;
  color: #656565;
  font-size: 15px;
  line-height: 18px;
  font-weight: 500;
}
.frontpage .cta {
  color: #fff;
  border: 2px solid #fff;
  border-radius: 4px;
  height: 50px;
  line-height: 46px;
  box-sizing: border-box;
  width: 300px;
}
.frontpage .cta:hover {
  color: #31624b;
  background: #fff;
  text-decoration: none;
}
.frontpage .head .c_request {
  border-color: #fff;
  position: relative;
  z-index: 1;
}
.frontpage .head .c_request:hover {
  background: #ce2533;
}
.frontpage .head .c_request:hover span {
  color: #fff;
}
.frontpage .head .c_request span {
  margin-left: auto;
  color: #fff;
}
.frontpage .login .menu_items.languages {
  margin-left: 0;
  margin-top: -20px;
  display: none;
}
.frontpage .menu_items {
  display: none;
  height: 80px;
}
@media only screen and (min-width: 1230px) {
  .frontpage .menu_items {
    display: flex;
  }
}

.frontpage .top_line {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background: 0 0;
  box-sizing: border-box;
  transition: all 0.4s;
  display: flex;
  padding-left: 14px;
  padding-right: 30px;
}
@media screen and (max-width: 1230px) {
  .frontpage .top_line {
    padding-right: 80px;
  }
}
.frontpage .top_line.accent {
  background: rgba(64, 49, 97, 0.8);
}
.frontpage .top_line.accent-always {
  background: rgba(64, 49, 97, 0.8);
}
.frontpage .top_line.transparent .menu_items a:hover {
  background: rgba(255, 255, 255, 0.1);
}
.frontpage .top_line.transparent .has-submenu:hover {
  background: rgba(255, 255, 255, 0.1);
}
.frontpage.examples .visualize-steps a,
.frontpage.features .visualize-steps a,
.frontpage.index .visualize-steps a {
  display: block;
  width: 300px;
  font-size: 17px;
  border-radius: 4px;
  height: 46px;
  background-color: transparent;
  color: #656565;
  border: 2px solid #656565;
  text-align: center;
  line-height: 45px;
  transition: all 0s;
  margin: 0 auto;
}
.frontpage.examples .visualize-steps a:hover,
.frontpage.features .visualize-steps a:hover,
.frontpage.index .visualize-steps a:hover {
  text-decoration: none;
  color: #fff;
  background: #656565;
}
.frontpage .notification {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background: #3195cb;
  z-index: 9999;
  color: #fff;
  text-align: center;
  line-height: 40px;
}
.frontpage .notification a {
  color: #fff;
  text-decoration: underline;
}
@media screen and (min-width: 930px) {
  .frontpage .notification {
    display: block;
  }
  .frontpage .top_line.__with_notification {
    top: 40px;
  }
}
@media only screen and (max-width: 599px) {
  .frontpage .top_line {
    position: absolute;
    z-index: 1;
  }
  .frontpage .top_line .login {
    display: none;
  }
  .frontpage .team_header {
    height: 600px !important;
  }
  .frontpage .p_head h1 {
    max-width: 300px;
    width: auto;
    margin: 0 auto;
    font-size: 36px;
    line-height: 48px;
  }
  .frontpage .teams .p_head {
    margin-left: -150px !important;
    width: 300px !important;
  }
  .frontpage .pricing .p_head {
    padding-top: 100px;
  }
}
.frontpage .frontpage_header {
  background-image: url("../images/header.svg");
  background-size: cover;
  display: block;
  height: 830px;
  padding: 0 20px;
}
.frontpage .join_now {
  min-width: 290px;
  height: 50px;
  background-color: #ce2533;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  margin: 21px 0;
  position: relative;
  transition: 0.4s all cubic-bezier(0.165, 0.84, 0.44, 1);
}
.frontpage .join_now span {
  font-size: 17px;
  line-height: 17px;
  color: #fff;
  padding: 16px 20px 0;
}
.frontpage .join_now:focus,
.frontpage .join_now:hover {
  background-color: #dc5356;
}
.frontpage .cta_default {
  cursor: pointer;
  width: 290px;
  margin: 21px 0;
  background-color: #3195cb;
  font-size: 17px;
  line-height: 19px;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 16px 0;
  position: relative;
}
.frontpage .cta_default:focus,
.frontpage .cta_default:hover {
  background-color: #5fb7e5;
}
.frontpage .ig_counter {
  font-size: 17px;
  color: #fff;
  font-weight: 500;
  padding: 0 25px;
}
.frontpage .teaser {
  text-align: center;
  padding: 0 20px 90px;
}
.frontpage .teaser .screen_wrapper {
  max-width: 738px;
  margin: 0 auto;
  position: relative;
}
.frontpage .teaser .description {
  max-width: 700px;
  margin: 10px auto 0;
  line-height: 25px;
  font-size: 15px;
  color: #656565;
}
.frontpage .signup_block {
  background: #fff;
  padding-top: 0;
}
.frontpage .actions {
  background: #ebebee;
}
.frontpage .actions ul {
  list-style-type: none;
  padding: 0;
  max-width: 930px;
  margin: 0 auto;
}
.frontpage .actions ul li {
  padding: 44px 0;
  display: inline-block;
  width: 290px;
  text-align: center;
}
.frontpage .actions ul li .title {
  padding: 0 0 20px;
  font-size: 25px;
  line-height: 25px;
  font-weight: 300;
  color: #656565;
}
.frontpage .actions ul li .descr {
  font-size: 15px;
  font-weight: 500;
  max-width: 210px;
  margin: 0 auto;
  color: #656565;
}
.frontpage .actions ul li img {
  width: 160px;
  height: 188px;
}
.frontpage .actions ul li.first,
.frontpage .actions ul li.second {
  margin-right: 20px;
}
@media screen and (max-width: 930px) {
  .frontpage .actions ul li {
    padding: 30px 0;
    display: block;
    margin-right: auto !important;
    margin: 0 auto;
  }
}
.frontpage .visualize-steps ul {
  list-style-type: none;
  padding: 0 20px;
  max-width: 972px;
  margin: 0 auto;
}
.frontpage .visualize-steps ul li {
  padding: 50px 10px 0;
  display: inline-block;
  width: 301px;
  text-align: left;
}
@media screen and (max-width: 943px) {
  .frontpage .visualize-steps ul li {
    display: block;
    margin-right: auto !important;
    margin: 0 auto;
    width: 100%;
  }
}
.frontpage .visualize-steps ul li h4 {
  font-size: 21px;
  font-weight: 500;
  line-height: 34px;
  color: #656565;
  margin-bottom: 7px;
  text-align: left;
}
.frontpage .visualize-steps ul li .descr {
  font-size: 15px;
  font-weight: 400;
  text-align: left;
  line-height: 26px;
  color: #656565;
}
.frontpage .visualize-steps ul li img {
  width: auto;
  height: 100px;
  margin-bottom: 15px;
}
.frontpage .visualize-steps ul li:nth-child(n + 4) img {
  height: 80px;
}
.frontpage .visualize-steps {
  padding: 0 20px 50px;
}
.frontpage .visualize-steps ul li {
  padding: 40px 7px 30px;
}
.frontpage .visualize-steps ul li .title {
  line-height: 29px;
  margin-bottom: 15px;
}
.frontpage .visualize-steps ul li img {
  height: 166px;
}
.frontpage .visualize-steps a {
  display: block;
  width: 300px;
  font-size: 17px;
  border-radius: 4px;
  height: 50px;
  background-color: #3796c9;
  color: #fff;
  text-align: center;
  line-height: 47px;
  margin-bottom: 20px;
  transition: all 0.4s;
  margin: 0 auto;
}
.frontpage .visualize-steps a:hover {
  text-decoration: none;
  background: #5fb7e5;
}
.frontpage .pricing {
  padding: 40px 0;
}
.frontpage .pricing .wrapper {
  max-width: 930px;
  margin: 0 auto;
  font-size: 15px;
}
.frontpage .pricing .wrapper .title {
  font-size: 29px;
  line-height: 25px;
  font-weight: 300;
  color: #656565;
  text-align: center;
  padding-bottom: 20px;
}
.frontpage .pricing .wrapper .description {
  color: #656565;
  text-align: center;
  padding-bottom: 40px;
  line-height: 25px;
}
.frontpage .pricing .wrapper .per_seat {
  text-align: center;
  color: #656565;
}
.frontpage .pricing .wrapper .per_seat .cur {
  margin-top: -8px;
  vertical-align: top;
}
.frontpage .pricing .wrapper .per_seat .price {
  font-size: 47px;
  font-weight: 300;
  vertical-align: baseline;
}
.frontpage .pricing .wrapper .per_seat .team_member {
  vertical-align: baseline;
}
.frontpage .pricing .wrapper .plans {
  list-style-type: none;
}
.frontpage .pricing .wrapper .plans li {
  display: inline-block;
  width: 290px;
}
.frontpage .pricing .wrapper .plans li.first,
.frontpage .pricing .wrapper .plans li.second {
  margin-right: 20px;
}
.frontpage .pricing .wrapper .plans .pop_features {
  padding-bottom: 10px;
}
.frontpage .pricing .wrapper .plans .pb_period,
.frontpage .pricing .wrapper .plans .pb_price {
  text-align: center;
  width: 230px;
  padding: 0 10px;
}
.frontpage .pricing .wrapper .plans .pb_line {
  line-height: 33px;
  padding-left: 20px;
}
.frontpage .pricing .wrapper .plans .pb_line.pb_bullet {
  padding-left: 40px;
}
.frontpage .pricing .wrapper .plans .pb_line.pb_bullet:before {
  content: "";
  display: block;
  position: absolute;
  background: #fff;
  height: 10px;
  width: 10px;
  border-radius: 10px;
  left: 0;
  top: 11px;
  margin-left: 20px;
}
.frontpage .pricing .wrapper .plans .pb_business,
.frontpage .pricing .wrapper .plans .pb_enterprise,
.frontpage .pricing .wrapper .plans .pb_pro {
  border-radius: 4px;
}
.frontpage .pricing .wrapper .plans .pb_business .pb_title,
.frontpage .pricing .wrapper .plans .pb_enterprise .pb_title,
.frontpage .pricing .wrapper .plans .pb_pro .pb_title {
  height: 170px;
  color: #fff;
  border-radius: 4px 4px 0 0;
}
.frontpage .pricing .wrapper .plans .pb_business .pop_features,
.frontpage .pricing .wrapper .plans .pb_enterprise .pop_features,
.frontpage .pricing .wrapper .plans .pb_pro .pop_features {
  color: #fff;
  position: relative;
  padding: 20px 0;
}
.frontpage .pricing .wrapper .plans .pb_business .pop_features .pb_headline,
.frontpage .pricing .wrapper .plans .pb_enterprise .pop_features .pb_headline,
.frontpage .pricing .wrapper .plans .pb_pro .pop_features .pb_headline {
  padding: 0 20px 20px;
}
.frontpage .pricing .wrapper .plans .pb_business .pop_features .pb_line_title,
.frontpage .pricing .wrapper .plans .pb_enterprise .pop_features .pb_line_title,
.frontpage .pricing .wrapper .plans .pb_pro .pop_features .pb_line_title {
  line-height: 20px;
  padding-left: 20px;
  padding-bottom: 10px;
}
.frontpage .pricing .wrapper .plans .pb_business .pop_features .pb_bullet:after,
.frontpage
  .pricing
  .wrapper
  .plans
  .pb_enterprise
  .pop_features
  .pb_bullet:after,
.frontpage .pricing .wrapper .plans .pb_pro .pop_features .pb_bullet:after {
  background-color: #fff;
}
.frontpage .pricing .wrapper .plans .pb_business .pop_features:before,
.frontpage .pricing .wrapper .plans .pb_enterprise .pop_features:before,
.frontpage .pricing .wrapper .plans .pb_pro .pop_features:before {
  position: absolute;
  display: block;
  content: "";
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #476579;
  top: -8px;
  left: 50%;
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}
.frontpage .pricing .wrapper .plans .pb_business .pop_features .all_features,
.frontpage .pricing .wrapper .plans .pb_enterprise .pop_features .all_features,
.frontpage .pricing .wrapper .plans .pb_pro .pop_features .all_features {
  text-decoration: none;
  padding: 20px 20px 0;
}
.frontpage
  .pricing
  .wrapper
  .plans
  .pb_business
  .pop_features
  .all_features:hover,
.frontpage
  .pricing
  .wrapper
  .plans
  .pb_enterprise
  .pop_features
  .all_features:hover,
.frontpage .pricing .wrapper .plans .pb_pro .pop_features .all_features:hover {
  text-decoration: underline;
}
.frontpage .pricing .wrapper .plans .pb_pro {
  height: 452px;
}
.frontpage .pricing .wrapper .plans .pb_pro .pop_features {
  background: #2f6857;
  height: 242px;
}
.frontpage .pricing .wrapper .plans .pb_pro .pop_features .pb_headline {
  padding: 0 20px 70px;
}
.frontpage .pricing .wrapper .plans .pb_pro .pop_features:before {
  border-bottom-color: #2f6857;
}
.frontpage .pricing .wrapper .plans .pb_pro .pop_features .all_features {
  color: #82c9ee;
}
.frontpage .pricing .wrapper .plans .pb_business {
  width: auto;
}
.frontpage .pricing .wrapper .plans .pb_business .pop_features {
  background: #476579;
}
.frontpage .pricing .wrapper .plans .pb_business .pop_features:before {
  border-bottom-color: #476579;
}
.frontpage .pricing .wrapper .plans .pb_business .pop_features .all_features {
  color: #82c9ee;
}
.frontpage .pricing .wrapper .plans .pb_enterprise .pop_features {
  background: #664f7c;
}
.frontpage .pricing .wrapper .plans .pb_enterprise .pop_features:before {
  border-bottom-color: #664f7c;
}
.frontpage .pricing .wrapper .plans .pb_enterprise .pop_features .all_features {
  color: #caace7;
}
.frontpage .pricing .wrapper .plans .pb_enterprise .contact_sales {
  height: 40px;
  border: 2px solid #fff;
  border-radius: 4px;
  color: #fff;
  min-width: 200px;
  margin-top: 20px;
}
.frontpage .pricing .wrapper .plans .pb_enterprise .contact_sales:hover {
  text-decoration: none;
  background: #fff;
  color: #9c81b6;
}
.frontpage .pricing .wrapper .plans .pb_enterprise .contact_sales span {
  font-size: 15px;
  line-height: 40px;
  font-weight: 500;
}
.frontpage .pricing .wrapper .plans .pb_upgrade_v1 {
  width: 290px;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  height: 50px;
  background-color: #3796c9;
  color: #fff;
  text-align: center;
  line-height: 50px;
  margin-top: 2px;
}
.frontpage .pricing .wrapper .plans .pb_upgrade_v1:hover {
  background-color: #5fb7e5;
  text-decoration: none;
}
.frontpage .pricing .wrapper .pb_title {
  text-align: center;
}
.frontpage .pricing .wrapper .more {
  font-size: 15px;
  font-weight: 500;
  color: #656565;
  text-align: center;
  padding-top: 20px;
}
.frontpage .pricing .wrapper .plan_adds {
  text-align: center;
  color: #656565;
  line-height: 24px;
  padding-bottom: 40px;
}
@media screen and (max-width: 930px) {
  .frontpage .pricing .wrapper .plans li {
    padding-bottom: 30px !important;
    display: block !important;
    margin-right: auto !important;
    margin: 0 auto;
  }
  .frontpage .pricing .wrapper .plans li .pb_upgrade {
    margin-left: auto !important;
    line-height: 22px;
  }
  .frontpage .pricing .wrapper .pb_pro {
    height: 400px !important;
  }
  .frontpage .pricing .wrapper .pb_pro .pop_features {
    height: 190px !important;
  }
  .frontpage .pricing .wrapper .pb_pro .pb_headline {
    padding: 0 20px 20px !important;
  }
}
.frontpage .logos.dark {
  background-color: #ebebee;
}
.frontpage .logos .wrapper {
  max-width: 952px;
  margin: 30px auto 0;
  padding: 0 30px;
  text-align: center;
}
.frontpage .logos .wrapper img {
  width: 100%;
}
@media only screen and (max-width: 960px) {
  .frontpage .logos .wrapper img {
    width: 90%;
  }
}
.frontpage .common-testimonials {
  max-width: 940px;
  margin: 0 auto;
  overflow: hidden;
  text-align: left;
}
.frontpage .main.testimonials-title {
  font-size: 29px;
  font-weight: 300;
  margin-bottom: 10px;
  text-align: center;
  margin-top: 10px;
}
.frontpage .testimonials-subtitle {
  font-size: 21px;
  text-align: center;
  padding-top: 0;
  margin-bottom: 20px;
  font-weight: 400;
}
.frontpage .common-testimonials {
  background: 0 0;
}
.frontpage .common-testimonials .row {
  margin-bottom: 0;
}
@media only screen and (min-width: 768px) {
  .frontpage .common-testimonials .row {
    margin-bottom: 40px;
  }
}
@media only screen and (min-width: 768px) {
  .frontpage .common-testimonials .row {
    border-bottom: 1px solid #ebebee;
  }
}
.frontpage .common-testimonials .row:last-of-type {
  border: none;
}
.frontpage .common-testimonials .col {
  width: calc(100% - 30px);
  border-bottom: 1px solid #ebebee;
  margin-bottom: 20px;
}
@media only screen and (min-width: 768px) {
  .frontpage .common-testimonials .col {
    width: calc(33.33% - 30px);
    border-bottom: 0;
    margin-bottom: 0;
  }
}
.frontpage .common-testimonials .col .testimonial .t-logo {
  height: 45px;
  margin-bottom: 25px;
}
.frontpage .common-testimonials .col .testimonial .t-logo img {
  height: 45px;
}
.frontpage .common-testimonials .col .testimonial p {
  color: #656565;
  font-size: 16px;
  font-style: italic;
  line-height: 29px;
  padding-bottom: 20px;
}
.frontpage .common-testimonials .col .testimonial .author img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  float: left;
  margin-right: 13px;
}
.frontpage .common-testimonials .col .testimonial .author p {
  color: #656565;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  margin-bottom: 5px;
}
.frontpage .common-testimonials .col .testimonial .author p span {
  font-size: 13px;
}
.frontpage .common-testimonials .col .testimonial.t-martin-currie .t-logo img {
  height: 70px;
  position: relative;
  top: -10px;
}
.frontpage .testimonials {
  width: 100%;
  padding-top: 60px;
  padding-bottom: 40px;
  background: linear-gradient(135.17deg, #7196af 0, #9b80b7 100%);
  overflow: hidden;
}
.frontpage .testimonials .wrapper {
  padding: 0 20px;
  text-align: center;
  max-width: 100%;
  margin: 0 auto;
}
@media only screen and (min-width: 1023px) {
  .frontpage .testimonials .wrapper {
    width: 80%;
    display: flex;
    overflow: hidden;
  }
}
.frontpage .testimonials .item {
  position: relative;
  display: block;
  padding: 24px 30px 84px 30px;
  width: calc(100% - 60px);
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  margin: 0 auto 20px;
  text-align: left;
}
@media only screen and (min-width: 1023px) {
  .frontpage .testimonials .item {
    width: 240px;
    margin: 0 10px 20px;
    display: inline-block;
    flex: 1;
  }
}
.frontpage .testimonials .item .text {
  font-weight: 400;
  font-size: 19px;
  font-style: italic;
  line-height: 29px;
  color: #fff;
  margin-bottom: 20px;
  word-wrap: break-word;
}
.frontpage .testimonials .item .author {
  position: absolute;
  bottom: 24px;
  width: calc(100% - 30px);
}
.frontpage .testimonials .item .author .image {
  float: left;
  max-width: 50px;
  margin-right: 12px;
}
.frontpage .testimonials .item .author .image img {
  width: 100%;
}
.frontpage .testimonials .item .author .data {
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  color: #fff;
}
.frontpage .testimonials .item .author .data a {
  position: relative;
  color: #4f6376;
  padding-left: 20px;
}
.frontpage .testimonials .item .author .data a:before {
  position: absolute;
  top: 4px;
  left: 0;
  content: "";
  width: 17px;
  height: 15px;
  background-image: url("../images/ico-testimonial-twitter.svg");
  background-size: 17px 15px;
  background-repeat: no-repeat;
}
.frontpage .demo {
  width: 100%;
  height: 360px;
  background: url("../images/demo.jpg") no-repeat;
  background-size: cover;
}
.frontpage .demo h2 {
  max-width: 470px;
  font-size: 29px;
  font-weight: 300;
  text-align: center;
  line-height: 34px;
  color: #fff;
  margin: 0 auto;
  padding: 100px 20px 30px;
}
.frontpage .demo a {
  display: block;
  width: 300px;
  font-size: 17px;
  border-radius: 4px;
  height: 50px;
  background-color: #3796c9;
  color: #fff;
  text-align: center;
  line-height: 47px;
  margin-bottom: 20px;
  transition: all 0.4s;
  margin: 0 auto;
}
.frontpage .demo a:hover {
  text-decoration: none;
  background: #5fb7e5;
}
.frontpage .why-use-infogram {
  overflow: hidden;
  max-width: 920px;
  margin: 0 auto;
  padding: 0 20px;
}
.frontpage .why-use-infogram .text-block {
  width: 100%;
  float: left;
  padding-bottom: 50px;
}
@media only screen and (min-width: 992px) {
  .frontpage .why-use-infogram .text-block {
    width: 50%;
  }
}
.frontpage .why-use-infogram .text-block h2 {
  color: #656565;
  font-size: 21px;
  font-weight: 500;
  line-height: 34px;
  margin-bottom: 6px;
}
.frontpage .why-use-infogram .text-block p {
  color: #656565;
  font-size: 15px;
  line-height: 27px;
  margin-bottom: 10px;
  font-weight: 400;
}
.frontpage .why-use-infogram .text-block ul {
  color: #656565;
  font-size: 15px;
  line-height: 27px;
  font-weight: 400;
}
.frontpage .premium_plans {
  position: relative;
  text-align: center;
  background: #184d6e;
}
.frontpage .premium_plans h3,
.frontpage .premium_plans h4 {
  color: #fff;
}
.frontpage .premium_plans h4 {
  padding-bottom: 40px;
}
.frontpage .premium_plans ul {
  color: #fff;
  list-style-type: none;
  padding: 0;
}
.frontpage .premium_plans ul li {
  width: 200px;
  display: inline-block;
  margin: 0 0 40px;
}
.frontpage .premium_plans ul li img {
  margin-bottom: 10px;
}
.frontpage .premium_plans .cta {
  min-width: 274px;
  border: 2px solid #fff;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  margin: 0 0 37px;
}
.frontpage .premium_plans .cta span {
  font-size: 17px;
  line-height: 17px;
  color: #fff;
  margin-top: 14px;
}
.frontpage .premium_plans .cta:hover {
  background: #fff;
}
.frontpage .premium_plans .cta:hover span {
  color: #184d6e;
}
.frontpage .premium_plans .download_screen {
  max-width: 732px;
  margin: 0 auto;
}
.frontpage .premium_plans .download_screen img {
  width: 100%;
}
.frontpage .search_wrap {
  position: relative;
  text-align: center;
}
.frontpage .search_wrap h4 {
  padding: 0 20px 40px;
}
.frontpage .search_wrap .search_box {
  max-width: 1012px;
  margin: 0 auto;
  text-align: center;
  padding-bottom: 39px;
}
.frontpage .search_wrap .search_title {
  font-size: 37px;
  line-height: 50px;
  font-weight: 100;
  padding: 27px 20px 0;
}
.frontpage .search_wrap .search_subtitle {
  display: block;
  font-size: 15px;
  line-height: 20px;
  font-weight: 500;
  padding: 18px 20px 28px;
}
.frontpage .search_wrap .search_area_holder {
  position: relative;
  height: 50px;
  background-color: #fff;
  border-radius: 4px;
  text-align: left;
}
.frontpage .search_wrap .search_area_holder.has_suggestions {
  border-radius: 4px 4px 4px 0;
}
.frontpage .search_wrap .has_suggestions .search_dropdown {
  display: block;
}
.frontpage .search_wrap .search_area {
  display: table;
  width: 100%;
}
.frontpage .search_wrap .inp_wrap {
  position: relative;
  display: table-cell;
  width: 100%;
}
.frontpage .search_wrap .search_field {
  font-size: 15px;
  font-weight: 500;
  height: 50px;
  color: #434343;
  font-family: Roboto, sans-serif;
  width: 100%;
  padding: 2px 8px 4px 14px;
  outline: 0;
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
}
.frontpage .search_wrap .search_btn {
  display: table-cell;
  min-width: 50px;
  height: 50px;
  font-size: 15px;
  color: #fff;
  text-align: center;
  border-radius: 0 4px 4px 0;
  background-color: #3796c9;
  cursor: pointer;
  line-height: 10px;
}
.frontpage .search_wrap .search_btn:hover {
  background-color: #5fb7e5;
}
.frontpage .search_wrap .search_btn img {
  width: 15px;
  height: 15px;
}
.frontpage .search_wrap .search_dropdown {
  display: none;
  position: absolute;
  top: 50px;
  left: -1px;
  right: 159px;
  width: 100%;
  max-height: 160px;
  border: 1px solid #dedede;
  border-radius: 0 0 4px 4px;
  overflow: auto;
}
.frontpage .search_wrap .search_suggestion {
  font-size: 15px;
  line-height: 21px;
  color: #434343;
  background-color: #fff;
  padding: 11px 15px 12px;
  cursor: pointer;
}
.frontpage .search_wrap .search_suggestion:hover {
  background-color: #3796c9;
  color: #fff;
}
.frontpage .search_wrap .search_igs {
  height: 400px;
  overflow: hidden;
  text-align: center;
}
@media only screen and (max-width: 600px) {
  .frontpage .search_wrap .search_igs {
    height: 210px;
  }
  .frontpage .search_wrap .search_igs img {
    width: 90%;
  }
}
@media only screen and (min-width: 599px) {
  .frontpage h2.search_title {
    font-size: 49px;
  }
}
@media only screen and (min-width: 650px) {
  .frontpage .search_area_holder {
    width: 300px;
    margin: 0 auto;
  }
}
@media only screen and (max-width: 400px) {
  .frontpage .search_field {
    position: relative;
    padding-left: 40px;
  }
  .frontpage .inp_wrap:after {
    display: block;
    content: "";
    position: absolute;
    top: 16px;
    left: 14px;
    width: 17px;
    height: 17px;
    background-image: url("../images/img_sprite_041c962b5d8949e88f86d73b288c8265.png");
    background-position: -139px -473px;
  }
  .frontpage .search_btn {
    display: none;
  }
}
.frontpage .quote_wrap {
  position: relative;
  background-color: #7196af;
  overflow: hidden;
  padding: 60px 0 20px;
  color: #fff;
}
.frontpage .quote_wrap .quote {
  position: relative;
  width: 100%;
}
.frontpage .quote_wrap .q_ico {
  width: 74px;
  height: 81px;
  margin: 0 auto 20px;
  background-image: url("../images/img_sprite_041c962b5d8949e88f86d73b288c8265.png");
  background-position: -104px -272px;
}
.frontpage .quote_wrap .quote p {
  font-size: 29px;
  line-height: 45px;
  font-weight: 300;
  padding: 20px 30px;
  text-align: center;
}
.frontpage .quote_wrap .q_author {
  width: 312px;
  margin: 36px auto 0;
  position: relative;
  left: 30px;
}
.frontpage .quote_wrap .qa_pic {
  float: left;
  margin-top: -10px;
  width: 70px;
  height: 70px;
  overflow: hidden;
}
.frontpage .quote_wrap .qa_pic img {
  height: 70px;
  width: 70px;
  position: relative;
  border-radius: 70px;
  background-color: #d7d7d7;
}
.frontpage .quote_wrap .qa_txt {
  float: left;
  width: 221px;
  padding: 4px 0 0 21px;
  text-align: left;
  color: #fff;
  font-size: 15px;
  line-height: 17px;
}
.frontpage .quote_wrap .q_nav {
  text-align: center;
  margin: 15px 0 15px;
}
.frontpage .quote_wrap .q_nav span {
  width: 16px;
  height: 16px;
  background-image: url("../images/img_sprite_041c962b5d8949e88f86d73b288c8265.png");
  background-position: -104px -133px;
}
.frontpage .quote_wrap .q_nav span.swiper-active-switch,
.frontpage .quote_wrap .q_nav span:hover {
  background-position: -122px -133px;
}
.frontpage .quote_wrap .swiper-pagination-switch {
  display: none;
}
.frontpage .quote_wrap {
  position: relative;
  width: 100%;
  margin: 0 auto;
}
.frontpage .quote_wrap .quote p {
  max-width: 960px;
  font-style: italic;
}
.frontpage .award {
  text-align: center;
  padding: 40px 0 20px;
  background-color: #476579;
  list-style-type: none;
}
.frontpage .award li {
  display: inline-block;
  width: 243px;
  height: 134px;
  margin: 0 20px 20px 20px;
  background-image: url("../images/flowers.png");
}
.frontpage .award li span {
  width: 100%;
  color: #434343;
}
.frontpage .award li .a_h2 {
  font-size: 17px;
  line-height: 20px;
  font-weight: 900;
  margin: 24px 0 7px;
  color: #fff;
}
.frontpage .award li .a_h3 {
  font-size: 13px;
  line-height: 20px;
  margin-bottom: 7px;
  color: #fff;
}
.frontpage .award li img {
  margin-top: 10px;
  height: 30px;
  width: auto;
}
.frontpage .award .quote_content {
  position: relative;
  width: 1020px;
  margin: 0 auto;
}
.frontpage .sales_team {
  max-width: 930px;
  margin: 0 auto;
  color: #656565;
  text-align: center;
}
.frontpage .sales_team .title {
  padding: 20px 0 10px;
  font-size: 29px;
  line-height: 34px;
  font-weight: 300;
}
.frontpage .sales_team .description {
  color: #656565;
  text-align: center;
  padding-bottom: 40px;
  line-height: 25px;
}
.frontpage .sales_team .sales_team_form .form_col {
  width: 290px;
  margin: 0 15px;
}
.frontpage .sales_team .sales_team_form .form_col.first {
  margin-left: 0;
}
.frontpage .sales_team .sales_team_form .form_col.last {
  margin-right: 0;
}
@media screen and (min-width: 930px) {
  .frontpage .sales_team .sales_team_form .form_col {
    float: left;
  }
}
@media screen and (max-width: 930px) {
  .frontpage .sales_team .sales_team_form .form_col {
    display: block;
    margin: 0 auto 20px !important;
  }
}
.frontpage .sales_team .sales_team_form label {
  font-size: 13px;
  text-align: left;
  display: block;
  padding-bottom: 5px;
}
.frontpage .sales_team .sales_team_form input[type="text"],
.frontpage .sales_team .sales_team_form textarea {
  display: block;
  box-sizing: border-box;
  border: 1px solid #7d7d7d;
  padding: 15px;
  font-size: 15px;
  border-radius: 4px;
  width: 100%;
  margin-bottom: 10px;
}
.frontpage .sales_team .sales_team_form textarea {
  height: 130px;
}
.frontpage .sales_team .sales_team_form .false {
  color: #ce2533;
  border-color: #ce2533 !important;
}
.frontpage .sales_team .sales_team_form .required {
  font-size: 13px;
  padding: 10px 0 20px;
}
.frontpage .sales_team .sales_team_form .submit {
  font-family: Roboto;
  width: 290px;
  font-size: 15px;
  border-radius: 4px;
  height: 50px;
  background-color: #3796c9;
  color: #fff;
  text-align: center;
  line-height: 50px;
  margin-bottom: 20px;
}
.frontpage .sales_team .sales_team_form .submit:hover {
  text-decoration: none;
  background: #5fb7e5;
}
.frontpage .sales_team .get_in_touch {
  line-height: 24px;
}
.frontpage .sales_team .form_success {
  display: none;
  background: #8ec3a7;
  padding: 80px 0;
  color: #fff;
  border-radius: 4px;
  margin-bottom: 40px;
}
.frontpage .sales_team .form_success .form_success_title {
  font-size: 35px;
  line-height: 25px;
  font-weight: 300;
  text-align: center;
  padding-bottom: 20px;
}
.frontpage .contact_sales_form,
.frontpage .education_form {
  width: 250px !important;
}
.frontpage .cb_inner {
  text-align: center;
}
.frontpage .use-cases {
  background: linear-gradient(135.17deg, #7196af 0, #9b80b7 100%);
}
.frontpage .use-cases-wrapper {
  max-width: 972px;
  margin: 0 auto 10px;
  overflow: hidden;
  padding: 60px 20px 40px;
}
.frontpage .use-cases-wrapper h2 {
  max-width: 640px;
  display: block;
  color: #fff;
  font-size: 29px;
  font-weight: 300;
  line-height: 38px;
  margin: 0 auto 30px;
  text-align: center;
}
.frontpage .use-cases-wrapper ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.frontpage .use-cases-wrapper ul li {
  border-radius: 4px;
  margin: 0 10px 20px;
  background-color: rgba(255, 255, 255, 0.2);
  text-align: center;
  flex: 0 0 auto;
  width: 140px;
  min-height: 220px;
  transition: 0.2s all ease;
}
.frontpage .use-cases-wrapper ul li:hover,
.frontpage .use-cases-wrapper ul li[focus-within] {
  background-color: rgba(255, 255, 255, 0.3);
}
.frontpage .use-cases-wrapper ul li:focus-within {
  background-color: rgba(255, 255, 255, 0.3);
}
@media only screen and (max-width: 480px) {
  .frontpage .use-cases-wrapper ul li {
    width: 120px;
  }
}
.frontpage .use-cases-wrapper ul a {
  text-decoration: none;
  color: #fff;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  height: 100%;
  padding: 25px 0;
  font-size: 15px;
  align-items: center;
  box-sizing: border-box;
}
.frontpage .use-cases-wrapper ul a span {
  font-size: 17px;
  font-weight: 500;
  line-height: 20px;
  flex-basis: 100%;
  align-self: flex-end;
}
.frontpage .use-cases-wrapper ul img {
  max-width: 76px;
  max-height: 80px;
}
.frontpage .use-case-testimonials {
  padding-top: 0;
  background: 0 0;
}
.frontpage .use-case-testimonials h2.main {
  text-align: center;
  color: #656565;
}
.frontpage .use-case-testimonials .item .author .data,
.frontpage .use-case-testimonials .item .text {
  color: #656565;
}
.frontpage .use-case-testimonials .item {
  background-color: #ebebee;
  border-radius: 4px;
  width: 80%;
}
@media only screen and (min-width: 1023px) {
  .frontpage .use-case-testimonials .item {
    width: 0;
  }
}
.frontpage .use-case-testimonials .item .image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #d8d8d8;
}
.frontpage .use-case-testimonials .author {
  max-width: 410px;
}
.frontpage .use-case-testimonials .author .data {
  padding-top: 3px;
}
@media only screen and (min-width: 1023px) {
  .frontpage .use-case-testimonials.reporting .item {
    min-height: 245px;
  }
}
.frontpage .use-case-testimonials.reporting .author .data {
  padding-top: 13px;
}
.frontpage .schedule-demo {
  width: 100%;
  min-height: 155px;
  background-image: url("../images/schedule_demo_bg.jpg");
  background-size: cover;
  margin-bottom: 50px;
  padding: 50px 0;
  text-align: center;
}
.frontpage .schedule-demo p {
  width: calc(100% - 40px);
  max-width: 688px;
  color: #fff;
  font-size: 21px;
  font-weight: 400;
  line-height: 29px;
  text-align: center;
  padding: 0 20px;
  margin: 0 auto 30px;
}
.frontpage .demo_customers {
  text-align: center;
  margin-bottom: 50px;
}
.frontpage .demo_customers .cta_default,
.frontpage .schedule-demo .cta_default {
  background-color: #ce2533;
  margin-top: 0;
  margin-bottom: 0;
}
.frontpage .demo_customers .cta_default:focus,
.frontpage .demo_customers .cta_default:hover,
.frontpage .schedule-demo .cta_default:focus,
.frontpage .schedule-demo .cta_default:hover {
  background-color: #de4854;
}
.body.frontpage .c_request {
  transition: 0.25s all ease-in-out;
}
.body.frontpage .c_request:focus,
.body.frontpage .c_request:hover {
  background-color: #de4854;
}
.frontpage--404 .header-404 {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.5);
  background: linear-gradient(267.77deg, #419bad 0, #4064b9 50%, #582e8d 100%);
  text-align: center;
}
.frontpage--404 .header-404__cta {
  margin-bottom: 10px;
}
.frontpage--404 .header-404__logo {
  width: 110px;
  height: 145px;
  margin: 0 auto;
  background-image: url("../images/404-sad-dark.svg");
}
.frontpage--404 .header-404__logo--happy {
  background-image: url("../images/404-happy-dark.svg");
}
.frontpage--404 .header-404__title {
  font-size: 29px;
  line-height: 34px;
  color: #fff;
  font-weight: 300;
  text-align: center;
  margin: 20px 0;
}
.frontpage--404 .header-404__subtitle {
  font-size: 15px;
  line-height: 20px;
  color: #fff;
  font-weight: 500;
  text-align: center;
  margin: 20px 0;
}
.frontpage--404--expired-link .top_line,
.frontpage--404--infographics .top_line {
  background: 0 0;
}
.frontpage--404--expired-link .top_line__logo,
.frontpage--404--infographics .top_line__logo {
  width: 145px;
}
.frontpage--404--expired-link .top_line__logo-link,
.frontpage--404--infographics .top_line__logo-link {
  display: block;
  margin-left: 18px;
  margin-top: 18px;
}
.frontpage--404--expired-link .header-404,
.frontpage--404--infographics .header-404 {
  background: 0 0;
}
.frontpage--404--expired-link .header-404__subtitle,
.frontpage--404--expired-link .header-404__title,
.frontpage--404--infographics .header-404__subtitle,
.frontpage--404--infographics .header-404__title {
  color: #656565;
}
.frontpage--webinars .login .menu_items.languages {
  display: none;
}
.frontpage--webinars .g-header {
  padding-bottom: 200px;
}
@media only screen and (max-width: 480px) {
  .frontpage--webinars .g-header {
    padding-bottom: 150px;
  }
}
.frontpage--webinars .webinars {
  position: relative;
  top: -120px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.frontpage--webinars .webinars .webinar {
  flex-basis: 49%;
  margin-top: 35px;
}
@media only screen and (max-width: 480px) {
  .frontpage--webinars .webinars .webinar {
    flex-basis: 100%;
  }
}
@media only screen and (min-width: 480px) {
  .frontpage--webinars .webinars .webinar:first-of-type,
  .frontpage--webinars .webinars .webinar:nth-of-type(2) {
    margin-top: 0;
  }
}
.frontpage--webinars .webinars .webinar__title {
  font-size: 24px;
  line-height: 32px;
  color: #656565;
  font-weight: 500;
  text-align: left;
  margin-top: 15px;
}
.frontpage--webinars .webinars .webinar__title a {
  color: #656565;
}
.frontpage--webinars .webinars .webinar__title a:hover {
  text-decoration: none;
  color: #9b9b9b;
}
.frontpage--webinars .webinars .webinar__description {
  font-size: 15px;
  line-height: 23px;
  font-weight: 400;
  margin: 10px 0 15px;
}
.frontpage--webinars .webinars .webinar__image-link {
  display: block;
}
.frontpage--webinars .webinar-single {
  position: relative;
  top: -120px;
}
.frontpage--webinars .webinar-single__image {
  max-width: 780px;
  display: block;
  margin: 0 auto;
}
@media only screen and (max-width: 767px) {
  .frontpage--webinars .webinar-single__image {
    max-width: 100%;
  }
}
.frontpage--webinars .webinar-single__cta span {
  position: relative;
  top: -2px;
}
.frontpage--webinars .webinar-single__back {
  display: inline-block;
  margin-top: 20px;
}
.frontpage--webinars .webinar-single__body {
  max-width: 720px;
  margin: 0 auto;
  padding: 30px 0;
  font-size: 16px;
  line-height: 29px;
  font-weight: 400;
}
.frontpage--webinars .webinar-single__body::after {
  display: block;
  content: "";
  clear: both;
}
.frontpage--webinars .webinar-single__body h2,
.frontpage--webinars .webinar-single__body h3,
.frontpage--webinars .webinar-single__body h4,
.frontpage--webinars .webinar-single__body h5,
.frontpage--webinars .webinar-single__body h6 {
  text-align: left;
  font-weight: 700;
  margin-bottom: 15px;
}
.frontpage--webinars .webinar-single__body ol,
.frontpage--webinars .webinar-single__body ul {
  padding-left: 18px;
}
.frontpage--webinars .webinar-single__body ol {
  list-style-type: decimal;
}
.frontpage--webinars .webinar-single__body ul {
  list-style-type: initial;
}
.frontpage--webinars .webinar-single__body h4 {
  font-size: 18px;
}
.frontpage--webinars .webinar-single__body p {
  font-size: inherit;
  font-weight: inherit;
}
.frontpage--webinars .webinar-single__body img {
  height: auto !important;
}
img.js-lazy-item {
  opacity: 0;
}
img.js-lazy-item[src$=".svg"] {
  opacity: initial;
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}
.team_page .team_header {
  background: url(../images/teams_landing.jpg) no-repeat 50% 50%;
  background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  -webkit-background-size: cover;
  display: block;
  height: 700px;
}
.team_page .pricing .pricing-block .pricing-plan .plan-title {
  min-height: initial;
}
.team_page .pricing .pricing-block .pricing-plan__info {
  margin-bottom: 10px;
}
.team_page .actions ul li img {
  width: auto;
  height: 140px;
  margin-bottom: 20px;
}
.team_page .actions ul li .descr {
  max-width: 250px;
  line-height: 23px;
}

ol,
ul {
  list-style: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.p-0 {
  padding: 0 !important;
}
.m-0 {
  margin: 0 !important;
}
.d-flex {
  display: flex;
}
.f-wrap {
  flex-wrap: wrap;
}
@media only screen and (max-width: 480px) {
  .hidden-phone {
    display: none;
  }
}
.g-header {
  padding: 120px 0 100px;
  background-image: url(../images/bg_header.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  text-align: center;
}
.g-header__title {
  font-weight: 300;
  font-size: 49px;
  line-height: 60px;
  max-width: 840px;
  margin: 0 auto;
}
@media only screen and (max-width: 480px) {
  .g-header__title {
    font-size: 42px;
    line-height: 48px;
  }
}
.g-header__description {
  font-size: 15px;
  font-weight: 500;
  line-height: 25px;
  max-width: 460px;
  margin: 20px auto 15px;
}
.g-header__embed {
  max-width: 700px;
  min-height: 470px;
  margin: 35px auto;
}
@media only screen and (max-width: 480px) {
  .g-header__embed {
    min-height: 280px;
  }
}
.g-header__embed .infogram-embed {
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.33);
  border-radius: 4px;
  background: rgba(71, 59, 107, 0.5);
}
.g-button {
  cursor: pointer;
  font-family: inherit;
  box-sizing: border-box;
  font-size: 15px;
  font-weight: 500;
  background-color: #3195cb;
  border-radius: 4px;
  width: 220px;
  color: #fff;
  height: 40px;
  line-height: 40px;
  transition: 0.25s all cubic-bezier(0.165, 0.84, 0.44, 1);
}
.g-button:active,
.g-button:focus,
.g-button:hover {
  color: #fff;
  text-decoration: none;
  background-color: #58aad7;
}
.g-button--accent {
  background-color: #ce2533;
}
.g-button--accent:active,
.g-button--accent:focus,
.g-button--accent:hover {
  background-color: #de4854;
  color: #fff;
}
.g-button--large {
  width: 300px;
  height: 50px;
  line-height: 47px;
  font-size: 17px;
  box-sizing: border-box;
  padding: 0 20px;
}
@media screen and (max-width: 360px) {
  .g-button--large {
    width: 100%;
    font-size: 15px;
  }
}
.g-button--transparent {
  border: 1px solid #fff;
  background-color: transparent;
}
.g-button--transparent:active,
.g-button--transparent:focus,
.g-button--transparent:hover {
  background-color: #fff;
  color: #656565;
}
.g-button--gray {
  border: 2px solid #656565;
  color: #656565;
  background-color: transparent;
}
.g-button--gray:active,
.g-button--gray:focus,
.g-button--gray:hover {
  color: #fff;
  background-color: #656565;
}
.g-button--centered {
  display: block;
  margin: 0 auto;
}
.g-link {
  color: #3796c9;
}
.g-link:hover {
  color: #3896c9;
}
.g-paragraph {
  font-weight: 500;
  color: #656565;
  font-size: 15px;
  line-height: 25px;
}
.fade-in {
  opacity: 0;
  -webkit-animation: fadeIn ease-in 1;
  -moz-animation: fadeIn ease-in 1;
  animation: fadeIn ease-in 1;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 0.6s;
  -moz-animation-duration: 0.6s;
  animation-duration: 0.6s;
}
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-moz-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
img {
  max-width: 100%;
}
a {
  color: #3195cb;
}
a:hover {
  color: #58aad7;
}
.frontpage .section {
  padding: 50px 0;
}
.frontpage .section .container {
  max-width: 1000px;
  box-sizing: border-box;
  margin: 0 auto;
}
@media only screen and (max-width: 1199px) {
  .frontpage .section .container {
    padding: 0 20px;
  }
}
.frontpage .section--gray {
  background-color: #ebebee;
}
.frontpage .section--dark {
  color: #fff;
}
.frontpage .section--light {
  color: #656565;
}
.frontpage .section__paragraph {
  font-size: 15px;
  line-height: 25px;
  color: inherit;
  font-weight: 400;
}
.frontpage .section__title {
  color: inherit;
  font-size: 29px;
  font-weight: 300;
  line-height: 34px;
}
.frontpage .section__description {
  color: inherit;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  padding-top: 0;
  margin-top: 20px;
  margin-bottom: 15px;
}
.accordion-toggle {
  display: block;
}
.accordion-content {
  display: none;
}
.accordion-content.is-collapsed {
  display: block;
}
.frontpage--landings h1 {
  font-weight: 300;
  font-size: 49px;
  line-height: 57px;
  max-width: 750px;
  margin: 0 auto;
}
.frontpage--landings h3 {
  width: 100%;
  font-size: 13px;
  line-height: 13px;
  margin-top: 20px;
  margin-bottom: 10px;
  padding-bottom: 5px;
}
.frontpage--landings .top_line {
  background-color: transparent;
}
.frontpage--landings .landings__header {
  background: url("../images/shapes.svg"),
    url("../images/landing_header_bg.jpg");
  background-position: center 130px, center;
  background-repeat: no-repeat, no-repeat;
  background-size: cover;
  display: block;
  box-sizing: border-box;
  padding: 120px 0 80px;
  color: #fff;
}
@media only screen and (max-width: 600px) {
  .frontpage--landings .landings__header {
    background: url("../images/landing_header_bg.jpg");
  }
}
.frontpage--landings .landings__header-description {
  font-weight: 500;
  font-size: 15px;
  line-height: 25px;
  color: #fff;
  max-width: 620px;
  margin: 15px auto 20px;
}
.frontpage--landings .landings__header-cta-button {
  font-weight: 500;
  font-size: 15px;
  line-height: 20px;
  min-width: 248px;
  height: 40px;
  color: #fff;
  background-color: #ce2533;
  border-radius: 4px;
  cursor: pointer;
  margin: 0 0 18px;
  position: relative;
  transition: 0.4s all cubic-bezier(0.165, 0.84, 0.44, 1);
}
.frontpage--landings .landings__header-cta-button:hover {
  background-color: #dc5356;
}
.frontpage--landings .landings__header-cta-button span {
  font-weight: 500;
  font-size: 15px;
  line-height: 20px;
  padding: 10px 0;
}
.frontpage--landings .landings__header-review {
  font-size: 11px;
  color: #fff;
  opacity: 0.8;
  display: flex;
  align-items: center;
  width: 200px;
  margin: 0 auto 20px;
  text-decoration: none;
}
.frontpage--landings .landings__header-review:hover {
  opacity: 1;
}
.frontpage--landings .landings__header-review .stars {
  position: relative;
}
.frontpage--landings .landings__header-review .stars__star {
  font-size: 16px;
  width: 80px;
  height: 20px;
  background: url("../images/stars-empty-set.svg") left center no-repeat;
}
.frontpage--landings .landings__header-review .stars__full {
  display: inline-block;
  position: absolute;
  overflow-x: hidden;
  background: url("../images/stars-full-set.svg") left center no-repeat;
  left: 0;
  top: 0;
}
.frontpage--landings .landings__header-review .score {
  margin: 0 15px 0 7px;
}
.frontpage--landings .landings__header-review .count {
  width: 100%;
}
.frontpage--landings .landings__header--with-hero-image {
  padding: 120px 0 40px;
}
.frontpage--landings .landings__content__body {
  max-width: 780px;
  font-weight: 300;
  font-size: 29px;
  color: #656565;
  line-height: 36px;
  margin: 40px auto 10px;
}
.frontpage--landings .landings__content__body p {
  font-size: 16px;
  line-height: 29px;
  margin-bottom: 20px;
  --x-height-multiplier: 0.35;
  --baseline-multiplier: 0.179;
  font-weight: 400;
  font-style: normal;
  letter-spacing: -0.003em;
  color: #656565;
}
.frontpage--landings .landings__content__body h2 {
  font-size: 29px;
  line-height: 36px;
  text-align: left;
  padding: 0;
  margin: 40px 0 20px;
  font-weight: 400;
}
.frontpage--landings .landings__content__body h4 {
  width: 100%;
  font-weight: 500;
  font-size: 21px;
  line-height: 30px;
  text-align: left;
  color: #656565;
}
.frontpage--landings .landings__content__body ol {
  margin-bottom: 20px;
  line-height: 29px;
  font-size: 16px;
  padding-left: 20px;
}
.frontpage--landings .landings__content__body ol li {
  font-weight: 400;
  margin-bottom: 10px;
}
.frontpage--landings .landings__content__body ol li p {
  margin-bottom: 0;
}
.frontpage--landings .landings__content__body ul {
  margin-bottom: 20px;
  line-height: 29px;
  font-size: 16px;
}
.frontpage--landings .landings__content__body ul li {
  position: relative;
  padding-left: 22px;
  font-weight: 400;
  margin-bottom: 10px;
}
.frontpage--landings .landings__content__body ul li p {
  margin-bottom: 0;
}
.frontpage--landings .landings__content__body ul li:before {
  position: absolute;
  left: 0;
  content: "•";
  color: #b2b2b2;
  font-size: 40px;
  box-sizing: border-box;
}
.frontpage--landings .landings__content__body iframe {
  max-width: 100%;
}
.frontpage--landings .landings__content__body .description {
  max-width: 780px;
  margin: 0 auto 40px;
  line-height: 30px;
}
.frontpage--landings .landings__content .infographic_types {
  width: 100%;
  background: linear-gradient(
    117.76deg,
    #a3649f 14.61%,
    #5869b4 58.95%,
    #a2f69f 110.33%
  );
}
.frontpage--landings
  .landings__content
  .infographic_types
  .section__description,
.frontpage--landings .landings__content .infographic_types .section__title {
  color: #fff;
}
.frontpage--landings .landings__content .infographic_types__content {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
}
.frontpage--landings .landings__content .infographic_types__content__card {
  flex-basis: 300px;
  background: #fff;
  box-shadow: 0 4px 7px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  margin-bottom: 40px;
  padding: 20px 0 60px;
  margin-right: 20px;
  position: relative;
}
.frontpage--landings .landings__content .infographic_types__content__card > * {
  flex-basis: 100%;
}
@media only screen and (max-width: 767px) {
  .frontpage--landings .landings__content .infographic_types__content__card {
    flex-basis: calc(50% - 20px);
  }
}
.frontpage--landings
  .landings__content
  .infographic_types__content__card:hover {
  text-decoration: none;
}
.frontpage--landings
  .landings__content
  .infographic_types__content__card__title {
  font-weight: 500;
  font-size: 17px;
  line-height: 20px;
  margin-bottom: 12px;
}
.frontpage--landings
  .landings__content
  .infographic_types__content__card__image {
  max-width: 185px;
  min-height: 130px;
  flex-basis: auto;
  align-self: flex-start;
}
.frontpage--landings
  .landings__content
  .infographic_types__content__card__description {
  padding: 15px 27px 0;
  font-weight: 500;
  font-size: 13px;
  line-height: 19px;
  text-align: left;
}
.frontpage--landings
  .landings__content
  .infographic_types__content__card__button {
  width: 80%;
  left: 50%;
  margin: 0 auto;
  margin-top: 20px;
  margin-left: -40%;
  bottom: 10px;
  position: absolute;
  background: #3195cb;
  border-radius: 4px;
  font-weight: 500;
  font-size: 13px;
  line-height: 15px;
  padding: 8px 0;
  color: #fff;
}
.frontpage--landings
  .landings__content
  .infographic_types__content__card:hover
  .infographic_types__content__card__button,
.frontpage--landings
  .landings__content
  .infographic_types__content__card__button:hover {
  background-color: #5fb7e5;
}
.frontpage--landings .landings__content .landings__testimonials {
  background: #fff;
}
.frontpage--landings
  .landings__content
  .landings__testimonials
  .common-testimonials
  .row {
  margin-bottom: 0;
}
.frontpage--landings .landings__content .landings__testimonials__content {
  max-width: 940px;
  margin: auto;
  text-align: center;
}
@media only screen and (max-width: 1199px) {
  .frontpage--landings .landings__content .landings__testimonials__content {
    width: calc(100% - 60px);
  }
}
.frontpage--landings .landings__content .landings__faq,
.frontpage--landings .landings__content .landings__templates {
  background: #f5f5f5;
}
.frontpage--landings .landings__content .landings__templates__content {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template {
  margin-right: 10px;
  margin-bottom: 47px;
  width: 180px;
  position: relative;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.infographic_freeLayout {
  width: 180px;
  height: 292px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.report {
  width: 180px;
  height: 252px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.slides {
  width: 227px;
  height: 130px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.dashboards {
  width: 227px;
  height: 130px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.poster {
  width: 180px;
  height: 254px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.singleMap {
  width: 180px;
  height: 180px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.singleMap
  .thumbnail {
  background-color: #fff;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.singleChart {
  width: 180px;
  height: 127px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.email_header {
  width: 250px;
  height: 125px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.youtube_thumbnail {
  width: 250px;
  height: 140px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.social_media {
  width: 180px;
  height: 180px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.instagram_post {
  width: 180px;
  height: 180px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.linkedin_post {
  width: 230px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.twitter_post {
  width: 230px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.facebook_post {
  width: 216px;
  height: 180px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template.pinterest_post {
  width: 180px;
  height: 270px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template
  .thumbnail {
  border-radius: 4px;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template
  .thumbnail
  img {
  border-radius: 4px;
  width: 100%;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template
  .overlay {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  z-index: 5;
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template
  .overlay__button {
  display: none;
  cursor: pointer;
  z-index: 10;
  color: #fff;
  border-radius: 4px;
  text-align: center;
  line-height: 39px;
  background: #3195cb;
  min-width: 140px;
  height: 40px;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template
  .overlay:hover {
  background-color: rgba(19, 79, 111, 0.7);
  border-radius: 4px;
  border-width: 2px;
  border-style: solid;
  border-color: #3195cb;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template
  .overlay:hover
  .overlay__button {
  display: block;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template
  .overlay:hover
  .overlay__button:hover {
  background: #58aad7;
  text-decoration: none;
}
.frontpage--landings
  .landings__content
  .landings__templates__content
  .template
  .name {
  text-align: center;
  font-size: 13px;
  margin-top: 10px;
}
.frontpage--landings .landings__content .landings__templates__subcategory {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
@media only screen and (max-width: 1199px) {
  .frontpage--landings .landings__content .landings__examples {
    width: calc(100% - 60px);
    margin: auto;
  }
}
@media only screen and (max-width: 767px) {
  .frontpage--landings .landings__content .landings__examples {
    flex-wrap: wrap;
  }
}
.frontpage--landings .landings__content .landings__examples .items {
  transition: 0.4s all cubic-bezier(0.165, 0.84, 0.44, 1);
  columns: 4;
  column-gap: 15px;
}
.frontpage--landings .landings__content .landings__examples .items.charts,
.frontpage--landings
  .landings__content
  .landings__examples
  .items.instagram-posts,
.frontpage--landings
  .landings__content
  .landings__examples
  .items.youtube-thumbnails {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
@media only screen and (min-width: 768px) and (max-width: 979px) {
  .frontpage--landings .landings__content .landings__examples .items {
    columns: 3;
  }
}
@media only screen and (max-width: 767px) {
  .frontpage--landings .landings__content .landings__examples .items {
    flex-basis: 100%;
    columns: 3;
  }
}
@media only screen and (max-width: 480px) {
  .frontpage--landings .landings__content .landings__examples .items {
    columns: 2;
  }
}
.frontpage--landings .landings__content .landings__examples .items .item {
  page-break-inside: avoid;
  break-inside: avoid;
  padding: 5px;
  border-radius: 4px;
  text-align: center;
  margin-bottom: 10px;
  transition: all 0.4s ease;
}
@media only screen and (max-width: 480px) {
  .frontpage--landings .landings__content .landings__examples .items .item {
    margin-bottom: 20px;
    padding: 10px;
  }
}
.frontpage--landings .landings__content .landings__examples .items .item:hover,
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item[focus-within] {
  background: #ebebee;
}
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item:hover
  a,
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item[focus-within]
  a {
  text-decoration: none;
}
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item:focus-within {
  background: #ebebee;
}
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item:focus-within
  a {
  text-decoration: none;
}
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item__thumb {
  position: relative;
}
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item__thumb::after {
  content: " ";
  background: rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 5;
}
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item__thumb
  img {
  max-width: 100%;
  border-radius: 4px;
}
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item__title {
  margin-top: 10px;
  text-align: left;
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  color: #656565;
}
.frontpage--landings
  .landings__content
  .landings__examples
  .items
  .item__author {
  text-align: left;
  opacity: 0.6;
  font-size: 13px;
  font-style: italic;
  line-height: 20px;
  color: #656565;
  font-weight: 400;
}
.frontpage--landings .landings__content .landings__faq .question-wrapper {
  max-width: 780px;
  text-align: left;
  margin: auto;
  padding: 18px 0;
  border-bottom: 1px solid #d8d8d8;
}
@media only screen and (max-width: 1199px) {
  .frontpage--landings .landings__content .landings__faq .question-wrapper {
    width: calc(100% - 60px);
    margin: auto;
  }
}
.frontpage--landings
  .landings__content
  .landings__faq
  .question-wrapper
  .question {
  font-weight: 500;
  font-size: 19px;
  line-height: 25px;
}
.frontpage--landings
  .landings__content
  .landings__faq
  .question-wrapper
  .question
  .arrow {
  background-image: url("../images/arrow-down-dark.svg");
  width: 14px;
  height: 9px;
  margin: 9px 0 0 5px;
}
.frontpage--landings
  .landings__content
  .landings__faq
  .question-wrapper
  .question.is-collapsed
  .arrow {
  transform: rotate(-180deg);
}
.frontpage--landings
  .landings__content
  .landings__faq
  .question-wrapper
  .question:hover {
  color: #979797;
  cursor: pointer;
}
.frontpage--landings
  .landings__content
  .landings__faq
  .question-wrapper
  .question:hover
  .arrow {
  opacity: 0.6;
}
.frontpage--landings
  .landings__content
  .landings__faq
  .question-wrapper
  .answer {
  margin-top: 15px;
  font-weight: 400;
  font-size: 16px;
  line-height: 29px;
}
.frontpage--landings .landings__content .landings__faq .no-answer {
  font-weight: 500;
  font-size: 17px;
  line-height: 25px;
  text-align: center;
  margin: 32px 0 0;
}
.frontpage--landings .landings__content .landings__faq .no-answer a {
  color: #3195cb;
  text-decoration: none;
}
.frontpage--landings .landings__signup-block {
  text-align: center;
  background: #fff;
  padding: 40px 0;
}
.frontpage--landings .landings__signup-block .section__title {
  max-width: 900px;
  margin: 0 auto 20px;
}
@media only screen and (max-width: 600px) {
  .frontpage--landings .landings__signup-block .section__title {
    font-size: 24px;
  }
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}

ol,
ul {
  list-style: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.p-0 {
  padding: 0 !important;
}
.m-0 {
  margin: 0 !important;
}
.d-flex {
  display: flex;
}
.f-wrap {
  flex-wrap: wrap;
}
@media only screen and (max-width: 480px) {
  .hidden-phone {
    display: none;
  }
}
.g-header {
  padding: 120px 0 100px;
  background-image: url(../images/bg_header.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  text-align: center;
}
.g-header__title {
  font-weight: 300;
  font-size: 49px;
  line-height: 60px;
  max-width: 840px;
  margin: 0 auto;
}
@media only screen and (max-width: 480px) {
  .g-header__title {
    font-size: 42px;
    line-height: 48px;
  }
}
.g-header__description {
  font-size: 15px;
  font-weight: 500;
  line-height: 25px;
  max-width: 460px;
  margin: 20px auto 15px;
}
.g-header__embed {
  max-width: 700px;
  min-height: 470px;
  margin: 35px auto;
}
@media only screen and (max-width: 480px) {
  .g-header__embed {
    min-height: 280px;
  }
}
.g-header__embed .infogram-embed {
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.33);
  border-radius: 4px;
  background: rgba(71, 59, 107, 0.5);
}
.g-button {
  cursor: pointer;
  font-family: inherit;
  box-sizing: border-box;
  font-size: 15px;
  font-weight: 500;
  background-color: #3195cb;
  border-radius: 4px;
  width: 220px;
  color: #fff;
  height: 40px;
  line-height: 40px;
  transition: 0.25s all cubic-bezier(0.165, 0.84, 0.44, 1);
}
.g-button:active,
.g-button:focus,
.g-button:hover {
  color: #fff;
  text-decoration: none;
  background-color: #58aad7;
}
.g-button--accent {
  background-color: #ce2533;
}
.g-button--accent:active,
.g-button--accent:focus,
.g-button--accent:hover {
  background-color: #de4854;
  color: #fff;
}
.g-button--large {
  width: 300px;
  height: 50px;
  line-height: 47px;
  font-size: 17px;
  box-sizing: border-box;
  padding: 0 20px;
}
@media screen and (max-width: 360px) {
  .g-button--large {
    width: 100%;
    font-size: 15px;
  }
}
.g-button--transparent {
  border: 1px solid #fff;
  background-color: transparent;
}
.g-button--transparent:active,
.g-button--transparent:focus,
.g-button--transparent:hover {
  background-color: #fff;
  color: #656565;
}
.g-button--gray {
  border: 2px solid #656565;
  color: #656565;
  background-color: transparent;
}
.g-button--gray:active,
.g-button--gray:focus,
.g-button--gray:hover {
  color: #fff;
  background-color: #656565;
}
.g-button--centered {
  display: block;
  margin: 0 auto;
}
.g-link {
  color: #3796c9;
}
.g-link:hover {
  color: #3896c9;
}
.g-paragraph {
  font-weight: 500;
  color: #656565;
  font-size: 15px;
  line-height: 25px;
}
.fade-in {
  opacity: 0;
  -webkit-animation: fadeIn ease-in 1;
  -moz-animation: fadeIn ease-in 1;
  animation: fadeIn ease-in 1;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 0.6s;
  -moz-animation-duration: 0.6s;
  animation-duration: 0.6s;
}
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-moz-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
img {
  max-width: 100%;
}
a {
  color: #3195cb;
}
a:hover {
  color: #58aad7;
}
.frontpage .section {
  padding: 50px 0;
}
.frontpage .section .container {
  max-width: 1000px;
  box-sizing: border-box;
  margin: 0 auto;
}
@media only screen and (max-width: 1199px) {
  .frontpage .section .container {
    padding: 0 20px;
  }
}
.frontpage .section--gray {
  background-color: #ebebee;
}
.frontpage .section--dark {
  color: #fff;
}
.frontpage .section--light {
  color: #656565;
}
.frontpage .section__paragraph {
  font-size: 15px;
  line-height: 25px;
  color: inherit;
  font-weight: 400;
}
.frontpage .section__title {
  color: inherit;
  font-size: 29px;
  font-weight: 300;
  line-height: 34px;
}
.frontpage .section__description {
  color: inherit;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  padding-top: 0;
  margin-top: 20px;
  margin-bottom: 15px;
}
.frontpage .a-header {
  background: url("../images/about_bg_header.jpg") 50% 50% no-repeat;
  background-size: cover;
  background-position: center;
  min-height: 492px;
  box-sizing: border-box;
  text-align: center;
  color: #fff;
}
@media only screen and (min-width: 1200px) {
  .frontpage .a-header {
    min-height: 625px;
  }
}
@media only screen and (max-width: 480px) {
  .frontpage .a-header {
    min-height: 325px;
  }
}
.frontpage .a-features h2 {
  color: #656565;
  font-size: 49px;
  font-weight: 300;
  line-height: 57px;
  text-align: center;
  margin-bottom: 32px;
}
.frontpage .a-features h3 {
  color: #656565;
  font-size: 21px;
  font-style: italic;
  line-height: 34px;
  text-align: center;
  font-weight: 400;
  /*margin-bottom: 34px;*/
}
.frontpage .a-marriage {
  padding: 70px 0;
  background-color: #ebebee;
}
.frontpage .a-marriage__logo {
  max-height: 70px;
}
.frontpage .a-marriage__heading {
  font-size: 29px;
  font-weight: 300;
  line-height: 34px;
  text-align: center;
  color: #656565;
  margin-top: 20px;
  margin-bottom: 15px;
}
.frontpage .a-marriage__subheading {
  padding: 0;
  font-size: 15px;
  line-height: 23px;
  font-weight: 400;
  color: #656565;
}
.frontpage .a-clients {
  padding-top: 50px;
  padding-bottom: 60px;
}
.frontpage .a-clients__heading {
  font-size: 29px;
  font-weight: 300;
  line-height: 34px;
  margin-bottom: 30px;
  color: #656565;
}
.frontpage .a-clients__logo {
  width: 100%;
}
.frontpage .a-careers {
  padding: 145px 0;
  background-image: url("../images/bg_careers.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  color: #fff;
}
.frontpage .a-careers__heading {
  font-size: 29px;
  font-weight: 300;
  line-height: 40px;
  margin-bottom: 20px;
}
.frontpage .a-presskit {
  color: #656565;
}
.frontpage .a-presskit__heading {
  font-size: 17px;
  font-weight: 500;
  line-height: 20px;
}
.frontpage .a-presskit__logo {
  display: block;
  height: 85px;
  margin: 20px auto;
}
.member:not(:last-child) {
  margin-bottom: 30px;
}
.member__img {
  width: 140px;
  margin-bottom: 10px;
}
.member__fullname {
  font-size: 21px !important;
  line-height: 25px !important;
  margin-bottom: 15px;
  font-weight: 300 !important;
  color: inherit !important;
}
.member__position {
  font-size: 15px !important;
  line-height: 25px !important;
  margin-bottom: 17px;
  font-style: italic;
  font-weight: 400 !important;
  color: inherit !important;
}
.member__description {
  padding: 0 5px;
  color: inherit;
}
.member--dark {
  color: #fff;
}
@media only screen and (max-width: 767px) {
  .member {
    flex-basis: 50% !important;
  }
}
@media only screen and (max-width: 480px) {
  .member {
    flex-basis: 100% !important;
  }
}
.a-award:not(:last-child) {
  margin-bottom: 30px;
}
.a-infogram-video {
  margin-top: 40px;
}
.i-feature {
  flex: 1 0 29%;
  margin: 0 2%;
  color: #656565;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.i-feature > * {
  flex-basis: 100%;
}
.i-feature__stat {
  font-size: 49px;
  font-weight: 300;
  line-height: 57px;
  margin-bottom: 15px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}
.i-feature__stat > * {
  flex-basis: 100%;
  min-width: 100%;
}
.i-feature__stat small {
  display: block;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  min-height: 21px;
}
.i-feature__descr {
  font-size: 15px;
  line-height: 23px;
  font-weight: 400;
}
@media only screen and (max-width: 767px) {
  .i-feature {
    flex-basis: 100%;
  }
}

ol,
ul {
  list-style: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.p-0 {
  padding: 0 !important;
}
.m-0 {
  margin: 0 !important;
}
.d-flex {
  display: flex;
}
.f-wrap {
  flex-wrap: wrap;
}
@media only screen and (max-width: 480px) {
  .hidden-phone {
    display: none;
  }
}
.g-header {
  padding: 120px 0 100px;
  background-image: url(../images/bg_header.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  text-align: center;
}
.g-header__title {
  font-weight: 300;
  font-size: 49px;
  line-height: 60px;
  max-width: 840px;
  margin: 0 auto;
}
@media only screen and (max-width: 480px) {
  .g-header__title {
    font-size: 42px;
    line-height: 48px;
  }
}
.g-header__description {
  font-size: 15px;
  font-weight: 500;
  line-height: 25px;
  max-width: 460px;
  margin: 20px auto 15px;
}
.g-header__embed {
  max-width: 700px;
  min-height: 470px;
  margin: 35px auto;
}
@media only screen and (max-width: 480px) {
  .g-header__embed {
    min-height: 280px;
  }
}
.g-header__embed .infogram-embed {
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.33);
  border-radius: 4px;
  background: rgba(71, 59, 107, 0.5);
}
.g-button {
  cursor: pointer;
  font-family: inherit;
  box-sizing: border-box;
  font-size: 15px;
  font-weight: 500;
  background-color: #3195cb;
  border-radius: 4px;
  width: 220px;
  color: #fff;
  height: 40px;
  line-height: 40px;
  transition: 0.25s all cubic-bezier(0.165, 0.84, 0.44, 1);
}
.g-button:active,
.g-button:focus,
.g-button:hover {
  color: #fff;
  text-decoration: none;
  background-color: #58aad7;
}
.g-button--accent {
  background-color: #ce2533;
}
.g-button--accent:active,
.g-button--accent:focus,
.g-button--accent:hover {
  background-color: #de4854;
  color: #fff;
}
.g-button--large {
  width: 300px;
  height: 50px;
  line-height: 47px;
  font-size: 17px;
  box-sizing: border-box;
  padding: 0 20px;
}
@media screen and (max-width: 360px) {
  .g-button--large {
    width: 100%;
    font-size: 15px;
  }
}
.g-button--transparent {
  border: 1px solid #fff;
  background-color: transparent;
}
.g-button--transparent:active,
.g-button--transparent:focus,
.g-button--transparent:hover {
  background-color: #fff;
  color: #656565;
}
.g-button--gray {
  border: 2px solid #656565;
  color: #656565;
  background-color: transparent;
}
.g-button--gray:active,
.g-button--gray:focus,
.g-button--gray:hover {
  color: #fff;
  background-color: #656565;
}
.g-button--centered {
  display: block;
  margin: 0 auto;
}
.g-link {
  color: #3796c9;
}
.g-link:hover {
  color: #3896c9;
}
.g-paragraph {
  font-weight: 500;
  color: #656565;
  font-size: 15px;
  line-height: 25px;
}
.fade-in {
  opacity: 0;
  -webkit-animation: fadeIn ease-in 1;
  -moz-animation: fadeIn ease-in 1;
  animation: fadeIn ease-in 1;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 0.6s;
  -moz-animation-duration: 0.6s;
  animation-duration: 0.6s;
}
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-moz-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
img {
  max-width: 100%;
}
a {
  color: #3195cb;
}
a:hover {
  color: #58aad7;
}
.frontpage .section {
  padding: 50px 0;
}
.frontpage .section .container {
  max-width: 1000px;
  box-sizing: border-box;
  margin: 0 auto;
}
@media only screen and (max-width: 1199px) {
  .frontpage .section .container {
    padding: 0 20px;
  }
}
.container h3 {
	margin-top: 10px;
	margin-bottom: 10px;
}

.frontpage .section--gray {
  background-color: #ebebee;
}
.frontpage .section--dark {
  color: #fff;
}
.frontpage .section--light {
  color: #656565;
}
.frontpage .section__paragraph {
  font-size: 15px;
  line-height: 25px;
  color: inherit;
  font-weight: 400;
}
.frontpage .section__title {
  color: inherit;
  font-size: 29px;
  font-weight: 300;
  line-height: 34px;
}
.frontpage .section__description {
  color: inherit;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  padding-top: 0;
  margin-top: 20px;
  margin-bottom: 15px;
}
.features .use-cases-wrapper {
  margin-bottom: 0;
}
.features .infographic:not(.no-shadow) .infogram-embed {
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.33);
  border-radius: 4px;
}
.features .top_line {
  background: 0 0;
}
.features .top_line.accent .menu_items a.has-submenu .submenu,
.features .top_line.accent .menu_items div.has-submenu .submenu {
  background: #3f7c6b !important;
}
.features .top_line.accent .menu_items a.has-submenu .submenu a:hover,
.features .top_line.accent .menu_items div.has-submenu .submenu a:hover {
  background: #5ba38d !important;
}
.features .feature_header {
  background: url("../images/features_landing.jpg") no-repeat 50% 50%;
  background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  -webkit-background-size: cover;
  display: block;
  height: 380px;
}
.features .feature_header .p_head .join_now {
  margin: 20px 0;
  display: inline-block;
}
.features .items {
  padding: 30px 0;
}
.features .items .item {
  width: 100%;
  overflow: hidden;
}
.features .items .item .content {
  max-width: 1160px;
  padding: 0 20px;
  margin: 0 auto;
}
.features .items .item .right {
  float: right;
}
.features .items .item .left {
  float: left;
}
.features .items .item .button {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 220px;
  height: 50px;
  background-color: #ce2533;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.1s ease-out;
  text-decoration: none;
}
.features .items .item .button:focus,
.features .items .item .button:hover {
  background-color: #dc5356;
}
.features .items .item .button span {
  font-size: 15px;
  color: #fff;
  transition: color 0.1s ease-out;
}
.features .items .item .button-white {
  width: 220px;
  height: 40px;
  border: 2px solid #fff;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  line-height: 40px;
  color: #fff;
  text-decoration: none;
}
.features .items .item .image {
  width: 100%;
}
@media only screen and (min-width: 800px) {
  .features .items .item .image {
    width: 60%;
  }
}
.features .items .item .image img {
  width: 100%;
}
.features .items .item .text {
  width: 100%;
  color: #656565;
  text-align: center;
}
@media only screen and (min-width: 800px) {
  .features .items .item .text {
    width: 40%;
    text-align: left;
  }
}
.features .items .item .text.full {
  width: 100%;
  text-align: center;
}
.features .items .item .text.full p {
  margin: 0 auto 45px;
  max-width: 600px;
}
.features .items .item .text h2 {
  font-size: 29px;
  font-weight: 300;
  line-height: 39px;
  margin-bottom: 15px;
}
.features .items .item .text p {
  font-size: 15px;
  font-weight: 400;
  line-height: 25px;
  margin-bottom: 20px;
}
@media only screen and (min-width: 800px) {
  .features .items .item .text p {
    max-width: 380px;
  }
}
.features .items .item1 .image-absolute {
  position: relative;
  left: 0;
  width: 100%;
  margin-left: -20px;
}
@media only screen and (min-width: 800px) {
  .features .items .item1 .image-absolute {
    position: absolute;
    width: 45%;
    margin-left: auto;
  }
}
.features .items .item1 .image-absolute img {
  width: 100%;
}
.features .items .item1 .text {
  width: 100%;
  padding: 0 0 60px;
}
@media only screen and (min-width: 800px) {
  .features .items .item1 .text {
    width: 50%;
    padding: 140px 0;
  }
}
.features .items .item1 .charts-maps {
  display: inline-block;
  margin-right: 50px;
}
.features .items .item1 .charts-maps p {
  font-size: 42px;
  font-weight: 300;
  line-height: 49px;
  margin-bottom: 0;
}
.features .items .item1 .charts-maps p .plus {
  font-size: 30px;
  margin-top: -10px;
}
.features .items .item1 .charts-maps p .txt {
  width: 50%;
  height: 21px;
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  line-height: 21px;
}
.features .items .item1 .charts-maps:last-of-type {
  margin-right: 0;
}
.features .items .item1 .button {
  position: relative;
  left: 50%;
  margin-left: -110px;
}
@media only screen and (min-width: 800px) {
  .features .items .item1 .button {
    left: 0;
    margin-left: 0;
  }
}
.features .items .item2 {
  position: relative;
  background: linear-gradient(
    135.75deg,
    #a3649f 0,
    #5869b4 55.55%,
    #a2f69f 100%
  );
  padding: 50px 0;
  text-align: center;
}
.features .items .item2 .text {
  color: #fff;
}
.features .items .item2 img {
  position: relative;
  display: inline-block;
  width: calc(100% - 40px);
  margin: 0 20px;
  max-width: 1420px;
}
.features .items .item3 {
  padding: 70px 0 0;
}
@media only screen and (min-width: 800px) {
  .features .items .item3 {
    padding: 70px 0;
  }
}
.features .items .item3 .text {
  padding-top: 40px;
}
@media only screen and (min-width: 800px) {
  .features .items .item3 .text {
    padding-top: 150px;
  }
}
@media only screen and (min-width: 800px) {
  .features .items .item3 .text h2 {
    padding-left: 54px;
    max-width: 340px;
  }
}
@media only screen and (min-width: 800px) {
  .features .items .item3 .text p {
    padding-left: 55px;
  }
}
.features .items .item4 {
  padding: 70px 0;
}
.features .items .item4 .text {
  padding-top: 40px;
}
@media only screen and (min-width: 800px) {
  .features .items .item4 .text {
    padding-top: 70px;
  }
}
.features .items .item4 img {
  border-radius: 6px;
}
.features .items .item5 {
  background-color: #f5f5f5;
  padding: 80px 0 60px 0;
  text-align: center;
}
.features .items .item5 img {
  width: calc(100% - 40px);
  margin: 0 20px 30px 20px;
  max-width: 1260px;
}
.features .items .item6 {
  padding: 80px 0 60px;
  text-align: center;
}
.features .items .item6 .gifs {
  width: 100%;
}
.features .items .item6 .gifs .gif {
  display: inline-block;
  max-width: 368px;
  min-width: 300px;
  width: 32%;
  background-color: #fff;
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.13);
  border-radius: 10px;
  margin-bottom: 20px;
}
.features .items .item6 .gifs .gif:nth-child(2) {
  margin-right: 2%;
  margin-left: 2%;
}
.features .items .item6 img {
  width: 94%;
}
.features .items .item6 .file-icons {
  width: 100%;
  max-width: 370px;
  margin-bottom: 50px;
}
.features .items .item7 {
  position: relative;
  padding: 60px 0 0;
}
@media only screen and (min-width: 800px) {
  .features .items .item7 {
    padding: 60px 0 0;
  }
}
.features .items .item7 .content {
  max-width: 1340px;
}
@media only screen and (min-width: 800px) {
  .features .items .item7 .content {
    display: table;
  }
}
.features .items .item7 .bg-left {
  position: absolute;
}
@media only screen and (min-width: 800px) {
  .features .items .item7 .bg-left {
    width: 50%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(134.94deg, #71a0cd 0, #19906b 100%);
  }
}
.features .items .item7 .bg-right {
  position: absolute;
}
@media only screen and (min-width: 800px) {
  .features .items .item7 .bg-right {
    width: 50%;
    height: 100%;
    top: 0;
    right: 0;
    background: linear-gradient(135.17deg, #7196af 0, #9b80b7 100%);
  }
}
.features .items .item7 .text {
  position: relative;
  width: 100%;
  height: 100%;
  color: #fff;
  text-align: center;
  background: linear-gradient(134.94deg, #71a0cd 0, #19906b 100%);
  padding: 70px 20px;
  margin-left: -20px;
}
@media only screen and (min-width: 800px) {
  .features .items .item7 .text {
    background: 0 0;
    padding: 0 0 115px 0;
    margin: 0;
    float: none;
    display: table-cell;
  }
}
@media only screen and (min-width: 800px) {
  .features .items .item7 .text {
    width: 50%;
  }
}
.features .items .item7 .text__interactive-charts {
  padding-right: 20px;
}
.features .items .item7 .text__interactive-maps {
  padding-left: 20px;
}
.features .items .item7 .text p {
  padding: 0 50px;
  max-width: 100%;
}
.features .items .item7 .text .infographic {
  position: relative;
  width: 100%;
  margin-bottom: 30px;
}
.features .items .item7 .text .infographic__interactive-maps {
  background-color: rgba(71, 59, 107, 0.5);
}
.features .items .item7 .text .infographic__interactive-charts {
  background-color: #fff;
}
.features .items .item7 .text .infographic__interactive-charts,
.features .items .item7 .text .infographic__interactive-maps {
  width: auto;
  padding: 20px;
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.33);
  border-radius: 4px;
}
@media only screen and (min-width: 600px) {
  .features .items .item7 .text .infographic__interactive-charts,
  .features .items .item7 .text .infographic__interactive-maps {
    width: auto;
    margin: 0 80px 30px;
  }
}
.features .items .item7 .text .infographic__interactive-charts .infogram-embed,
.features .items .item7 .text .infographic__interactive-maps .infogram-embed {
  box-shadow: none;
  background: 0 0;
  min-height: 500px;
}
.features .items .item7 .text:last-child {
  background: linear-gradient(135.17deg, #7196af 0, #9b80b7 100%);
}
@media only screen and (min-width: 800px) {
  .features .items .item7 .text:last-child {
    background: 0 0;
  }
}
@media only screen and (min-width: 800px) {
  .features .items .item7 .text .button-white {
    position: absolute;
    bottom: 70px;
    left: 50%;
    margin-left: -110px;
  }
}
.features .items .item8 {
  padding: 80px 0;
  text-align: center;
}
.features .items .item8 img {
  width: calc(100% - 40px);
  margin: 0 20px;
  max-width: 690px;
}
.features .items .item9 {
  background: url(../images/block9-bg.jpg);
  background-size: cover;
  padding: 80px 0;
  text-align: center;
}
.features .items .item9 .text {
  color: #fff;
}
.features .items .item9 img {
  width: calc(100% - 40px);
  margin: 0 20px;
  max-width: 700px;
}
.features .items .item9 .cta_default {
  background-color: #ce2533;
  margin-top: 0;
  margin-bottom: 50px;
  transition: 0.2s all ease-in-out;
}
.features .items .item9 .cta_default:focus,
.features .items .item9 .cta_default:hover {
  background-color: #de4854;
}
.features .items .item10 {
  padding: 30px 0 0 0;
  text-align: center;
}
.features .items .item10 .text h2 {
  margin-bottom: 0;
}
.features .items .item10 .text h2 .button {
  min-width: 220px;
  font-weight: 500;
  margin: 10px auto 0;
}
@media only screen and (min-width: 800px) {
  .features .items .item10 .text h2 .button {
    margin: 0 0 0 20px;
    display: inline-block;
    line-height: 50px;
  }
}
.features .items .item11,
.features .items .item12 {
  padding: 60px 0;
  text-align: center;
}
.features .items .item11 .infographic,
.features .items .item12 .infographic {
  max-width: 700px;
  margin: 0 auto;
  margin-bottom: 30px;
}
.features .items .item12 {
  background-color: #f5f5f5;
  padding: 60px 0;
  text-align: center;
}
.features .feature_header,
.features .top_line {
  opacity: 1;
}
.features .items .item {
  transition: all 0.7s;
  transition-timing-function: ease-in-out;
}
.features .items .item .chart,
.features .items .item .gifs,
.features .items .item .image-absolute,
.features .items .item .map,
.features .items .item .text,
.features .items .item img {
  transition: all 0.7s;
  transition-timing-function: ease-in-out;
}
.features .items .item1 {
  opacity: 1;
}
.features .items .item2 {
  opacity: 0;
}
.features .items .item2 .text {
  opacity: 0;
  position: relative;
  bottom: -2000px;
  transition: all 0.9s, opacity 1.1s linear;
}
.features .items .item2 img {
  opacity: 0;
  bottom: -2000px;
  transition: all 1.3s, opacity 1.5s linear;
}
.features .items .item2.animate {
  opacity: 1;
}
.features .items .item2.animate .text {
  opacity: 1;
  bottom: 0;
}
.features .items .item2.animate img {
  opacity: 1;
  bottom: 0;
}
.features .items .item3 .image img {
  position: relative;
  display: inline-block;
  bottom: -2000px;
  transition: all 0.9s;
}
.features .items .item3.animate .image img {
  bottom: 0;
}
.features .items .item4 img {
  position: relative;
  display: inline-block;
  bottom: -2000px;
  transition: all 0.9s;
}
.features .items .item4.animate img {
  bottom: 0;
}
.features .items .item5 {
  opacity: 0;
}
.features .items .item5 .text {
  opacity: 0;
  position: relative;
  bottom: -2000px;
  transition: all 0.9s, opacity 0.9s linear;
}
.features .items .item5 img {
  position: relative;
  opacity: 0;
  bottom: -2000px;
  transition: all 1.3s, opacity 1.3s linear;
}
.features .items .item5.animate {
  opacity: 1;
}
.features .items .item5.animate .text {
  opacity: 1;
  bottom: 0;
}
.features .items .item5.animate img {
  opacity: 1;
  bottom: 0;
}
.features .items .item6 .text {
  opacity: 0;
  position: relative;
  bottom: -2000px;
  transition: all 0.9s, opacity 1.1s linear;
}
.features .items .item6 .gifs {
  position: relative;
}
.features .items .item6 .gifs div {
  position: relative;
  bottom: -1000px;
  transition: all 1.2s;
}
.features .items .item6 .gifs div + div {
  transition: all 1.9s;
}
.features .items .item6 .gifs div + div + div {
  transition: all 2.4s;
}
.features .items .item6.animate .text {
  opacity: 1;
  bottom: 0;
}
.features .items .item6.animate .gifs .gif {
  bottom: 0;
}
.features .items .item8 {
  opacity: 0;
}
.features .items .item8 .text {
  opacity: 0;
  position: relative;
  bottom: -2000px;
  transition: all 0.9s, opacity 1.1s linear;
}
.features .items .item8 img {
  position: relative;
  opacity: 0;
  bottom: -2000px;
  transition: all 1.3s, opacity 1.5s linear;
}
.features .items .item8.animate {
  opacity: 1;
}
.features .items .item8.animate .text {
  opacity: 1;
  bottom: 0;
}
.features .items .item8.animate img {
  opacity: 1;
  bottom: 0;
}
.features .items .item11,
.features .items .item12 {
  opacity: 0;
}
.features .items .item11 .text,
.features .items .item12 .text {
  opacity: 0;
  position: relative;
  top: -2000px;
  transition: all 0.9s, opacity 0.9s linear;
}
.features .items .item11.animate,
.features .items .item12.animate {
  opacity: 1;
}
.features .items .item11.animate .text,
.features .items .item12.animate .text {
  opacity: 1;
  top: 0;
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}
.team_page .switcher {
  position: relative;
  font-size: 15px;
  font-weight: 500;
  width: 100%;
  max-width: 460px;
  margin: 20px auto 60px;
  text-align: center;
  line-height: 50px;
}
.team_page .switcher:after {
  content: "";
  display: table;
  clear: both;
}
.team_page .switcher a {
  position: relative;
  color: #656565;
  text-decoration: none;
}
.team_page .switcher a:after {
  content: "";
  display: block;
  height: 2px;
  width: 100%;
  background: #d8d8d8;
  margin-top: -2px;
}
.team_page .switcher .active:after {
  height: 5px;
  margin-top: -5px;
  background: #3195cb;
}
.team_page .switcher .monthly {
  width: 50%;
  float: left;
}
.team_page .switcher .yearly {
  width: 50%;
  float: right;
}
.team_page .switcher .annual-save {
  position: absolute;
  right: 0;
  top: 0;
  color: #38a86f;
  font-size: 11px;
  font-weight: 500;
  line-height: 13px;
  text-align: center;
}
.team_page .pb_upgrade {
  height: 40px;
  background-color: #3796c9;
  cursor: pointer;
  text-align: center;
  width: 100%;
  font-size: 15px;
  color: #fff;
  line-height: 40px;
  border-radius: 4px;
  margin-bottom: 20px;
}
.team_page .pb_upgrade:focus,
.team_page .pb_upgrade:hover {
  border: 0 none;
  background: #5fb7e5;
  text-decoration: none;
}
.team_page .pricing-block {
  position: relative;
  max-width: 1200px;
  margin: 0 auto 40px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.team_page .pricing-block:after {
  content: "";
  display: table;
  clear: both;
}
.team_page .pricing-block .pricing-plan {
  position: relative;
  margin: 0 10px 30px;
  max-width: 210px;
  flex-basis: 210px;
}
.team_page .pricing-block .pricing-plan__description {
  margin-bottom: 15px;
  margin-top: 15px;
  color: #fff;
  font-size: 13px;
  line-height: 18px;
  text-align: center;
}
.team_page .pricing-block .pricing-plan__price {
  align-self: flex-end;
}
.team_page .pricing-block .pricing-plan__heading {
  text-align: left;
  font-size: 16px;
  font-weight: 700;
  line-height: 19px;
  margin-bottom: 10px;
  margin-top: -3px;
}
.team_page .pricing-block .pricing-plan .plan-title {
  height: 300px;
  border-radius: 4px;
  text-align: center;
  margin-bottom: 10px;
  padding: 25px 15px;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.team_page .pricing-block .pricing-plan .plan-title .title-txt {
  color: #fff;
  font-size: 19px;
  font-weight: 500;
  line-height: 23px;
  text-align: center;
}
.team_page .pricing-block .pricing-plan .plan-title .price {
  color: #fff;
  font-size: 47px;
  font-weight: 300;
  line-height: 55px;
  flex-basis: 100%;
  margin-bottom: 10px;
}
.team_page .pricing-block .pricing-plan .plan-title .price img {
  max-height: 55px;
}
.team_page .pricing-block .pricing-plan .plan-title .price .dollar {
  font-size: 15px;
  font-weight: 500;
  line-height: 35px;
}
.team_page .pricing-block .pricing-plan .plan-title .price.pb_price_monthly {
  display: none;
}
.team_page .pricing-block .pricing-plan .plan-title .sub-txt {
  color: #fff;
  font-size: 11px;
  font-weight: 500;
  line-height: 13px;
}
.team_page .pricing-block .pricing-plan .plan-title .sub-txt .phone {
  font-size: 13px;
}
.team_page .pricing-block .pricing-plan .plan-title .sub-txt.pb_price_monthly {
  display: none;
}
.team_page .pricing-block .pricing-plan .features {
  display: block;
}
.team_page .pricing-block .pricing-plan .features li {
  position: relative;
  color: #434343;
  font-size: 13px;
  font-weight: 500;
  line-height: 15px;
  margin-bottom: 15px;
  padding-left: 30px;
  width: 150px;
}
.team_page .pricing-block .pricing-plan .features li[data-tooltip] {
  cursor: help;
}
.team_page .pricing-block .pricing-plan .features li:before {
  display: block;
  content: "";
  position: absolute;
  top: 2px;
  left: 0;
  width: 16px;
  height: 11px;
  background-image: url(../images/pricing_check.png);
  background-size: 100%;
}
.team_page .pricing-block .pricing-plan.basic .plan-title {
  background-color: #9b9b9b;
}
.team_page .pricing-block .pricing-plan.pro .plan-title {
  background-color: #529882;
}
.team_page .pricing-block .pricing-plan.business .plan-title {
  background-color: #456579;
}
.team_page .pricing-block .pricing-plan.business .plan-title .most-popular {
  position: absolute;
  top: -20px;
  left: 50%;
  margin-left: -70px;
  height: 20px;
  width: 140px;
  border-radius: 4px 4px 0 0;
  background-color: #7d95ad;
  color: #fff;
  font-size: 11px;
  font-weight: 500;
  line-height: 19px;
}
.team_page .pricing-block .pricing-plan.team .plan-title {
  background-color: #cd8485;
}
.team_page .pricing-block .pricing-plan.enterprise {
  margin-right: 0;
}
.team_page .pricing-block .pricing-plan.enterprise .plan-title {
  background-color: #9b80b7;
}
.team_page .pricing-block .pricing-block-bottom-info {
  max-width: 620px;
  text-align: center;
}
.team_page .pricing-block .pricing-block-bottom-info__text {
  padding: 0 20px;
  font-size: 11px;
  font-weight: 500;
  line-height: 19px;
  color: #656565;
  flex-basis: 100%;
  text-align: center;
  margin-bottom: 20px;
}
.team_page .pricing-block .pricing-block-bottom-info__link {
  display: inline-block;
  height: 40px;
  width: 160px;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 500;
  line-height: 40px;
  text-align: center;
  color: #fff;
  text-decoration: none;
  margin: 0 10px 10px;
}
.team_page .pricing-block .pricing-block-bottom-info__link:hover {
  opacity: 0.7;
}
.team_page .pricing-block .pricing-block-bottom-info__link--demo {
  background-color: #3195cb;
}
.team_page .pricing-block .pricing-block-bottom-info__link--talk {
  background-color: #656565;
}
.upgrade_limited .pb_price {
  font-size: 47px;
  line-height: 54px;
  font-weight: 300;
  width: 100%;
  margin: 16px 0 3px;
}
.upgrade_limited .pb_price_monthly {
  display: none;
}
.upgrade_limited .pb_price_enterprise_monthly .pb_vat {
  padding-bottom: 52px;
}
.pb_vat {
  font-size: 15px;
  line-height: 20px;
  font-weight: 500;
  margin: 29px 0 0 -5px;
}
.pb_enterprise .pb_vat {
  text-align: left;
  margin: 29px 0 0 -4px;
}
@media only screen and (max-width: 972px) {
  .payment_switch {
    margin-bottom: 17px;
  }
  .pb_upgrade.header {
    margin-left: 10px !important;
  }
  .pb_upgrade span {
    padding-top: 15px;
  }
}

ol,
ul {
  list-style: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
@keyframes infogram-loader {
  100% {
    transform: rotate(360deg);
  }
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.p-0 {
  padding: 0 !important;
}
.m-0 {
  margin: 0 !important;
}
.d-flex {
  display: flex;
}
.f-wrap {
  flex-wrap: wrap;
}
@media only screen and (max-width: 480px) {
  .hidden-phone {
    display: none;
  }
}
.g-header {
  padding: 120px 0 100px;
  background-image: url(../images/bg_header.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  text-align: center;
}
.g-header__title {
  font-weight: 300;
  font-size: 49px;
  line-height: 60px;
  max-width: 840px;
  margin: 0 auto;
}
@media only screen and (max-width: 480px) {
  .g-header__title {
    font-size: 42px;
    line-height: 48px;
  }
}
.g-header__description {
  font-size: 15px;
  font-weight: 500;
  line-height: 25px;
  max-width: 460px;
  margin: 20px auto 15px;
}
.g-header__embed {
  max-width: 700px;
  min-height: 470px;
  margin: 35px auto;
}
@media only screen and (max-width: 480px) {
  .g-header__embed {
    min-height: 280px;
  }
}
.g-header__embed .infogram-embed {
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.33);
  border-radius: 4px;
  background: rgba(71, 59, 107, 0.5);
}
.g-button {
  cursor: pointer;
  font-family: inherit;
  box-sizing: border-box;
  font-size: 15px;
  font-weight: 500;
  background-color: #3195cb;
  border-radius: 4px;
  width: 220px;
  color: #fff;
  height: 40px;
  line-height: 40px;
  transition: 0.25s all cubic-bezier(0.165, 0.84, 0.44, 1);
}
.g-button:active,
.g-button:focus,
.g-button:hover {
  color: #fff;
  text-decoration: none;
  background-color: #58aad7;
}
.g-button--accent {
  background-color: #ce2533;
}
.g-button--accent:active,
.g-button--accent:focus,
.g-button--accent:hover {
  background-color: #de4854;
  color: #fff;
}
.g-button--large {
  width: 300px;
  height: 50px;
  line-height: 47px;
  font-size: 17px;
  box-sizing: border-box;
  padding: 0 20px;
}
@media screen and (max-width: 360px) {
  .g-button--large {
    width: 100%;
    font-size: 15px;
  }
}
.g-button--transparent {
  border: 1px solid #fff;
  background-color: transparent;
}
.g-button--transparent:active,
.g-button--transparent:focus,
.g-button--transparent:hover {
  background-color: #fff;
  color: #656565;
}
.g-button--gray {
  border: 2px solid #656565;
  color: #656565;
  background-color: transparent;
}
.g-button--gray:active,
.g-button--gray:focus,
.g-button--gray:hover {
  color: #fff;
  background-color: #656565;
}
.g-button--centered {
  display: block;
  margin: 0 auto;
}
.g-link {
  color: #3796c9;
}
.g-link:hover {
  color: #3896c9;
}
.g-paragraph {
  font-weight: 500;
  color: #656565;
  font-size: 15px;
  line-height: 25px;
}
.fade-in {
  opacity: 0;
  -webkit-animation: fadeIn ease-in 1;
  -moz-animation: fadeIn ease-in 1;
  animation: fadeIn ease-in 1;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 0.6s;
  -moz-animation-duration: 0.6s;
  animation-duration: 0.6s;
}
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-moz-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
img {
  max-width: 100%;
}
a {
  color: #3195cb;
}
a:hover {
  color: #58aad7;
}
.frontpage .section {
  padding: 50px 0;
}
.frontpage .section .container {
  max-width: 1000px;
  box-sizing: border-box;
  margin: 0 auto;
}
@media only screen and (max-width: 1199px) {
  .frontpage .section .container {
    padding: 0 20px;
  }
}
.frontpage .section--gray {
  background-color: #ebebee;
}
.frontpage .section--dark {
  color: #fff;
}
.frontpage .section--light {
  color: #656565;
}
.frontpage .section__paragraph {
  font-size: 15px;
  line-height: 25px;
  color: inherit;
  font-weight: 400;
}
.frontpage .section__title {
  color: inherit;
  font-size: 29px;
  font-weight: 300;
  line-height: 34px;
}
.frontpage .section__description {
  color: inherit;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  padding-top: 0;
  margin-top: 20px;
  margin-bottom: 15px;
}
body,
html {
  height: 100%;
}
.signup-form-container {
  display: flex;
}
.signup_sidebar {
  width: 440px;
  background: linear-gradient(135deg, #582e8d, #419bad 100%);
  color: #fff;
  padding: 30px;
  display: none;
  position: relative;
  padding-bottom: 0;
  min-height: 100vh;
}
@media only screen and (min-width: 850px) {
  .signup_sidebar {
    display: block;
  }
}
.signup_sidebar .logo {
  position: relative;
  top: 0;
  left: 0;
  margin: 0 0 40px;
}
.signup_sidebar h2 {
  font-size: 37px;
  font-weight: 300;
  line-height: 45px;
  margin-bottom: 40px;
}
.signup_sidebar .testimonial {
  font-size: 22px;
  font-style: italic;
  font-weight: 300;
  line-height: 29px;
  margin-bottom: 15px;
}
.signup_sidebar .testimonial_author {
  margin-bottom: 30px;
}
.signup_sidebar .testimonial_author img {
  width: 63px;
  height: 63px;
  margin-right: 6px;
  border-radius: 50%;
}
.signup_sidebar .testimonial_author span {
  font-size: 13px;
  font-weight: 500;
  line-height: 17px;
  margin-top: 13px;
}
.signup_sidebar .sidebar_footer {
  position: absolute;
  left: 0;
  bottom: 0;
  background-color: rgba(15, 115, 64, 0.5);
  padding: 30px;
}
.signup_sidebar .sidebar_footer .footer_title {
  font-size: 15px;
  font-weight: 500;
  line-height: 18px;
  margin-bottom: 15px;
  text-align: center;
}
.signup_sidebar .sidebar_footer img {
  width: 440px;
}
.form_wrapper {
  width: calc(100% - 60px);
  padding: 0 30px;
}
.contact_page .form_wrapper .form_container > h1 {
  line-height: 40px;
  margin-bottom: 10px;
}
.form_wrapper.form_teams {
  position: initial;
  width: auto;
}
.form_wrapper.form_teams .form_head {
  text-align: center;
}
.form_wrapper.form_teams .form_head img {
  margin-bottom: 20px;
}
.form_wrapper.form_teams .get_in_touch {
  text-align: center;
}
.form_wrapper.form_teams .actions {
  background-color: transparent;
}
.form_wrapper .submitted-message {
  background: #8ec3a7;
  padding: 80px 0;
  color: #fff;
  border-radius: 4px;
  margin-bottom: 40px;
  text-align: center;
}
.form_wrapper .submitted-message p strong {
  font-size: 35px;
  line-height: 25px;
  font-weight: 300;
  text-align: center;
  padding-bottom: 20px;
  display: inline-block;
}
@media only screen and (min-width: 850px) {
  .form_wrapper {
    width: calc(100% - 560px);
  }
}
.form_wrapper .close {
  position: absolute;
  top: 20px;
  right: 20px;
  height: 20px;
  width: 20px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.4);
  cursor: pointer;
}
.form_wrapper .close:after,
.form_wrapper .close:before {
  position: absolute;
  top: 9px;
  left: 5px;
  content: "";
  width: 11px;
  height: 2px;
  background: #fff;
}
.form_wrapper .close:before {
  transform: rotate(133deg);
}
.form_wrapper .close:after {
  transform: rotate(226deg);
}
.form_wrapper .close span {
  position: absolute;
  top: 25px;
  color: #999;
  font-size: 11px;
  font-weight: 500;
  line-height: 13px;
}
.form_wrapper .form_container {
  width: 100%;
  max-width: 436px;
  margin: 70px auto 0;
}
@media only screen and (max-width: 490px) {
  .form_wrapper .form_container {
    margin: 50px auto 0;
  }
}
.form_wrapper .form_container h1 {
  color: #656565;
  font-size: 41px;
  font-weight: 300;
  line-height: 60px;
  text-align: center;
  margin-bottom: 30px;
}
.form_wrapper .form_container .success_msg {
  width: 100%;
  color: #3ea771;
  font-size: 13px;
  font-weight: 500;
  line-height: 17px;
  text-align: center;
  margin-bottom: 30px;
}
.form_wrapper .form_container .top_info_msg {
  width: 100%;
  color: #2d2d2d;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  text-align: center;
  margin-bottom: 30px;
}
.form_wrapper .soc_connect {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.form_wrapper .soc_connect__icon {
  flex-basis: auto;
  width: 100%;
  border-radius: 4px;
  margin-bottom: 15px;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  height: 40px;
  line-height: 40px;
  text-align: center;
  text-decoration: none;
  padding-left: 20px;
  box-sizing: border-box;
  position: relative;
}
[lang="pt"] .form_wrapper .soc_connect__icon {
  font-size: 13px;
}
.form_wrapper .soc_connect__icon:hover {
  opacity: 0.9;
}
.form_wrapper .soc_connect__icon::before {
  content: "";
  width: 40px;
  height: 40px;
  background-position: center;
  background-size: 50%;
  background-repeat: no-repeat;
  position: absolute;
  left: 0;
}
.form_wrapper .soc_connect__icon--facebook {
  background-color: #4267b2;
}
.form_wrapper .soc_connect__icon--facebook::before {
  background-image: url("../images/facebook-white.svg");
}
.form_wrapper .soc_connect__icon--facebook:hover {
  opacity: 1;
  background: #365899;
}
.form_wrapper .soc_connect__icon--google {
  background-color: #4285f4;
}
.form_wrapper .soc_connect__icon--google::before {
  width: 38px;
  height: 38px;
  background-image: url("../images/google.svg");
  background-color: #fff;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  background-position: center;
  left: 1px;
  top: 1px;
}
.form_wrapper .soc_connect__icon--google:hover {
  opacity: 1;
  background-color: #3367d6;
}
.form_wrapper .soc_connect__icon--linkedin {
  background-color: #2588bb;
}
.form_wrapper .soc_connect__icon--linkedin::before {
  background-image: url("../images/linkedin-white.svg");
}
.form_wrapper .ls_or {
  width: 100%;
  height: 1px;
  margin: 10px auto 30px;
  text-align: center;
}
.form_wrapper .ls_line {
  height: 1px;
  background-color: #ccc;
}
.form_wrapper .or_txt {
  width: 160px;
  font-size: 15px;
  line-height: 17px;
  color: #434343;
  background-color: #fff;
  margin-top: -10px;
  display: inline-block;
  position: relative;
  top: -9px;
}
.form_wrapper .form_block .input {
  position: relative;
  margin-bottom: 25px;
}
.form_wrapper .form_block .input .label {
  color: #434343;
  font-size: 11px;
  font-weight: 500;
  line-height: 13px;
  margin-bottom: 8px;
}
.form_wrapper .form_block .input input {
  width: calc(100% - 26px);
  height: 16px;
  border: 1px solid #a6a6a6;
  border-radius: 4px;
  background-color: #fff;
  color: #434343;
  font-size: 15px;
  font-weight: 500;
  line-height: 18px;
  padding: 11px 13px;
}
.form_wrapper .form_block .input input:focus {
  border: 1px solid #3195cb;
}
.form_wrapper .form_block .input .error_msg,
.form_wrapper .form_block .input .info_msg {
  display: block;
  position: absolute;
  top: auto;
  right: auto;
  bottom: -15px;
  width: 100%;
  height: 13px;
  background: 0 0;
  color: #c32f2f;
  font-size: 11px;
  font-weight: 500;
  line-height: 13px;
  text-align: right;
  padding: 0;
  animation: fade;
}
.form_wrapper .form_block .input .error_msg {
  opacity: 0;
}
.form_wrapper .form_block .input .info_msg {
  text-align: left;
  color: #2d2d2d;
  opacity: 0.5;
}
.form_wrapper .form_block .input.false input {
  border: 1px solid #c32f2f;
}
.form_wrapper .form_block .input.false .error_msg {
  opacity: 1;
  transition: all 0.2s;
}
.form_wrapper .form_block .input.false .info_msg {
  display: none;
}
.form_wrapper .form_block .remember_forget {
  width: 100%;
  margin: 0;
}
.form_wrapper .form_block .remember_forget #remember_me {
  margin-left: 0;
  color: #656565;
}
.form_wrapper .form_block .remember_forget .forgot_password {
  float: right;
  font-size: 13px;
  text-align: right;
  margin-top: 10px;
  margin-right: 0;
}
.form_wrapper .form_block .remember_forget .toggle_btn {
  color: #656565;
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
}
.form_wrapper .form_block .hs-button.primary.large,
.form_wrapper .form_block .submit {
  width: 100%;
  height: 50px;
  line-height: 50px;
  border-radius: 4px;
  background-color: #3195cb;
  text-decoration: none;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 15px;
  font-family: Roboto, sans-serif;
  cursor: pointer;
}
.form_wrapper .form_block .hs-button.primary.large:focus,
.form_wrapper .form_block .hs-button.primary.large:hover,
.form_wrapper .form_block .submit:focus,
.form_wrapper .form_block .submit:hover {
  opacity: 0.9;
}
.form_wrapper .form_block .hs-button.primary.large .spinner,
.form_wrapper .form_block .submit .spinner {
  position: absolute;
  top: 50%;
  margin-top: -15px;
  right: 10px;
  width: 20px;
  height: 20px;
  border: 3px solid transparent;
  border-top: 3px solid #fff;
  border-right: 3px solid #fff;
  border-radius: 50%;
  -webkit-animation: infogram-loader 1s linear infinite;
  animation: infogram-loader 1s linear infinite;
  display: none;
}
.form_wrapper .form_block .hs-button.primary.large[disabled],
.form_wrapper .form_block .submit[disabled] {
  opacity: 0.3;
  cursor: default;
}
.form_wrapper .form_block .hs-button.primary.large.loading,
.form_wrapper .form_block .submit.loading {
  position: relative;
}
.form_wrapper .form_block .hs-button.primary.large.loading .spinner,
.form_wrapper .form_block .submit.loading .spinner {
  display: block;
}
.form_wrapper .form_block .hs-button.primary.large.get_started,
.form_wrapper .form_block .submit.get_started {
  margin-top: 20px;
}
.form_wrapper .form_block .hs-button.primary.large {
  margin-top: 5px;
}
.form_wrapper .form_block .terms_agreement {
  position: relative;
  font-size: 11px;
  font-weight: 500;
  line-height: 1.6;
  color: #434343;
  text-align: center;
  margin-top: 18px;
}
.form_wrapper .form_block .a_reg {
  width: 100%;
  color: #3796c9;
  font-family: Roboto;
  font-size: 15px;
  font-weight: 500;
  line-height: 25px;
  padding: 0;
  margin: 0;
}
.form_wrapper .form_block .goback {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 22px;
  height: 20px;
  background-image: url(../images/back.svg);
  background-repeat: no-repeat;
  background-position: 5px 5px;
  background-size: 11px 10px;
  cursor: pointer;
}
.form_wrapper .form_block .forgot_form_success,
.form_wrapper .form_block .reset_psw_form_success {
  display: none;
  background-image: url(../images/envelope_icon_light.svg);
  background-size: 120px;
  padding-top: 170px;
  background-repeat: no-repeat;
  background-position: top center;
  width: 100%;
  max-width: 330px;
  color: #2d2d2d;
  font-size: 17px;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  margin: 130px auto 0;
}
.form_wrapper .form_block .reset_psw_form_success {
  background-image: url(../images/upgraded.png);
  background-size: 200px;
  padding-top: 250px;
}
form.hs-form select,
form.hs-form textarea {
  border-radius: 4px;
  padding: 9px 10px 10px;
  border: 1px solid #a6a6a6;
  margin-top: 5px;
  margin-bottom: 0;
  max-width: 480px;
  font-size: 15px;
  line-height: 18px;
}
form.hs-form select {
  width: 100%;
  background: 0 0;
  height: 40px;
  font-size: 15px;
  box-sizing: border-box;
}
form.hs-form textarea {
  width: 100%;
  height: 100px;
  resize: none;
  box-sizing: border-box;
}
form.hs-form label,
form.hs-form legend {
  color: #434343;
  font-size: 11px;
  font-weight: 500;
  line-height: 14px;
  margin-bottom: 20px;
}
form.hs-form legend {
  margin-bottom: 0;
}
form.hs-form .hs-error-msgs {
  margin-top: -19px;
}
form.hs-form .hs-error-msgs label {
  color: #ce2533;
}
.contact_page .form_wrapper .form_container h2,
.demo_page .form_wrapper .form_container h2,
.team_page .form_wrapper .form_container h2,
.thank_you .form_wrapper .form_container h2,
.trial_page .form_wrapper .form_container h2 {
  color: #434343;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 30px;
}
.contact_page .hs-richtext,
.demo_page .hs-richtext,
.team_page .hs-richtext,
.thank_you .hs-richtext,
.trial_page .hs-richtext {
  font-size: 12px;
  margin-bottom: 20px;
}
.thank_you h1 {
  font-size: 28px !important;
  margin-bottom: 10px;
}
.thank_you h2 {
  font-size: 13px !important;
  line-height: 21px !important;
  margin-bottom: 10px;
}
.thank_you .thank-you-bottom-nav .blog a,
.thank_you .thank-you-bottom-nav .customers a,
.thank_you .thank-you-bottom-nav .data-visualization-resources a,
.thank_you .thank-you-bottom-nav .features a {
  position: relative;
  display: block;
  padding: 25px;
  max-width: 200px;
  min-height: 255px;
  border-radius: 4px;
  text-align: center;
  font-size: 15px;
  line-height: 19px;
  color: #fff;
  transition: all 0.4s;
  margin: 0 auto 30px;
  box-sizing: border-box;
}
@media only screen and (min-width: 1100px) {
  .thank_you .thank-you-bottom-nav .blog a,
  .thank_you .thank-you-bottom-nav .customers a,
  .thank_you .thank-you-bottom-nav .data-visualization-resources a,
  .thank_you .thank-you-bottom-nav .features a {
    margin: 0 9px 30px;
    float: left;
  }
}
.thank_you .thank-you-bottom-nav .blog a:hover,
.thank_you .thank-you-bottom-nav .customers a:hover,
.thank_you .thank-you-bottom-nav .data-visualization-resources a:hover,
.thank_you .thank-you-bottom-nav .features a:hover {
  opacity: 0.7;
  text-decoration: none;
}
.thank_you .thank-you-bottom-nav .blog a img,
.thank_you .thank-you-bottom-nav .customers a img,
.thank_you .thank-you-bottom-nav .data-visualization-resources a img,
.thank_you .thank-you-bottom-nav .features a img {
  max-width: 100%;
  display: block;
  margin: 0 auto;
}
.thank_you .thank-you-bottom-nav .features a {
  background: #8ec3a7;
  padding: 60px 36px;
}
.thank_you .thank-you-bottom-nav .features a img {
  margin-bottom: 42px;
}
.thank_you .thank-you-bottom-nav .data-visualization-resources a {
  background: #3796c9;
  padding: 50px 57px;
}
.thank_you .thank-you-bottom-nav .data-visualization-resources a img {
  margin-bottom: 35px;
}
.thank_you .thank-you-bottom-nav .blog a {
  background: #9c82b6;
  padding: 44px 51px;
}
.thank_you .thank-you-bottom-nav .blog a img {
  margin-bottom: 32px;
}
.thank_you .thank-you-bottom-nav .customers a {
  background: #f09b69;
}
.thank_you .thank-you-bottom-nav .customers a img {
  margin-bottom: 38px;
  max-width: 100px;
}
.hbspt-form .hs_error_rollup {
  margin-top: 25px;
  font-weight: 700;
}
#top {
  height: 599px;
  background: #1a1a1a url("../images/404-jifo.jpg") no-repeat center top;
}
#top h1 {
  font-family: Roboto, "Myriad Pro", Helvetica, Helvetica Neue, Arial,
    sans-serif;
  font-weight: 400;
  font-size: 46px;
  margin: 0 auto;
}
#notfound-tip {
  font-family: "Myriad Pro", Helvetica, Helvetica Neue, Arial, sans-serif;
  font-weight: 400;
  font-size: 12px;
  margin-top: 10px;
  margin-bottom: 30px;
}
#notfound-tip a {
  text-decoration: underline;
  color: #fff;
}
.swiper-container {
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -o-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  -webkit-transition-property: -webkit-transform, left, top;
  -webkit-transition-duration: 0s;
  -webkit-transform: translate3d(0, 0, 0);
  -webkit-transition-timing-function: ease;
  -moz-transition-property: -moz-transform, left, top;
  -moz-transition-duration: 0s;
  -moz-transform: translate3d(0, 0, 0);
  -moz-transition-timing-function: ease;
  -o-transition-property: -o-transform, left, top;
  -o-transition-duration: 0s;
  -o-transform: translate3d(0, 0, 0);
  -o-transition-timing-function: ease;
  -o-transform: translate(0, 0);
  -ms-transition-property: -ms-transform, left, top;
  -ms-transition-duration: 0s;
  -ms-transform: translate3d(0, 0, 0);
  -ms-transition-timing-function: ease;
  transition-property: transform, left, top;
  transition-duration: 0s;
  transform: translate3d(0, 0, 0);
  transition-timing-function: ease;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}
.swiper-free-mode > .swiper-wrapper {
  -webkit-transition-timing-function: ease-out;
  -moz-transition-timing-function: ease-out;
  -ms-transition-timing-function: ease-out;
  -o-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
  margin: 0 auto;
}
.swiper-slide {
  float: left;
}
.swiper-wp8-horizontal {
  -ms-touch-action: pan-y;
}
.swiper-wp8-vertical {
  -ms-touch-action: pan-x;
}
.global_notification,
.pop {
  font-family: Roboto, sans-serif;
  font-size: 100%;
  line-height: 120%;
  font-weight: 500;
  color: #434343;
}
.pop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
}
.pop .bg_layer {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}
.pop a {
  color: #3796c9;
  text-decoration: none;
  vertical-align: top;
  display: inline-block;
}
.pop a:hover {
  text-decoration: underline;
}
.pop .pop_content {
  position: absolute;
  width: 440px;
  min-height: 580px;
  top: 50%;
  left: 50%;
  margin-left: -220px;
  background-color: #fff;
  border-radius: 4px;
  margin-top: -362px;
}
.pop .pop_content.form_scroll {
  top: 20px;
  bottom: 20px;
  margin-top: 0;
  min-height: 0;
}
.form_scroll .pop_inner {
  position: relative;
  height: 100%;
  overflow: auto;
}
.pop h3 {
  font-size: 23px;
  line-height: 30px;
  color: #434343;
  font-weight: 500;
  padding: 32px 20px 23px;
  text-align: center;
}
.pop h4 {
  font-size: 29px;
  line-height: 32px;
  color: #434343;
  font-weight: 500;
  margin-bottom: 12px;
}
.pop p {
  font-size: 15px;
  line-height: 17px;
  color: #434343;
}
.pop label {
  font-size: 13px;
  line-height: 15px;
  display: block;
  margin: 0 0 9px -16px;
}
.pop input {
  position: relative;
  z-index: 1;
  font-size: 15px;
  height: 48px;
  color: #282727;
  border: 1px solid #7d7d7d;
  border-radius: 4px;
  background-color: #fff;
  width: 100%;
  padding: 0 14px;
  margin: 0 0 16px -15px;
}
.pop textarea {
  position: relative;
  z-index: 1;
  font-size: 15px;
  line-height: 20px;
  color: #282727;
  background-color: #fff;
  border: 1px solid #7d7d7d;
  border-radius: 4px;
  width: 100%;
  min-height: 62px;
  padding: 13px 14px;
  margin-left: -15px;
}
.pop input.focused {
  border-color: #3796c9;
  border-width: 2px;
  height: 46px;
  padding: 0 13px;
}
.input {
  position: relative;
}
.error_ico {
  display: none;
  position: absolute;
  top: 15px;
  right: -49px;
  width: 24px;
  height: 21px;
  background-image: url(../images/img_sprite_bb4e60a13dd34655957ae20fa8451ae2.png);
  background-position: -187px -355px;
}
.false .error_ico {
  background-position: -214px -355px;
}
.false .error_ico,
.true .error_ico {
  display: inline-block;
}
.error_msg {
  display: none;
  position: absolute;
  z-index: 5;
  top: 5px;
  right: 273px;
  font-size: 13px;
  line-height: 40px;
  height: 40px;
  color: #fff;
  background-color: #c32f2f;
  border-radius: 4px;
  white-space: nowrap;
  padding: 0 21px;
}
.em_arrow {
  position: absolute;
  top: 12px;
  right: -8px;
  width: 9px;
  height: 16px;
  background-image: url(../images/img_sprite_bb4e60a13dd34655957ae20fa8451ae2.png);
  background-position: -176px -355px;
}
.select .error_msg {
  right: 286px;
}
.select .error_ico {
  right: -36px;
}
.false .error_msg {
  display: inline-block;
}
.form_scroll .false .error_msg {
  display: none;
}
.pop .false input {
  color: #ce2533;
  border-color: #ce2533;
}
.pop .fields {
  width: 230px;
  margin: 0 auto;
}
.pop {
  opacity: 1;
  transition: opacity 0.2s;
}
.pop-hide {
  opacity: 0;
  display: none;
}
.cboxes {
  width: 300px;
  margin: 12px 0 0 -15px;
}
.cbox {
  float: left;
  width: 150px;
  margin-bottom: 9px;
  cursor: pointer;
}
.cb_ico {
  width: 20px;
  height: 20px;
  background-image: url(../images/img_sprite_bb4e60a13dd34655957ae20fa8451ae2.png);
  background-position: -166px -394px;
  margin-right: 9px;
}
.checked .cb_ico {
  background-position: -166px -416px;
}
.cb_txt {
  font-size: 13px;
  line-height: 20px;
}
.submitted {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  margin-top: -270px;
  text-align: center;
}
.submitted .u_ico {
  width: 200px;
  height: 200px;
  background-image: url(../images/upgraded.png);
  margin: 90px 0 38px;
}
.pop .pop_submit {
  display: block;
  width: 281px;
  height: 50px;
  margin: 0 auto 20px;
  background-color: #3195cb;
  font-size: 17px;
  line-height: 19px;
  color: #fff;
  text-align: center;
  border-radius: 4px;
}
.pop_submit:hover {
  background-color: #5fb7e5;
}
.sub_close {
  width: 256px;
  height: 46px;
  margin: 81px 3px 0;
  background-color: #fff;
  color: #434343;
  font-size: 17px;
  line-height: 19px;
  text-align: center;
  border: 2px solid #656565;
  border-radius: 4px;
}
.sub_close:hover {
  color: #fff;
  background-color: #7d7d7d;
  border-color: #7d7d7d;
}
.sub_txt {
  margin-top: 12px;
}
.pop .close {
  position: absolute;
  top: 0;
  left: auto;
  right: 0;
  z-index: 1;
  width: 40px;
  height: 40px;
  background: 0 0;
  border-radius: 4px;
  cursor: pointer;
}
.pop .close::before {
  position: absolute;
  left: 12px;
  top: 12px;
  content: "×";
  color: #434343;
  font-size: 26px;
}
@media only screen and (min-width: 441px) {
  .pop .close.close-light::before {
    color: #fff;
  }
}
.pop .close:hover {
  background: 0 0;
}
.pop .close:hover::before {
  color: #656565;
}
.pop_content.h_740 {
  top: 20px;
  margin-top: 0;
}
.pop_height {
  overflow: hidden;
}
.pop .login_popup,
.pop .login_popup_before,
.pop .register_popup {
  min-height: 500px;
  margin-top: -270px;
}
.pop .forgot_popup {
  min-height: 312px;
  margin-top: -160px;
}
.pop .login_popup {
  width: 780px;
  margin-left: -360px;
  background: 0 0;
}
.pop .login_popup .close:hover::before {
  color: #fff;
  opacity: 0.5;
}
.pop .login_popup .pop_inner {
  width: 440px;
  float: left;
  background: #fff;
  min-height: 500px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.pop .login_popup .sidebar {
  position: relative;
  box-sizing: border-box;
  width: 340px;
  min-height: 500px;
  float: right;
  color: #fff;
  padding: 30px;
  background: linear-gradient(-135deg, #9364a3 0, #7680a2 42%, #73c586 100%);
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.pop .login_popup .sidebar .title {
  font-size: 32px;
  font-weight: 300;
  line-height: 37px;
  margin-bottom: 30px;
}
.pop .login_popup .sidebar .testimonial {
  font-size: 22px;
  font-weight: 300;
  font-style: italic;
  line-height: 29px;
  margin-bottom: 15px;
}
.pop .login_popup .sidebar .testimonial_author {
  font-size: 13px;
  font-weight: 500;
  line-height: 17px;
}
.pop .login_popup .sidebar .testimonial_author img {
  width: 63px;
  height: 63px;
  float: left;
  margin-right: 7px;
}
.pop .login_popup .sidebar .testimonial_author span {
  display: inline-block;
  margin-top: 13px;
}
.pop .login_popup .sidebar .sidebar_footer {
  position: absolute;
  left: 0;
  width: calc(100% - 54px);
  padding: 28px 27px;
  bottom: 0;
  background-color: rgba(15, 115, 64, 0.5);
  border-bottom-right-radius: 4px;
}
.pop .login_popup .sidebar .sidebar_footer .footer_title {
  font-size: 15px;
}
.pop .login_popup .sidebar .sidebar_footer img {
  width: 290px;
  margin-top: 10px;
}
.pop .login_popup .sidebar .headline {
  font-weight: 300;
  font-size: 37px;
  line-height: 37px;
  padding-bottom: 20px;
}
.pop .login_popup .sidebar .pro_biz {
  font-size: 15px;
  line-height: 20px;
  padding-bottom: 20px;
}
.pop .login_popup .sidebar ul {
  list-style: none;
}
.pop .login_popup .sidebar li {
  position: relative;
  padding-left: 20px;
  font-size: 15px;
  line-height: 28px;
}
.pop .login_popup .sidebar li:before {
  content: "";
  display: block;
  position: absolute;
  background: #fff;
  height: 10px;
  width: 10px;
  border-radius: 10px;
  left: 0;
  top: 10px;
}
.top_mobile_info {
  position: absolute;
  top: 0;
  z-index: 1;
  width: calc(100% - 60px);
  text-align: center;
  padding: 13px 30px;
  color: #fff;
  background: #9b9b9b;
  font-size: 13px;
}
@media only screen and (max-width: 440px) {
  .pop {
    position: absolute;
    z-index: 16000005;
  }
  .pop .pop_content {
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100%;
    margin: 0 !important;
    border-radius: 0;
    background: #fff !important;
    padding-top: 42px;
  }
  .pop .login_popup .pop_inner {
    width: 100%;
  }
  .pop .pop_content .sidebar {
    display: none;
  }
  .pop .pop_content.form_scroll {
    top: 0;
    bottom: 0;
  }
  .pop .close {
    left: auto;
    right: 0;
    top: 0;
  }
  .pop .close:hover {
    background-color: none;
  }
  .submitted {
    top: 0;
    margin-top: 0;
  }
  .submitted .u_ico {
    width: 100px;
    height: 100px;
    background-size: 100% 100%;
    margin-top: 40px;
  }
  .pop .error_msg {
    display: none;
  }
  .pop_height {
    overflow: auto;
  }
  .pop_height .body_inner {
    height: 800px;
    overflow: hidden;
  }
}
@media only screen and (min-width: 992px) {
  .top_mobile_info {
    display: none;
  }
}
.log_form {
  width: 251px;
  margin: 0 auto;
}
.log_form input {
  margin-bottom: 20px;
}
.log_form > .select {
  margin: 0 -13px 27px -15px;
  position: relative;
}
.log_form dl.select dt {
  line-height: 26px;
}
.log_form dl.select dd {
  top: 46px;
}
.log_form dl.select dd > span {
  text-transform: inherit;
}
.log_form dl.select dt > span:first-child {
  text-transform: inherit;
}
.log_form dl.select dt > span:first-child + span {
  top: 22px;
}
.remember_forget {
  width: 250px;
  margin: 0 auto 10px;
  height: 24px;
}
#forgot-password {
  float: right;
  font-size: 13px;
  text-align: right;
  margin-right: -15px;
}
.tgl_remember_me {
  margin-left: -15px;
  float: left;
}
.tgl_remember_me:hover > span {
  text-decoration: underline;
}
.a_reg {
  text-align: center;
  margin-top: 29px;
  margin-bottom: 32px;
}
.a_reg a {
  font-size: 15px;
  line-height: 17px;
}
.forgot_popup .pop_submit {
  margin-top: 0;
  margin-bottom: 11px;
}
.register_popup .terms_agreement {
  position: relative;
  max-width: 260px;
  margin: 0 auto;
  font-size: 9px;
  font-weight: 400;
  line-height: 1.4;
  color: #777;
  text-align: center;
}
.pop .processing {
  background-color: #9cc4d9;
  color: #c7dde9;
  cursor: default;
}
.pop .log_tip {
  width: 260px;
  margin: 0 auto;
  font-size: 11px;
  line-height: 20px;
}
.pop .forgot_popup h3 {
  padding-bottom: 19px;
}
.login_popup_before {
  display: none;
  z-index: 10;
  padding-top: 40px;
  text-align: center;
}
.pop .login_popup.login_popup_before .pop_inner {
  padding: 0 20px;
  width: calc(100% - 40px);
}
@media only screen and (min-width: 441px) {
  .pop .login_popup_before .pop_inner {
    width: 440px;
  }
}
.login_popup_before .pc_ico {
  background-image: url(../images/pc_ico.png);
  background-size: 100%;
  width: 135px;
  height: 114px;
  margin: 0 auto 30px;
}
.login_popup_before p.title {
  font-size: 23px;
  line-height: 27px;
  margin-bottom: 20px;
}
.login_popup_before p.msg {
  font-size: 15px;
  line-height: 21px;
  margin-bottom: 23px;
}
.login_popup_before a.a_close {
  font-size: 15px;
  padding: 8px 50px 9px;
  border: 2px solid #656565;
  border-radius: 4px;
  color: #2d2d2d;
  text-decoration: none;
  margin-bottom: 30px;
}
.login_popup_before .a_continue {
  display: block;
}
.global_notification {
  position: fixed;
  bottom: 13px;
  right: 13px;
  z-index: 999999;
  width: 450px;
  border: 2px solid rgba(101, 101, 101, 0.5);
  border-radius: 5px;
  overflow: hidden;
  display: none;
}
.msg_head {
  position: relative;
  height: 50px;
  background-color: #bf3338;
}
.true .msg_head {
  background-color: #3ea771;
}
.msg_head p {
  font-size: 15px;
  line-height: 17px;
  color: #fff;
  padding: 17px 60px 0;
  text-align: center;
}
.msg_head .ico {
  position: absolute;
  top: 14px;
  left: 19px;
  z-index: 1;
  width: 27px;
  height: 20px;
  background-image: url(../images/img_sprite_bb4e60a13dd34655957ae20fa8451ae2.png);
  background-position: -214px -378px;
}
.true .msg_head .ico {
  background-position: -187px -378px;
}
.msg_head .n_close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  width: 50px;
  height: 50px;
  cursor: pointer;
}
.msg_head .nc_inner {
  width: 30px;
  height: 30px;
  background-image: url(../images/img_sprite_bb4e60a13dd34655957ae20fa8451ae2.png);
  background-position: -105px -229px;
  margin: 10px 0 0 8px;
  border-radius: 4px;
}
.msg_head:hover .nc_inner {
  background-color: rgba(0, 0, 0, 0.3);
}
.msg_body {
  color: #000;
  background-color: #fff;
  overflow: hidden;
  padding: 19px 0 11px;
}
.msg_body .msg_p {
  position: relative;
  font-size: 15px;
  line-height: 20px;
  text-align: left;
  margin: 0 0 10px 21px;
  padding: 0 28px;
}
.msg_p .p_ico {
  position: absolute;
  top: 6px;
  left: 0;
  width: 10px;
  height: 10px;
  background-image: url(../images/img_sprite_bb4e60a13dd34655957ae20fa8451ae2.png);
  background-position: -242px -355px;
}
.true .msg_p .p_ico {
  background-position: -242px -378px;
}
.true .msg_p .p_ico {
  background-position: -242px -378px;
}
.ls_or {
  width: 281px;
  height: 1px;
  margin: 20px auto;
  text-align: center;
}
.ls_line {
  height: 1px;
  background-color: #ccc;
}
.or_txt {
  width: 140px;
  font-size: 15px;
  line-height: 17px;
  color: #434343;
  background-color: #fff;
  margin-top: -10px;
  display: inline-block;
  position: relative;
  top: -9px;
}
.bg_grey .pop_content {
  background-color: #fff;
}
.bg_grey h3 {
  color: #fff;
}
.bg_grey .ls_line {
  background-color: #434343;
}
.bg_grey .or_txt {
  color: #656565;
  background-color: #282727;
}
.bg_grey p {
  color: #fff;
}
@media only screen and (max-width: 440px), screen and (max-height: 740px) {
  .global_notification {
    display: none;
  }
}
