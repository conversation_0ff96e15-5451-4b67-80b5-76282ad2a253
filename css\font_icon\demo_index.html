<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">客服经理-</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">供应管理</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66f;</span>
                <div class="name">店铺管理</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe672;</span>
                <div class="name">营销推广</div>
                <div class="code-name">&amp;#xe672;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">TAT检测</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">检前处理</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">追踪检测</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe749;</span>
                <div class="name">vuesax-bold-empty-wallet-tick</div>
                <div class="code-name">&amp;#xe749;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74a;</span>
                <div class="name">vuesax-bold-money-4</div>
                <div class="code-name">&amp;#xe74a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74b;</span>
                <div class="name">vuesax-bold-card-edit</div>
                <div class="code-name">&amp;#xe74b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74c;</span>
                <div class="name">vuesax-bold-receipt-edit</div>
                <div class="code-name">&amp;#xe74c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74d;</span>
                <div class="name">vuesax-bold-receipt-search</div>
                <div class="code-name">&amp;#xe74d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74e;</span>
                <div class="name">vuesax-bold-tag-2</div>
                <div class="code-name">&amp;#xe74e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74f;</span>
                <div class="name">vuesax-bold-wallet-check</div>
                <div class="code-name">&amp;#xe74f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe750;</span>
                <div class="name">vuesax-bold-wallet-search</div>
                <div class="code-name">&amp;#xe750;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe769;</span>
                <div class="name">vuesax-bold-sms-edit</div>
                <div class="code-name">&amp;#xe769;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76a;</span>
                <div class="name">vuesax-bold-message-tick</div>
                <div class="code-name">&amp;#xe76a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76b;</span>
                <div class="name">vuesax-bold-sms-search</div>
                <div class="code-name">&amp;#xe76b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76c;</span>
                <div class="name">vuesax-bold-location-tick</div>
                <div class="code-name">&amp;#xe76c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76f;</span>
                <div class="name">vuesax-bold-brifecase-tick</div>
                <div class="code-name">&amp;#xe76f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe780;</span>
                <div class="name">vuesax-bold-edit-2</div>
                <div class="code-name">&amp;#xe780;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79d;</span>
                <div class="name">vuesax-bold-search-status-1</div>
                <div class="code-name">&amp;#xe79d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79c;</span>
                <div class="name">vuesax-bold-personalcard</div>
                <div class="code-name">&amp;#xe79c;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.ttf?t=1729828749071') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-kefujingli-"></span>
            <div class="name">
              客服经理-
            </div>
            <div class="code-name">.icon-kefujingli-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongying"></span>
            <div class="name">
              供应管理
            </div>
            <div class="code-name">.icon-gongying
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianpu"></span>
            <div class="name">
              店铺管理
            </div>
            <div class="code-name">.icon-dianpu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingxiaotuiguang"></span>
            <div class="name">
              营销推广
            </div>
            <div class="code-name">.icon-yingxiaotuiguang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-TATjiancejiance"></span>
            <div class="name">
              TAT检测
            </div>
            <div class="code-name">.icon-TATjiancejiance
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jianqianchulichulijiancha"></span>
            <div class="name">
              检前处理
            </div>
            <div class="code-name">.icon-jianqianchulichulijiancha
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuizongjiancebiaobenzhuizongjiance"></span>
            <div class="name">
              追踪检测
            </div>
            <div class="code-name">.icon-zhuizongjiancebiaobenzhuizongjiance
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-empty-wallet-tick"></span>
            <div class="name">
              vuesax-bold-empty-wallet-tick
            </div>
            <div class="code-name">.icon-vuesax-bold-empty-wallet-tick
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-money-4"></span>
            <div class="name">
              vuesax-bold-money-4
            </div>
            <div class="code-name">.icon-vuesax-bold-money-4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-card-edit"></span>
            <div class="name">
              vuesax-bold-card-edit
            </div>
            <div class="code-name">.icon-vuesax-bold-card-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-receipt-edit"></span>
            <div class="name">
              vuesax-bold-receipt-edit
            </div>
            <div class="code-name">.icon-vuesax-bold-receipt-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-receipt-search"></span>
            <div class="name">
              vuesax-bold-receipt-search
            </div>
            <div class="code-name">.icon-vuesax-bold-receipt-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-tag-2"></span>
            <div class="name">
              vuesax-bold-tag-2
            </div>
            <div class="code-name">.icon-vuesax-bold-tag-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-wallet-check"></span>
            <div class="name">
              vuesax-bold-wallet-check
            </div>
            <div class="code-name">.icon-vuesax-bold-wallet-check
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-wallet-search"></span>
            <div class="name">
              vuesax-bold-wallet-search
            </div>
            <div class="code-name">.icon-vuesax-bold-wallet-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-sms-edit"></span>
            <div class="name">
              vuesax-bold-sms-edit
            </div>
            <div class="code-name">.icon-vuesax-bold-sms-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-message-tick"></span>
            <div class="name">
              vuesax-bold-message-tick
            </div>
            <div class="code-name">.icon-vuesax-bold-message-tick
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-sms-search"></span>
            <div class="name">
              vuesax-bold-sms-search
            </div>
            <div class="code-name">.icon-vuesax-bold-sms-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-location-tick"></span>
            <div class="name">
              vuesax-bold-location-tick
            </div>
            <div class="code-name">.icon-vuesax-bold-location-tick
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-brifecase-tick"></span>
            <div class="name">
              vuesax-bold-brifecase-tick
            </div>
            <div class="code-name">.icon-vuesax-bold-brifecase-tick
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-edit-2"></span>
            <div class="name">
              vuesax-bold-edit-2
            </div>
            <div class="code-name">.icon-vuesax-bold-edit-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-search-status-1"></span>
            <div class="name">
              vuesax-bold-search-status-1
            </div>
            <div class="code-name">.icon-vuesax-bold-search-status-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vuesax-bold-personalcard"></span>
            <div class="name">
              vuesax-bold-personalcard
            </div>
            <div class="code-name">.icon-vuesax-bold-personalcard
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kefujingli-"></use>
                </svg>
                <div class="name">客服经理-</div>
                <div class="code-name">#icon-kefujingli-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongying"></use>
                </svg>
                <div class="name">供应管理</div>
                <div class="code-name">#icon-gongying</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianpu"></use>
                </svg>
                <div class="name">店铺管理</div>
                <div class="code-name">#icon-dianpu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingxiaotuiguang"></use>
                </svg>
                <div class="name">营销推广</div>
                <div class="code-name">#icon-yingxiaotuiguang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-TATjiancejiance"></use>
                </svg>
                <div class="name">TAT检测</div>
                <div class="code-name">#icon-TATjiancejiance</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianqianchulichulijiancha"></use>
                </svg>
                <div class="name">检前处理</div>
                <div class="code-name">#icon-jianqianchulichulijiancha</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuizongjiancebiaobenzhuizongjiance"></use>
                </svg>
                <div class="name">追踪检测</div>
                <div class="code-name">#icon-zhuizongjiancebiaobenzhuizongjiance</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-empty-wallet-tick"></use>
                </svg>
                <div class="name">vuesax-bold-empty-wallet-tick</div>
                <div class="code-name">#icon-vuesax-bold-empty-wallet-tick</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-money-4"></use>
                </svg>
                <div class="name">vuesax-bold-money-4</div>
                <div class="code-name">#icon-vuesax-bold-money-4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-card-edit"></use>
                </svg>
                <div class="name">vuesax-bold-card-edit</div>
                <div class="code-name">#icon-vuesax-bold-card-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-receipt-edit"></use>
                </svg>
                <div class="name">vuesax-bold-receipt-edit</div>
                <div class="code-name">#icon-vuesax-bold-receipt-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-receipt-search"></use>
                </svg>
                <div class="name">vuesax-bold-receipt-search</div>
                <div class="code-name">#icon-vuesax-bold-receipt-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-tag-2"></use>
                </svg>
                <div class="name">vuesax-bold-tag-2</div>
                <div class="code-name">#icon-vuesax-bold-tag-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-wallet-check"></use>
                </svg>
                <div class="name">vuesax-bold-wallet-check</div>
                <div class="code-name">#icon-vuesax-bold-wallet-check</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-wallet-search"></use>
                </svg>
                <div class="name">vuesax-bold-wallet-search</div>
                <div class="code-name">#icon-vuesax-bold-wallet-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-sms-edit"></use>
                </svg>
                <div class="name">vuesax-bold-sms-edit</div>
                <div class="code-name">#icon-vuesax-bold-sms-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-message-tick"></use>
                </svg>
                <div class="name">vuesax-bold-message-tick</div>
                <div class="code-name">#icon-vuesax-bold-message-tick</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-sms-search"></use>
                </svg>
                <div class="name">vuesax-bold-sms-search</div>
                <div class="code-name">#icon-vuesax-bold-sms-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-location-tick"></use>
                </svg>
                <div class="name">vuesax-bold-location-tick</div>
                <div class="code-name">#icon-vuesax-bold-location-tick</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-brifecase-tick"></use>
                </svg>
                <div class="name">vuesax-bold-brifecase-tick</div>
                <div class="code-name">#icon-vuesax-bold-brifecase-tick</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-edit-2"></use>
                </svg>
                <div class="name">vuesax-bold-edit-2</div>
                <div class="code-name">#icon-vuesax-bold-edit-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-search-status-1"></use>
                </svg>
                <div class="name">vuesax-bold-search-status-1</div>
                <div class="code-name">#icon-vuesax-bold-search-status-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vuesax-bold-personalcard"></use>
                </svg>
                <div class="name">vuesax-bold-personalcard</div>
                <div class="code-name">#icon-vuesax-bold-personalcard</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
