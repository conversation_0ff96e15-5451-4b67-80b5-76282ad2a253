/* Hero section */
#hero {
    background-color: #080808;
    color: #000000;
    text-align: center; 
    height: 100vh;
    min-width: auto; /* 移除最小宽度限制 */
    width: 100%;
    background-image: linear-gradient(to bottom, rgba(2, 27, 55, 0) 0%,rgba(2, 27, 55, 0) 95%, rgba(2, 27, 55) 120%), url('./imgs/40.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    box-sizing: border-box;
    position: relative; /* 添加这行 */
    overflow: hidden; /* 添加这行，防止粒子溢出 */
    
    /* 添加以下属性 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 60px; /* 保留顶部内边距，为header留出空间 */
}

#hero > h1 {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    margin-top: 0; /* 移除顶部外边距 */
}

#hero > p {
    max-width: 900px;
    margin: 0 auto;
    font-size: 1.2rem;
}

/* 修改 .hero-feature 样式 */
.hero-feature {
    position: absolute;
    bottom: 150px;
    left: 50%;
    transform: translateX(-50%);
    text-align: left;
    width: 100%; /* 改为100% */
    max-width: 1315px; /* 添加最大宽度 */
    padding: 20px 15px; /* 添加内边距 */
    box-sizing: border-box;
    opacity: 1; /* 将透明度设置为1，使其可见 */
    transition: opacity 0.3s ease, transform 0.3s ease;
    z-index: 10; /* 将 z-index 提高到一个更高的值 */
}

.hero-feature h2 {
    font-size: 40px;
    color: #000000;
    margin-bottom: 10px;
}

.hero-feature p {
    font-size: 16px;
    color: #000000;
    margin-bottom: 15px; /* 减小段落的下边距 */
    max-width: 100%;
    margin-left: 0; /* 重置左边距 */
    margin-right: 0; /* 重置右边距 */
}

/* 修改 .hero-buttons 样式 */
.hero-buttons {
    display: flex;
    gap: 10px;
    margin-top: -8px; /* 添加负的上边距来向上移动按钮 */
}

/* 如果需要，可以调整按钮本身的样式 */
.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: white;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary:hover {
    background-color: #f8f9fa;
}

/* 添加这个新的样式 */
#particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1; /* 确保粒子在背景之上，但在内容之下 */
}

/* 确保 hero 中的其他元素在粒子之上，但在 hero-feature 之下 */
#hero > *:not(#particles-container):not(.hero-feature) {
    position: relative;
    z-index: 5;
}

/* 添加这个样式来确保 hero 的内容在正确的层级 */
#hero h1,
#hero > p {
    position: relative;
    z-index: 5;
}
