/* 核心优势部分样式 */
#about {
    display: flex;
    padding: 95px 0 105px 0;
    background-color: #f8f9fa;
    min-width: 100%; /* 改为100%，而不是固定宽度 */
    justify-content: center;
    position: relative;
    top: 0;
}

.about-content {
    width: 1200px;
    text-align: center;
    padding: 20px 0;
    height: 380px;
}

.about-content h2 {
    font-size: 32px;
    margin-bottom: 62px;
}

.core-advantages {
    display: flex;
    justify-content: space-between;
    gap: 30px;
}

.advantage-item {
    flex: 1;
    text-align: center;
    padding: 40px 20px 20px;
    background-color: #ffffff;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

/* .advantage-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
} */

.advantage-icon {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    transition: transform 0.3s ease; /* 添加过渡效果 */
}

.advantage-icon:hover {
    transform: translateX(-50%) translateY(-5px); /* 添加向上浮动效果 */
}

.advantage-icon img {
    width: 80px;
    height: 80px;
}

.advantage-item h3 {
    font-size: 21px;
    margin-top: 15px;
    margin-bottom: 10px;
    color: #333;
}

.advantage-item p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.8;
    color: #333;
    font-size: 16px;
}

/* Global styles */
.btn-primary {
    background-color: #007bff;
    color: #fff;
    padding: 0.5rem 1.2rem;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-primary {
    display: inline-block;
    margin-top: 1rem;
}

/* Add any other global styles here */
.about-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }
  
  .about-title {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
  }
  
  .about-title h2 {
    font-size: 36px;
    color: #2981e5;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
  }
  
  .about-title h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #007bff);
  }
  
  .about-section {
    margin-bottom: 50px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    padding: 30px;
    transition: all 0.3s ease;
  }
  
  .about-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  }
  
  .about-section h3 {
    font-size: 24px;
    color: #007bff;
    margin-bottom: 20px;
    font-style: normal !important;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    position: relative;
  }
  
  .about-section h3:before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 3px;
    background: #007bff;
    transition: all 0.3s ease; /* 添加过渡效果 */
  }
  
  .about-section h3:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 60px;
    right: 0;
    height: 1px;
    background: #007bff;
    transition: all 0.3s ease; /* 添加过渡效果 */
  }
  
  /* 修改悬停效果，保持边框不变 */
  .about-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  }
  
  /* 确保悬停时边框不变粗 */
  .about-section:hover h3:before,
  .about-section:hover h3:after {
    height: 3px; /* 保持 before 元素高度不变 */
  }
  
  .about-section:hover h3:after {
    height: 1px; /* 保持 after 元素高度不变 */
  }
  
  .about-section p {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
    margin-bottom: 20px;
  }
  
  .about-section ul {
    padding-left: 20px;
  }
  
  .about-section ul li {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
    margin-bottom: 10px;
    position: relative;
    list-style-type: none;
    padding-left: 25px;
  }
  
  .about-section ul li:before {
    content: '✓';
    color: #007bff;
    position: absolute;
    left: 0;
    font-weight: bold;
  }
  
  .product-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
  }
  
  .product-card {
    flex: 1 1 calc(50% - 10px);
    min-width: 300px;
    background: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #007bff;
  }
  
  .product-card strong {
    display: block;
    font-size: 18px;
    color: #007bff;
    margin-bottom: 10px;
  }
  
  .vision-box {
    background: linear-gradient(135deg, rgba(56, 168, 111, 0.05), rgba(47, 136, 148, 0.05));
    border-radius: 10px;
    padding: 30px;
    text-align: center;
  }
  
  .vision-box p {
    font-size: 18px;
    line-height: 1.8;
    color: #444;
  }
  
  @media (max-width: 768px) {
    .product-card {
      flex: 1 1 100%;
    }
    
    .about-section {
      padding: 20px;
    }
  }

  .scrolled {
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}