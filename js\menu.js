document.addEventListener('DOMContentLoaded', function() {
    // 获取所有菜单项
    const menuItems = document.querySelectorAll('.kbUcWB');
    
    // 定位下拉菜单的函数
    function positionDropdowns() {
        menuItems.forEach(item => {
            const menuButton = item.querySelector('span') || item.querySelector('a');
            const dropdown = item.querySelector('.hjLztF');
            
            if (dropdown && menuButton) {
                const buttonRect = menuButton.getBoundingClientRect();
                
                // 获取导航容器的位置
                const navContainer = document.querySelector('.NDhqQ');
                let leftOffset = buttonRect.left;
                
                // 为大屏幕调整位置
                if (window.innerWidth >= 1400) {
                    if (navContainer) {
                        const navRect = navContainer.getBoundingClientRect();
                        // 确保菜单左侧对齐于导航项
                        leftOffset = buttonRect.left;
                    }
                }
                
                // 设置菜单位置
                dropdown.style.left = leftOffset + 'px';
                
                // 确保不会超出右侧边界
                setTimeout(() => {
                    const dropdownRect = dropdown.getBoundingClientRect();
                    const windowWidth = window.innerWidth;
                    if (dropdownRect.right > windowWidth - 20) {
                        dropdown.style.left = (windowWidth - dropdownRect.width - 20) + 'px';
                    }
                    
                    // 确保不会超出左侧边界
                    if (parseFloat(dropdown.style.left) < 20) {
                        dropdown.style.left = '20px';
                    }
                }, 10);
            }
        });
    }
    
    // 初始定位和设置重定位
    setTimeout(positionDropdowns, 100);
    menuItems.forEach(item => {
        item.addEventListener('mouseenter', positionDropdowns);
    });
    window.addEventListener('resize', positionDropdowns);
    
    // 修复菜单闪烁问题
    menuItems.forEach(item => {
        const dropdown = item.querySelector('.hjLztF');
        if (dropdown) {
            // 防止菜单闪烁
            item.addEventListener('mouseleave', function() {
                // 确保菜单平滑消失
                dropdown.style.transition = 'opacity 0.3s';
            });
        }
    });
});