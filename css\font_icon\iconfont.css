@font-face {
  font-family: "iconfont"; /* Project id  */
  src: url('iconfont.ttf?t=1729828749071') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-kefujingli-:before {
  content: "\e660";
}

.icon-gongying:before {
  content: "\e66e";
}

.icon-dianpu:before {
  content: "\e66f";
}

.icon-yingxiaotuiguang:before {
  content: "\e672";
}

.icon-TATjiancejiance:before {
  content: "\e604";
}

.icon-jianqianchulichulijiancha:before {
  content: "\e605";
}

.icon-zhuizongjiancebiaobenzhuizongjiance:before {
  content: "\e60a";
}

.icon-vuesax-bold-empty-wallet-tick:before {
  content: "\e749";
}

.icon-vuesax-bold-money-4:before {
  content: "\e74a";
}

.icon-vuesax-bold-card-edit:before {
  content: "\e74b";
}

.icon-vuesax-bold-receipt-edit:before {
  content: "\e74c";
}

.icon-vuesax-bold-receipt-search:before {
  content: "\e74d";
}

.icon-vuesax-bold-tag-2:before {
  content: "\e74e";
}

.icon-vuesax-bold-wallet-check:before {
  content: "\e74f";
}

.icon-vuesax-bold-wallet-search:before {
  content: "\e750";
}

.icon-vuesax-bold-sms-edit:before {
  content: "\e769";
}

.icon-vuesax-bold-message-tick:before {
  content: "\e76a";
}

.icon-vuesax-bold-sms-search:before {
  content: "\e76b";
}

.icon-vuesax-bold-location-tick:before {
  content: "\e76c";
}

.icon-vuesax-bold-brifecase-tick:before {
  content: "\e76f";
}

.icon-vuesax-bold-edit-2:before {
  content: "\e780";
}

.icon-vuesax-bold-search-status-1:before {
  content: "\e79d";
}

.icon-vuesax-bold-personalcard:before {
  content: "\e79c";
}

