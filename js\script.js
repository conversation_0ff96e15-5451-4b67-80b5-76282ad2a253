// 打开模态框的函数
function openModal(modal) {
    modal.style.display = 'block';
    setTimeout(() => modal.classList.add('show'), 10);
}

// 关闭模态框的函数
function closeModal(modal) {
    modal.classList.remove('show');
    setTimeout(() => modal.style.display = 'none', 300);
}

// 当 DOM 内容加载完成后执行的主函数
document.addEventListener('DOMContentLoaded', function() {
    // 处理下拉菜单
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        dropdown.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {  // 仅在移动设备上处理点击事件
                e.preventDefault();
                this.querySelector('.dropdown-content').classList.toggle('show');
            }
        });
    });

    // 点击页面其他地方时关闭下拉菜单
    document.addEventListener('click', function(e) {
        if (!e.target.matches('.dropdown, .dropdown *')) {
            document.querySelectorAll('.dropdown-content.show').forEach(openDropdown => {
                openDropdown.classList.remove('show');
            });
        }
    });

    const navLinks = document.querySelectorAll('nav ul li a');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 移除所有链接的 active 类
            navLinks.forEach(l => l.classList.remove('active'));
            // 为被点击的链接添加 active 
            this.classList.add('active');
        });
    });

    // 保留卡片容器和hero-feature相关的代码
    const scrollDots = document.querySelectorAll('.scroll-dots .dot');
    const allSections = ['hero', 'features', 'about', 'team', 'build'];
    let isAnimating = false;
    let lastScrollTop = 0;

    function smoothScrollTo(targetId) {
        const target = document.getElementById(targetId);
        const start = window.pageYOffset;
        let targetPosition;

        if (targetId === 'features') {
            // 计算 hero 背景图剩余450px时的位置
            const hero = document.getElementById('hero');
            targetPosition = hero.offsetHeight - 500;
        } else {
            targetPosition = target.getBoundingClientRect().top + start;
        }

        const startTime = 'now' in window.performance ? performance.now() : new Date().getTime();

        function scroll() {
            const currentTime = 'now' in window.performance ? performance.now() : new Date().getTime();
            const animationDuration = 350;
            const time = Math.min(1, ((currentTime - startTime) / animationDuration));
            const ease = function(t) { return t<.5 ? 2*t*t : -1+(4-2*t)*t };
            window.scrollTo(0, start + (targetPosition - start) * ease(time));

            if (time < 1) {
                requestAnimationFrame(scroll);
            } else {
                isAnimating = false;
            }
        }

        isAnimating = true;
        requestAnimationFrame(scroll);
    }

    function handleScroll() {
        if (isAnimating) return;

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const hero = document.getElementById('hero');
        const heroHeight = hero.offsetHeight;
        const triggerPosition = heroHeight - 500;

        // 检测滚动方向
        const scrollDirection = scrollTop > lastScrollTop ? 'down' : 'up';

        // 在 hero 和 features 触发点之间执行动画
        if (scrollTop <= triggerPosition) {
            if (scrollDirection === 'down' && scrollTop < triggerPosition) {
                // 向下滚动，立即滚动到触发点
                smoothScrollTo('features');
            } else if (scrollDirection === 'up' && scrollTop > 0) {
                // 向上滚动，立即滚动到 hero
                smoothScrollTo('hero');
            }
        }

        lastScrollTop = scrollTop;
        updateScrollDots();
    }

    function updateScrollDots() {
        const scrollPosition = window.scrollY;
        const windowHeight = window.innerHeight;
        const hero = document.getElementById('hero');
        const heroHeight = hero.offsetHeight;
        const features = document.getElementById('features');
        const about = document.getElementById('about');
        const team = document.getElementById('team');
        const build = document.getElementById('build');
        const dots = document.querySelectorAll('.scroll-dots .dot');

        const triggerPositions = [
            0,
            heroHeight - 500,
            about.offsetTop - windowHeight / 2,
            team.offsetTop - windowHeight / 2,
            build.offsetTop - windowHeight / 2
        ];

        dots.forEach((dot, index) => {
            if (scrollPosition >= triggerPositions[index] && 
                (index === triggerPositions.length - 1 || scrollPosition < triggerPositions[index + 1])) {
                dot.classList.add('active');
            } else {
                dot.classList.remove('active');
            }
        });
    }

    // 添加一个防��函数来优化滚动事件
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 使用防抖函数包装 handleScroll
    const debouncedHandleScroll = debounce(handleScroll, 50);

    // 更新事件监听器
    window.addEventListener('scroll', debouncedHandleScroll);
    updateScrollDots(); // 初始化滚动点状态

    // 确保在页面加载和滚动时调用这个函数
    window.addEventListener('load', updateScrollDots);
    window.addEventListener('scroll', updateScrollDots);

    const heroFeature = document.querySelector('.hero-feature');
    const hero = document.getElementById('hero');

    function updateHeroFeatureVisibility() {
        const scrollPosition = window.scrollY;
        const heroHeight = hero.offsetHeight;
        const triggerPosition = heroHeight - 500;

        if (scrollPosition >= triggerPosition) {
            //heroFeature.style.opacity = '1';
            //heroFeature.style.transform = 'translateX(-50%) translateY(0)';
        } else {
            //heroFeature.style.opacity = '0';
            //heroFeature.style.transform = 'translateX(-50%) translateY(20px)';
        }
    }

    // 在页面加载时初始化 hero-feature 的可见性
    updateHeroFeatureVisibility();

    // 添加滚动事件监听器
    window.addEventListener('scroll', updateHeroFeatureVisibility);

    function updateFeatureCardsVisibility() {
        const scrollPosition = window.scrollY;
        const heroHeight = hero.offsetHeight;
        const triggerPosition = heroHeight - 500;

        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach((card, index) => {
            if (scrollPosition >= triggerPosition) {
                setTimeout(() => {
                    card.classList.add('visible');
                }, index * 100); // 添加延迟，使卡片逐个出现
            } else {
                card.classList.remove('visible');
            }
        });
    }

    // 在页面加载时初始化 feature-cards 的可见性
    updateFeatureCardsVisibility();

    // 添加滚动事件监听器
    window.addEventListener('scroll', updateFeatureCardsVisibility);

    // 应用层面模块的功能
    const buildFeatureItems = document.querySelectorAll('.build-feature-item');
    const buildFeatureDescription = document.getElementById('build-feature-description');

    const buildDescriptions = {
        '技术层面': {
            title: '规则依赖性',
            description: '<span class="highlight">传统审校：</span>依赖于预设规则，缺乏对语境和复杂语义的理解能力，对于模糊不清或新颖的表达方式难以准确判断。',
            features: [
                '<span class="highlight">智能审校：</span>通过深度学习和海量数据训练，能够理解文本的上下文和语义，提供更为灵活和准确的审核结果。'
            ],
            featureTitle: '上下文理解能力'
        },
        '操作层面': {
            title: '人工成本高',
            description: '<span class="highlight">传统审校：</span>依赖专业人工进行，成本高且效率受限，特别是在处理大量数据时，人工审核容易出错且速度慢。',
            features: [
                '<span class="highlight">智能审校：</span>能够自动化处理大量文本数据，快速完成审核任务，大幅提升审核效率和处理能力。'
            ],
            featureTitle: '高效自动化处理'
        },
        '知识管理层面': {
            title: '知识更新慢',
            description: '<span class="highlight">知传统审校：</span>规则库更新周期长，难以及时应对新兴的内容形式和不断变化的合规要求，会出现审核标准滞后的问题。',
            features: [
                '<span class="highlight">智能审校：</span>具有自我学习和优化能力，能够根据最新的数据和反馈不断调整和改进，保持审核标准的时效性和准确性。'
            ],
            featureTitle: '持续学习与自我优化'
        }
    };

    function updateBuildDescription(featureName) {
        const feature = buildDescriptions[featureName];
        const content = `
            <div class="feature-comparison">
                <div class="feature-side traditional">
                    <h3>${feature.title}</h3>
                    <p>${feature.description}</p>
                </div>
                <div class="vs-divider">VS</div>
                <div class="feature-side intelligent">
                    <h3>${feature.featureTitle}</h3>
                    <p>${feature.features[0]}</p>
                </div>
            </div>
        `;
        buildFeatureDescription.innerHTML = content;
    }

    function setActiveBuildFeature(clickedItem) {
        buildFeatureItems.forEach(item => item.classList.remove('active'));
        clickedItem.classList.add('active');
    }

    buildFeatureItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除所有项目的 active 类
            buildFeatureItems.forEach(i => i.classList.remove('active'));
            
            // 为被点击的项目添加 active 类
            this.classList.add('active');

            // 更新描述内容（这部分逻辑保持不变）
            const feature = this.getAttribute('data-feature');
            updateBuildDescription(feature);
        });
    });

    // 默认显示第一个功能的描述并设置为活跃状态
    const defaultBuildFeature = buildFeatureItems[0];
    updateBuildDescription(defaultBuildFeature.getAttribute('data-feature'));
    setActiveBuildFeature(defaultBuildFeature);

    // 移除或修改可能导致问题的全局点击事件监听器
    // 如果有类似下面的代码，请删除或修改它
    // document.addEventListener('click', function(e) {
    //     // 跳转到 hero 部分的代码
    // });

    // 为页脚链接添加正确的行为
    const footerLinks = document.querySelectorAll('footer a');
    footerLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 如果链接href为#，则阻止默认行为
            if (this.getAttribute('href') === '#') {
                e.preventDefault();
                console.log('链接被点击，但没有指定目标页面');
            }
            // 否则，让链接正常工作（跳转到指定页面）
        });
    });
});
