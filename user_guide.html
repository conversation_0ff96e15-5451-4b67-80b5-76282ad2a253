<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>版慎通-智能审校</title>
    <link rel="stylesheet" href="css/user_guide.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="stylesheet" href="css/menu.css">
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    <script src="js/menu.js" defer></script>
    </head> 
 <body class="frontpage about user-guide-active">

   <div class="body_inner pushable"> 
    <header class="sc-73348620-5 fPTAHN">
    <div class="sc-73348620-6 NDhqQ">
     <div class="sc-73348620-0 fmyafy">
      <a href="/"><img src="images/logo3.png" alt="Logo" style="height: 36px; width: auto; vertical-align: middle; margin-top: 5px; display: inline-block;"></a>
      <nav class="sc-b6ff7819-3 hkfElN">
        <menu>
         <li class="sc-b6ff7819-2 kbUcWB"><a href="index.html" class="sc-11d92d2b-0 sc-11d92d2b-1 sc-b6ff7819-0 gNojVm iiTsSz cVSLsi">首页</a></li>
         <li class="sc-b6ff7819-2 kbUcWB"><span class="sc-11d92d2b-0 sc-11d92d2b-1 sc-b6ff7819-0 gNojVm iiTsSz cVSLsi">产品<span class="sc-11d92d2b-3 hrnfxV"></span></span>
          <div class="sc-28b433d-0 hjLztF">
           <div class="sc-28b433d-1 hnSjjg">
            <div class="sc-28b433d-6 iqQoYf">
                <menu class="sc-28b433d-7 iUUtFN">
                 <li><a href="product.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/sjxt.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>文档解析</span>
                 <span>智能识别错误，提升内容质量</span>
                </div></a></li>
              <li><a href="p2.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/bzxt.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>标注系统</span>
                 <span>精准标记内容</span>
                </div></a></li>
              <li><a href="p3.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/bgxt.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>编改系统</span>
                 <span>智能优化文本，一键完善内容</span>
                </div></a></li>
             </menu>
             <menu class="sc-28b433d-7 iUUtFN">
                 <li><a href="p4.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/zgxt.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>撰稿系统</span>
                 <span>AI辅助创作</span>
                </div></a></li>
             </menu>
            </div>
            <div class="sc-28b433d-3 eQgydm">
              <a href="play_video.html" class="sc-28b433d-5 kSIJTA">查看演示<span class="sc-28b433d-4 kOCa-dZ"></span></a>
           </div>
           </div>
           <div class="sc-28b433d-2 eLZOnL">
            <div>
             <a href="info.html" class="sc-2267ac9f-1 drmxbQ">
              <div class="sc-2267ac9f-2 hdSKDN">
               <img src="images/icon/demo.png" alt="预约体验">
              </div>
              <div class="sc-2267ac9f-3 lcfxrO">
               <span>预约体验</span>
               <span>探索团队解决方案，提升业务效率</span>
              </div></a>
            </div>
            <div>
             <a href="javascript:void(0);" class="sc-a41775ac-0 jwxDuG" style="pointer-events: none; cursor: default; text-decoration: none;"><img src="images/banner/chanpin.png" alt="API接入"><span>API接入</span></a>
            </div>
           </div>
          </div></li>
         <li class="sc-b6ff7819-2 kbUcWB"><span class="sc-11d92d2b-0 sc-11d92d2b-1 sc-b6ff7819-0 gNojVm iiTsSz cVSLsi">功能<span class="sc-11d92d2b-3 hrnfxV"></span></span>
          <div class="sc-28b433d-0 hjLztF">
           <div class="sc-28b433d-1 hnSjjg">
            <div class="sc-28b433d-6 iqQoYf">
             <menu class="sc-28b433d-7 iUUtFN">
                 <li><a href="features1.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/sxwfx.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>上下文分析</span>
                 <span>深度理解语境，避免断章取义</span>
                </div></a></li>
              <li><a href="features2.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/nraq.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>内容安全</span>
                 <span>全方位守护</span>
                </div></a></li>
              <li><a href="features3.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/ysxt.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>敏感信息</span>
                 <span>精准把握导向</span>
                </div></a></li>
             </menu>
             <menu class="sc-28b433d-7 iUUtFN">
                 <li><a href="features4.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/zscc.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>知识差错</span>
                 <span>专业知识校验</span>
                </div></a></li>
              <li><a href="features5.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/yfjcjy.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>语法基础校验</span>
                 <span>提升表达准确性</span>
                </div></a></li>
              <li><a href="features6.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/fhszbz.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>符号数值标准</span>
                 <span>规范符号使用，保证专业呈现</span>
                </div></a></li>
             </menu>
            </div>
            <div class="sc-28b433d-3 eQgydm">
                <a href="play_video.html" class="sc-28b433d-5 kSIJTA">查看演示<span class="sc-28b433d-4 kOCa-dZ"></span></a>
               </div>
              </div>
              <div class="sc-28b433d-2 eLZOnL">
               <div>
                <a href="info.html" class="sc-2267ac9f-1 drmxbQ">
                 <div class="sc-2267ac9f-2 hdSKDN">
                  <img src="images/icon/demo_blue.png" alt="预约体验">
                 </div>
                 <div class="sc-2267ac9f-3 lcfxrO">
                  <span>预约体验</span>
                  <span>探索团队解决方案，提升业务效率</span>
                 </div></a>
               </div>
               <div>
                <a href="javascript:void(0);" class="sc-a41775ac-0 jwxDuG" style="pointer-events: none; cursor: default; text-decoration: none;"><img src="images/banner/gongneng.png" alt="审校功能持续研发"><span>审校功能持续研发</span></a>
               </div>
              </div>
        </div></li>
         <li class="sc-b6ff7819-2 kbUcWB"><span class="sc-11d92d2b-0 sc-11d92d2b-1 sc-b6ff7819-0 gNojVm iiTsSz cVSLsi">资料<span class="sc-11d92d2b-3 hrnfxV"></span></span>
          <div class="sc-28b433d-0 hjLztF">
           <div class="sc-28b433d-1 hnSjjg">
            <div class="sc-28b433d-6 iqQoYf">
              <menu class="sc-28b433d-7 iUUtFN">
              <li><a href="user_guide.html" class="sc-2267ac9f-1 drmxbQ"> <!-- 修改为指向 user_guide.html -->
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/sysc.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>使用手册</span>
                 <span>详尽操作指南</span>
                </div></a></li>
              <li><a href="wpaper.html" class="sc-2267ac9f-1 drmxbQ">
                <div class="sc-2267ac9f-2 hdXGxQ">
                 <img src="images/icon/cpbps.png" alt="">
                </div>
                <div class="sc-2267ac9f-3 lcfxrO">
                 <span>产品白皮书</span>
                 <span>深度剖析产品价值</span>
                </div></a></li>
             
                <li><a href="play_video.html" class="sc-2267ac9f-1 drmxbQ">
                  <div class="sc-2267ac9f-2 hdXGxQ">
                   <img src="images/icon/cpys.png" alt="">
                  </div>
                  <div class="sc-2267ac9f-3 lcfxrO">
                   <span>产品演示</span>
                   <span>直观功能展示</span>
                  </div></a></li>
               </menu>
            </div>
            <div class="sc-28b433d-3 eQgydm">
                <a href="play_video.html" class="sc-28b433d-5 kSIJTA">查看演示<span class="sc-28b433d-4 kOCa-dZ"></span></a>
               </div>
              </div>
              <div class="sc-28b433d-2 eLZOnL">
               <div>
                <a href="info.html" class="sc-2267ac9f-1 drmxbQ">
                 <div class="sc-2267ac9f-2 hdSKDN">
                  <img src="images/icon/demo.png" alt="预约体验">
                 </div>
                 <div class="sc-2267ac9f-3 lcfxrO">
                  <span>预约体验</span>
                  <span>探索团队解决方案，提升业务效率</span>
                 </div></a>
               </div>
               <div>
                <a href="javascript:void(0);" class="sc-a41775ac-0 jwxDuG" style="pointer-events: none; cursor: default; text-decoration: none;"><img src="images/banner/ziliao.png" alt="审校功能持续研发"><span>审校功能持续研发</span></a>
               </div>
              </div>
          </div></li>
         <li class="sc-b6ff7819-2 kbUcWB"><a href="about.html" class="sc-11d92d2b-0 sc-11d92d2b-1 sc-b6ff7819-0 sc-b6ff7819-1 gNojVm iiTsSz cVSLsi guhIJf">关于我们</a></li>
         <li class="sc-b6ff7819-2 kbUcWB"><a href="https://ai.publishguard.com" target="_blank" class="sc-11d92d2b-0 sc-11d92d2b-1 sc-b6ff7819-0 sc-b6ff7819-1 gNojVm iiTsSz cVSLsi guhIJf">在线体验</a></li>
      
        </menu>
       </nav>
     </div>
   </header>
    <div class="user-guide-container">
        <!-- 左侧目录区域 -->
        <div class="sidebar">
            <h2>目录</h2>
            <ul class="menu">
                <li class="active"><a href="#introduction">产品介绍</a></li>
                <li><a href="#getting-started">快速入门</a></li>
                <li><a href="#features">功能详解</a></li>
                <li><a href="#faq">常见问题</a></li>
                <li><a href="#troubleshooting">故障排除</a></li>
                <li><a href="#updates">更新日志</a></li>
            </ul>
        </div>
        
        <!-- 右侧内容区域 -->
        <div class="content-area">
            <section id="introduction">
                <h1>版慎通-智能审校系统</h1>
                <p>版慎通智能审校系统是江西朗知中文数智科技有限公司（简称"朗知中文"）基于大语言模型与自研AI技术，为出版行业深度定制的智能审校整体解决方案。系统完整覆盖从安全筛查、内容审核、编改预处理和文本校对的全流程，大幅提升图书出版质量与效率。</p>
            </section>
            
            <section id="getting-started">
                <h2>快速入门</h2>
                <div class="step-guide">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>登录并进入版慎通智能审校系统</h3>
                            <p>使用您的账号和密码登录版慎通系统。如果您还没有账号，请联系管理员创建账号。</p>
                            <img src="images/login.png" alt="登录界面" class="guide-img">
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>上传文件</h3>
                            <p>成功登录后→进入系统主界面→选择"上传文件"</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>进入功能模块</h3>
                            <p>点击对应文件一行的“筛查、审核、编加、校对”进入相关页面</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h3>功能范围选择</h3>
                            <p>点击右上角的“安全审核” “审核”“校对”进入</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h3>进度查看</h3>
                            <p>点击“文档管理”，回到系统主界面，可查看任务进度。</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">6</div>
                        <div class="step-content">
                            <h3>文本保存</h3>
                            <p>任务及修改结束后，可点击左上角“保存”图标对文本保存</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">end</div>
                    </div>
                </div>
            </section>
            
            <section id="features">
                <h2>功能详解</h2>
                <p>版慎通提供全面的内容审校功能，帮助您提升内容质量和准确性。</p>
                
                <div id="feature-1" class="feature-section">
                    <h3>安全筛查</h3>
                    <p>基于大模型语义理解能力，突破传统规则知识库限制，实现多维度风险识别：</p>
                    <ul>
                        <li>敏感信息审查：精准识别价值导向偏差、历史虚无主义等敏感表述</li>
                        <li>违法内容拦截：自动检测涉黄赌毒、暴恐、虚假信息等违规内容</li>
                        <li>价值导向把关：深度分析文本隐含价值观，过滤低俗媚俗、拜金主义等不良倾向</li>
                        <li>社会风险预警：筛查歧视性表述、地域黑、性别对立等潜在舆情风险点</li>
                    </ul>
                </div>
                
                <div id="feature-2" class="feature-section">
                    <h3>内容审核</h3>
                    <p>建立多维质量评估体系，提供全维度审读报告：</p>
                    <ul>
                        <li>敏感信息与价值观审查：核查思想立场、民族宗教、领土主权等敏感表述，确保符合社会主义核心价值观</li>
                        <li>内容质量评估：从原创性、专业性、逻辑性等维度检测文本价值，识别抄袭风险与知识性硬伤</li>
                        <li>语言规范诊断：分析表达准确性、学术规范性、术语统一性，定位模糊表述与常识错误</li>
                        <li>结构完整性审查：评估章节布局合理性，检测关键要素缺失</li>
                        <li>价值定位分析：通过AI语义解析判定作品市场定位，输出读者画像匹配度与出版价值评估</li>
                    </ul>
                </div>
                
                <div id="feature-3" class="feature-section">
                    <h3>编改预处理</h3>
                    <p>提供分层级文本优化方案：</p>
                    <ul>
                        <li>基础优化：智能修正语病、冗余表述、逻辑断裂等基础问题，优化标点符号与数字用法规范</li>
                        <li>专业润色：根据不同文本类型（学术论文/文学作品/商业文案）激活专属AI模型，针对性提升表达效果</li>
                        <li>风格适配：自动调整语言风格（严谨学术体/通俗口语体），智能匹配目标读者阅读习惯</li>
                        <li>结构重组：提供章节优化建议，强化文本架构逻辑性</li>
                    </ul>
                </div>
                
                <div id="feature-4" class="feature-section">
                    <h3>文本校对</h3>
                    <p>构建全流程校对解决方案：</p>
                    <ul>
                        <li>敏感词智能检测：实时匹配最新版《出版管理条例》禁载内容，覆盖专项词库</li>
                        <li>精准错别字校对：融合字形、字音、语境三重校验，识别形近字、音近字等非常规错误</li>
                        <li>标点符号规范：依据GB/T 15834标准自动修正标点误用，智能处理引号嵌套、破折号连用等复杂情况</li>
                        <li>知识性差错排查：对接权威数据库实时校验人名、地名、历史事件、科学数据等关键信息</li>
                        <li>格式标准化处理：自动统一数字用法（汉字/阿拉伯数字）、计量单位等规范要素</li>
                    </ul>
                </div>
            </section>
            
            <section id="faq">
                <h2>常见问题</h2>
                <div class="faq-container">
                    <div class="faq-item">
                        <div class="faq-question">系统支持哪些文件格式？</div>
                        <div class="faq-answer">
                            <p>版慎通支持多种文件格式，包括但不限于：</p>
                            <ul>
                                <li>Microsoft Word文档（.docx）</li>
                            </ul>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">如何提高审校准确率？</div>
                        <div class="faq-answer">
                            <p>要提高审校准确率，您可以：</p>
                            <ul>
                                <li>选择适合您内容类型的审校模式</li>
                                <li>创建自定义规则，适应特定行业术语</li>
                                <li>定期更新系统，获取最新的审校规则</li>
                                <li>提供反馈，帮助系统学习和改进</li>
                            </ul>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">系统是否支持离线使用？</div>
                        <div class="faq-answer">
                            <p>版慎通主要是一个在线服务，但我们也提供企业版本，支持在内部网络环境中部署使用。请联系我们的销售团队了解更多信息。</p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">如何处理误报问题？</div>
                        <div class="faq-answer">
                            <p>如果您发现系统错误地标记了某些内容，您可以：</p>
                            <ul>
                                <li>点击"忽略"按钮，系统将不再标记类似内容(暂未上线)</li>
                                <li>提交反馈，帮助我们改进系统</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
            
            <section id="troubleshooting">
                <h2>故障排除</h2>
                <div class="troubleshooting-container">
                    <div class="trouble-item">
                        <h3>系统加载缓慢</h3>
                        <p>如果您发现系统加载速度变慢，请尝试以下解决方法：</p>
                        <ul>
                            <li>清除浏览器缓存</li>
                            <li>检查网络连接</li>
                            <li>关闭不必要的浏览器标签和应用程序</li>
                            <li>尝试使用不同的浏览器</li>
                        </ul>
                    </div>
                    <div class="trouble-item">
                        <h3>文件上传失败</h3>
                        <p>如果您无法上传文件，请检查：</p>
                        <ul>
                            <li>文件大小是否超过限制（普通账号支持450000字内容上传）</li>
                            <li>文件格式是否受支持（是否为docx格式文件）</li>
                            <li>网络连接是否稳定</li>
                        </ul>
                    </div>
                    <div class="trouble-item">
                        <h3>审校结果不准确</h3>
                        <p>如果您发现审校结果不准确，可能是因为：</p>
                        <ul>
                            <li>内容包含大量专业术语</li>
                            <li>文本格式复杂</li>
                            <li>系统规则需要更新</li>
                        </ul>
                        <p>建议您提交反馈，并考虑创建自定义规则来提高准确性。</p>
                    </div>
                </div>
            </section>
            
            <section id="updates">
                <h2>更新日志</h2>
                <div class="updates-container">
                    <div class="update-item">
                        <div class="update-version">版本 1.7.6 (2025-7-15)</div>
                        <div class="update-content">
                            <ul>
                                <li>新增智能AI解析模型，支持多种格式文件上传</li>
                                <li>新增筛查、审核和校对模块的报告下载，可与原文对照查看</li>
                                <li>修复了单元划分不准的问题</li>
                                <li>优化了敏感词知识库，筛查拦截更准确</li>
                                <li>改进了用户界面，呈现更清晰美观</li>
                            </ul>
                        </div>
                    </div>
                    <div class="update-item">
                        <div class="update-version">版本 1.5.1 (2024-5-15)</div>
                        <div class="update-content">
                            <ul>
                                <li>优化了智能审校算法，提高了识别准确率</li>
                                <li>新增了10种内容安全检测规则</li>
                                <li>修复了批量处理时的内存占用问题</li>
                                <li>改进了用户界面，提升了操作体验</li>
                            </ul>
                        </div>
                    </div>
                    <div class="update-item">
                        <div class="update-version">版本1.0.5 (2023-10-20)</div>
                        <div class="update-content">
                            <ul>
                                <li>增强了知识差错检测能力</li>
                                <li>优化了系统性能，提高了处理速度</li>
                            </ul>
                        </div>
                    </div>
                    <div class="update-item">
                        <div class="update-version">版本 1.0.2 (2023-08-05)</div>
                        <div class="update-content">
                            <ul>
                                <li>修复了多个已知问题</li>
                                <li>优化了审核功能</li>
                            </ul>
                        </div>
                    </div
                </div>
            </section>
                <!-- 添加版权信息区域 -->
                <div class="footer-bottom">
                    <p style="letter-spacing: 1px;padding: 0 0 20px 0;color: #666;"><img src="images/batb.png" width="16" height="16" style="vertical-align:text-bottom; margin-right: 5px;">
                      <a href="https://beian.mps.gov.cn/#/query/webSearch?code=36012202000553" rel="noreferrer" target="_blank" style="letter-spacing: 1px; font-size: 12px; color: inherit; text-decoration: none;">赣公网安备36012202000553号</a>
                      <a href="https://beian.miit.gov.cn/#/Integrated/index" rel="noreferrer" target="_blank" style="letter-spacing: 1px; font-size: 12px; color: inherit; text-decoration: none;">赣ICP备2025056979号-2</a>    
                      江西朗知中文数智科技有限公司©2011-2025</p>
                </div>
    </div>
 </body>
</html>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // FAQ展开/收起
    const faqQuestions = document.querySelectorAll('.faq-question');
    faqQuestions.forEach(question => {
        question.addEventListener('click', function() {
            const faqItem = this.parentNode;
            faqItem.classList.toggle('active');
        });
    });
    
    // 节流函数
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    const menuItems = document.querySelectorAll('.menu > li > a');
    const contentArea = document.querySelector('.content-area');
    const sections = document.querySelectorAll('.content-area section');
    const sidebar = document.querySelector('.sidebar');
    
    // 添加一个变量来跟踪是否是用户手动点击
    let isManualClick = false;
    let manualClickTimeout;

    // 目录项点击
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            isManualClick = true;
            clearTimeout(manualClickTimeout);
            
            document.querySelectorAll('.menu > li').forEach(i => {
                i.classList.remove('active');
            });
            
            this.parentNode.classList.add('active');
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement && contentArea) {
                const targetPosition = targetElement.offsetTop;
                
                contentArea.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                manualClickTimeout = setTimeout(() => {
                    isManualClick = false;
                }, 1000); // 增加延时以确保滚动动画完成
            }
        });
    });
    
    // 滚动事件监听
    if (contentArea) {
        contentArea.addEventListener('scroll', throttle(function() {
            if (isManualClick) return;
            
            const scrollPosition = contentArea.scrollTop;
            let newActiveSectionId = null; // 用于存储新计算出的活动 section ID
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;
                
                // 调整判断条件，使顶部对齐时就能选中
                if (scrollPosition >= sectionTop - 50 && // 减小偏移量
                    scrollPosition < sectionTop + sectionHeight - 50) {
                    newActiveSectionId = section.id; // 记录匹配到的 section ID
                }
            });

            // 如果滚动到最底部，确保最后一个section被选中
            if (contentArea.scrollTop + contentArea.clientHeight >= contentArea.scrollHeight - 10) {
                 const lastSection = sections[sections.length - 1];
                 if(lastSection) {
                    newActiveSectionId = lastSection.id;
                 }
            }
            
            // 只有当计算出了新的活动 section ID 时才更新菜单状态
            if (newActiveSectionId) {
                const currentActiveItem = document.querySelector('.menu > li.active > a');
                const newActiveMenuItem = document.querySelector(`.menu > li > a[href="#${newActiveSectionId}"]`);

                // 如果新计算出的活动项和当前活动项不同，或者当前没有活动项
                if (!currentActiveItem || (newActiveMenuItem && currentActiveItem.getAttribute('href') !== `#${newActiveSectionId}`)) {
                    // 移除所有 active 类
                    document.querySelectorAll('.menu > li').forEach(item => {
                        item.classList.remove('active');
                    });

                    // 添加 active 类到新的菜单项
                    if (newActiveMenuItem && sidebar) {
                        newActiveMenuItem.parentNode.classList.add('active');
                        
                        // 确保选中的菜单项在侧边栏视图中 (这部分逻辑保持不变)
                        const menuItemTop = newActiveMenuItem.parentNode.offsetTop;
                        const menuItemHeight = newActiveMenuItem.parentNode.offsetHeight;
                        const sidebarScrollTop = sidebar.scrollTop;
                        const sidebarHeight = sidebar.offsetHeight;
                        const scrollToPosition = menuItemTop - (sidebarHeight / 2) + (menuItemHeight / 2);
                        if (menuItemTop < sidebarScrollTop || menuItemTop + menuItemHeight > sidebarScrollTop + sidebarHeight) {
                            sidebar.scrollTo({
                                top: scrollToPosition,
                                behavior: 'smooth'
                            });
                        }
                    }
                }
            } 
            // 如果 newActiveSectionId 为 null (即没有匹配到任何 section)，则不执行任何操作，保持当前选中状态，避免跳动
        }, 100)); // 降低节流频率
    }

    // 页面加载时，根据URL哈希值设置初始选中项
    const initialHash = window.location.hash;
    if (initialHash) {
        const initialActiveItem = document.querySelector(`.menu > li > a[href="${initialHash}"]`);
        if (initialActiveItem) {
             document.querySelectorAll('.menu > li').forEach(i => {
                i.classList.remove('active');
            });
            initialActiveItem.parentNode.classList.add('active');
            // 触发一次滚动到对应位置（如果需要）
            const targetElement = document.getElementById(initialHash.substring(1));
             if (targetElement && contentArea) {
                const targetPosition = targetElement.offsetTop;
                contentArea.scrollTo({
                    top: targetPosition,
                    behavior: 'auto' // 初始加载不需要平滑
                });
            }
        }
    } else {
        // 如果没有哈希值，默认选中第一个
        const firstMenuItem = document.querySelector('.menu > li:first-child');
        if (firstMenuItem) {
            firstMenuItem.classList.add('active');
        }
    }

    // 处理浏览器前进/后退按钮
    window.addEventListener('popstate', function() {
        const hash = window.location.hash;
        if (hash) {
            const activeMenuItem = document.querySelector(`.menu > li > a[href="${hash}"]`);
            if (activeMenuItem) {
                document.querySelectorAll('.menu > li').forEach(i => {
                    i.classList.remove('active');
                });
                activeMenuItem.parentNode.classList.add('active');
                const targetElement = document.getElementById(hash.substring(1));
                if (targetElement && contentArea) {
                    const targetPosition = targetElement.offsetTop;
                    contentArea.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            }
        } else {
            // 如果没有哈希值，默认选中第一个
            document.querySelectorAll('.menu > li').forEach(i => {
                i.classList.remove('active');
            });
            const firstMenuItem = document.querySelector('.menu > li:first-child');
            if (firstMenuItem) {
                firstMenuItem.classList.add('active');
            }
             if (contentArea) {
                contentArea.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }
    });

});
</script>