/* Build section */
#build {

    padding: 88px 0; /* 移除顶部和底部的内边距 */
    text-align: center;
    min-width: 100%; /* 改为100%，而不是固定宽度 */
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 0; /* 将 -240px 改为 0 */
}

#build h2 {
    font-size: 32px;
    margin-bottom: 32px;
}

.build-content {
    width: 100%; /* 改为100% */
    max-width: 1200px; /* 添加最大宽度 */
    margin: 0 auto;
    padding: 20px 15px; /* 添加内边距 */
    box-sizing: border-box;
}

.build-features {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin-bottom: 32px;
}

.build-feature-item {
    flex: 1;
    padding: 16px 0;
    text-align: center;
    cursor: pointer;
    transition: all 0s ease;
    border-bottom: 1px solid #ebebeb;
}

.build-feature-item h3 {
    font-size: 21px;
    margin: 0;
    border-style: none;
    font-weight: normal;
}


/* 添加点击后的高亮效果 */
.build-feature-item.active {
    color: #007bff; /* 使用蓝色文字而不是白色 */
    border-bottom: 2px solid #007bff; /* 添加蓝色下 */
    background-color: transparent; /* 移除背景颜色 */
}

/* 修改 build-feature-description 的样式 */
#build-feature-description {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
}

/* 添加新的样式用于左右两部分 */
.feature-comparison {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
}

.feature-side {
    width: 430px; /* 减小宽度，为 VS 留出更多空间 */
    text-align: center;
}

.feature-side h3 {
    font-size: 30px;
    margin-bottom: 16px;
}

.feature-side h3::before {
    margin-right: 10px;
    font-size: 24px;
}

.feature-side.traditional h3::before {
    content: "\2716"; /* Unicode for X mark */
    color: #dc3545; /* Red color */
}

.feature-side.intelligent h3::before {
    content: "\2714"; /* Unicode for check mark */
    color: #28a745; /* Green color */
}

.feature-side p {
    text-align: left;
    font-size: 16px;
    line-height: 1.6;
}

.vs-divider {
    font-size: 40px;
    font-weight: bold;
    color: #007bff;
    align-self: center;
    margin: 0 40px; /* 增加左右边距 */
}

/* 调整 feature-description-content 的样式 */
.feature-description-content {
    width: 100%;
}

/* 移除 feature-image，因为我们不再需要它 */
.feature-image {
    display: none;
}

/* 其他样式保持不变 */
.feature-description-content h3 {
    font-size: 21px;
    margin-bottom: 16px;
}

.feature-description-content p {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 16px;
}

.feature-description-content h4 {
    font-size: 32px;
    margin-bottom: 8px;
    color: #007bff;
}

.feature-description-content ul {
    list-style-type: none;
    padding-left: 0;
    display: flex;
    flex-wrap: wrap;
}

.feature-description-content li {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 16px; /* 增加底部边距 */
    position: relative;
    padding-left: 0; /* 移除左侧填充 */
    flex-basis: 100%; /* 使每个列表项占据整行 */
}

/* 在文件末尾添加以下样式 */
.feature-description-content p::before {
    content: "\2716"; /* Unicode for X mark */
    color: #dc3545; /* Red color */
    margin-right: 10px;
    font-weight: bold;
}
.highlight {
    font-size: 18px; /* 增大字体大小 */
    font-weight: bold; /* 加粗字体 */
    color: #333; /* 设置字体颜色 */
}

/* 添加新的样式 */
.feature-comparison h3 {
    font-size: 30px;
    margin-bottom: 16px;
}

/* 其他样式保持不变 */

