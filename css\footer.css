/* Footer styles */
footer {
    background-color: #1c1c28;
    color: #fff;
    padding: 0 0 20px 0;
    min-width: 100%; /* 改为100%，而不是固定宽度 */
    transform: translateY(0); /* 将 -240px 改为 0 */
}

.footer-content {
    width: 100%; /* 改为100% */
    max-width: 1300px; /* 添加最大宽度 */
    margin: 0 auto;
    padding: 0 15px; /* 添加内边距 */
    box-sizing: border-box;
}

/* 修改 footer-main 布局 */
.footer-main {
    display: flex;
    justify-content: space-between;
    padding: 30px 0;
}

/* 修改 footer-right 样式 */
.footer-right {
    width: 30%;
    text-align: left;
    padding-right: 20px;
    border-right: 1px solid #333;
    order: -1; /* 将 footer-right 移到最左边 */
}

/* 修改 footer-columns 样式 */
.footer-columns {
    display: flex;
    width: 100%;
    justify-content: space-between;
    padding-top: 30px;
    padding-left: 40px;
}

.footer-column {
    width: 33%;
}

.footer-column-small {
    width: 15%;
}

.footer-column-middle {
    width: 35%;
}

.footer-column-large {
    width: 50%;
}

.footer-column h4 {
    font-size: 16px;
    color: #fff;
    margin-bottom: 20px;
}

.footer-column ul {
    list-style: none;
    padding: 0;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: #8a8a9a;
    text-decoration: none;
    font-size: 14px;
}

/* 添加 footer-right 样式 */
.footer-right h4 {
    font-size: 16px;
    color: #fff;
    margin-bottom: 10px;
}

.footer-right > p {
    color: #8a8a9a;
    font-size: 12px;
    margin-bottom: 15px;
    line-height: 1.5;
}

.footer-qr-codes {
    display: flex;
    justify-content: flex-start; /* 改为左对齐 */
    margin-bottom: 15px;
}

.footer-qr {
    text-align: center;
    margin-right: 20px; /* 添加右边距，使两个二维码之间有一定间隔 */
}

.footer-qr:last-child {
    margin-right: 0; /* 最后一个二维码不需要右边距 */
}

.footer-qr img {
    width: 80px;
    height: 80px;
    margin-bottom: 5px;
}

.footer-qr p {
    color: #8a8a9a;
    font-size: 12px;
}

.footer-right .contact-number {
    color: #fff;
    font-size: 14px;
    margin-bottom: 15px;
}

.footer-right .contact-btn {
    display: inline-block;
    background-color: #1890ff;
    color: #fff;
    padding: 8px 20px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
}

/* 保持 footer-bottom 样式不变 */
.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 20px;
    text-align: center;
}

.footer-bottom p {
    color: #fff;
    font-size: 12px;
    line-height: 1.4;
}

/* 添加页脚顶部样式 */
.footer-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #333;
}

.footer-top-item {
    display: flex;
    align-items: center;
}


.footer-top-item span {
    color: #fff;
    font-size: 18px;
}

.footer-top-item > .iconfont{
    color: #fff;
    font-size: 32px;
    display: flex;
    align-items: center;
    gap: 10px;
}
/* 修改 footer-social 的样式 */
.footer-social {
    margin-top: 20px;
}

.footer-social a {
    display: inline-block;
    width: 32px;
    height: 32px;
    background-color: #3a3a4a;
    border-radius: 50%;
    margin-right: 10px;
    text-align: center;
    line-height: 32px;
}

.footer-social a:last-child {
    margin-right: 0;
}

.footer-social img {
    width: 20px;
    height: 20px;
    vertical-align: middle;
}
