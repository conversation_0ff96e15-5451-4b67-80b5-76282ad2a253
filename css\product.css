@charset "UTF-8"; 
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}
body {
  margin: 0;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden],
template {
  display: none;
}
a {
  background-color: transparent;
}
a:active,
a:hover {
  outline: 0;
}
abbr[title] {
  border-bottom: 1px dotted;
}
b,
strong {
  font-weight: 700;
}
dfn {
  font-style: italic;
}
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
mark {
  background: #ff0;
  color: #000;
}
small {
  font-size: 80%;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
img {
  border: 0;
}
svg:not(:root) {
  overflow: hidden;
}
figure {
  margin: 1em 40px;
}
hr {
  box-sizing: content-box;
  height: 0;
}
pre {
  overflow: auto;
}
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}
button {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
button[disabled],
html input[disabled] {
  cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
input {
  line-height: normal;
}
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: textfield;
  box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
fieldset {
  border: 1px solid silver;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
legend {
  border: 0;
  padding: 0;
}
textarea {
  overflow: auto;
}
optgroup {
  font-weight: 700;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
} /*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {
  *,
  :after,
  :before {
    background: 0 0 !important;
    color: #000 !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: "";
  }
  blockquote,
  pre {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  img,
  tr {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  h2,
  h3,
  p {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  select {
    background: #fff !important;
  }
  .navbar {
    display: none;
  }
  .btn > .caret,
  .dropup > .btn > .caret {
    border-top-color: #000 !important;
  }
  .label {
    border: 1px solid #000;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td,
  .table th {
    background-color: #fff !important;
  }
  .table-bordered td,
  .table-bordered th {
    border: 1px solid #ddd !important;
  }
}
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: "Glyphicons Halflings";
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.glyphicon-asterisk:before {
  content: "\2a";
}
.glyphicon-plus:before {
  content: "\2b";
}
.glyphicon-eur:before,
.glyphicon-euro:before {
  content: "\20ac";
}
.glyphicon-minus:before {
  content: "\2212";
}
.glyphicon-cloud:before {
  content: "\2601";
}
.glyphicon-envelope:before {
  content: "\2709";
}
.glyphicon-pencil:before {
  content: "\270f";
}
.glyphicon-glass:before {
  content: "\e001";
}
.glyphicon-music:before {
  content: "\e002";
}
.glyphicon-search:before {
  content: "\e003";
}
.glyphicon-heart:before {
  content: "\e005";
}
.glyphicon-star:before {
  content: "\e006";
}
.glyphicon-star-empty:before {
  content: "\e007";
}
.glyphicon-user:before {
  content: "\e008";
}
.glyphicon-film:before {
  content: "\e009";
}
.glyphicon-th-large:before {
  content: "\e010";
}
.glyphicon-th:before {
  content: "\e011";
}
.glyphicon-th-list:before {
  content: "\e012";
}
.glyphicon-ok:before {
  content: "\e013";
}
.glyphicon-remove:before {
  content: "\e014";
}
.glyphicon-zoom-in:before {
  content: "\e015";
}
.glyphicon-zoom-out:before {
  content: "\e016";
}
.glyphicon-off:before {
  content: "\e017";
}
.glyphicon-signal:before {
  content: "\e018";
}
.glyphicon-cog:before {
  content: "\e019";
}
.glyphicon-trash:before {
  content: "\e020";
}
.glyphicon-home:before {
  content: "\e021";
}
.glyphicon-file:before {
  content: "\e022";
}
.glyphicon-time:before {
  content: "\e023";
}
.glyphicon-road:before {
  content: "\e024";
}
.glyphicon-download-alt:before {
  content: "\e025";
}
.glyphicon-download:before {
  content: "\e026";
}
.glyphicon-upload:before {
  content: "\e027";
}
.glyphicon-inbox:before {
  content: "\e028";
}
.glyphicon-play-circle:before {
  content: "\e029";
}
.glyphicon-repeat:before {
  content: "\e030";
}
.glyphicon-refresh:before {
  content: "\e031";
}
.glyphicon-list-alt:before {
  content: "\e032";
}
.glyphicon-lock:before {
  content: "\e033";
}
.glyphicon-flag:before {
  content: "\e034";
}
.glyphicon-headphones:before {
  content: "\e035";
}
.glyphicon-volume-off:before {
  content: "\e036";
}
.glyphicon-volume-down:before {
  content: "\e037";
}
.glyphicon-volume-up:before {
  content: "\e038";
}
.glyphicon-qrcode:before {
  content: "\e039";
}
.glyphicon-barcode:before {
  content: "\e040";
}
.glyphicon-tag:before {
  content: "\e041";
}
.glyphicon-tags:before {
  content: "\e042";
}
.glyphicon-book:before {
  content: "\e043";
}
.glyphicon-bookmark:before {
  content: "\e044";
}
.glyphicon-print:before {
  content: "\e045";
}
.glyphicon-camera:before {
  content: "\e046";
}
.glyphicon-font:before {
  content: "\e047";
}
.glyphicon-bold:before {
  content: "\e048";
}
.glyphicon-italic:before {
  content: "\e049";
}
.glyphicon-text-height:before {
  content: "\e050";
}
.glyphicon-text-width:before {
  content: "\e051";
}
.glyphicon-align-left:before {
  content: "\e052";
}
.glyphicon-align-center:before {
  content: "\e053";
}
.glyphicon-align-right:before {
  content: "\e054";
}
.glyphicon-align-justify:before {
  content: "\e055";
}
.glyphicon-list:before {
  content: "\e056";
}
.glyphicon-indent-left:before {
  content: "\e057";
}
.glyphicon-indent-right:before {
  content: "\e058";
}
.glyphicon-facetime-video:before {
  content: "\e059";
}
.glyphicon-picture:before {
  content: "\e060";
}
.glyphicon-map-marker:before {
  content: "\e062";
}
.glyphicon-adjust:before {
  content: "\e063";
}
.glyphicon-tint:before {
  content: "\e064";
}
.glyphicon-edit:before {
  content: "\e065";
}
.glyphicon-share:before {
  content: "\e066";
}
.glyphicon-check:before {
  content: "\e067";
}
.glyphicon-move:before {
  content: "\e068";
}
.glyphicon-step-backward:before {
  content: "\e069";
}
.glyphicon-fast-backward:before {
  content: "\e070";
}
.glyphicon-backward:before {
  content: "\e071";
}
.glyphicon-play:before {
  content: "\e072";
}
.glyphicon-pause:before {
  content: "\e073";
}
.glyphicon-stop:before {
  content: "\e074";
}
.glyphicon-forward:before {
  content: "\e075";
}
.glyphicon-fast-forward:before {
  content: "\e076";
}
.glyphicon-step-forward:before {
  content: "\e077";
}
.glyphicon-eject:before {
  content: "\e078";
}
.glyphicon-chevron-left:before {
  content: "\e079";
}
.glyphicon-chevron-right:before {
  content: "\e080";
}
.glyphicon-plus-sign:before {
  content: "\e081";
}
.glyphicon-minus-sign:before {
  content: "\e082";
}
.glyphicon-remove-sign:before {
  content: "\e083";
}
.glyphicon-ok-sign:before {
  content: "\e084";
}
.glyphicon-question-sign:before {
  content: "\e085";
}
.glyphicon-info-sign:before {
  content: "\e086";
}
.glyphicon-screenshot:before {
  content: "\e087";
}
.glyphicon-remove-circle:before {
  content: "\e088";
}
.glyphicon-ok-circle:before {
  content: "\e089";
}
.glyphicon-ban-circle:before {
  content: "\e090";
}
.glyphicon-arrow-left:before {
  content: "\e091";
}
.glyphicon-arrow-right:before {
  content: "\e092";
}
.glyphicon-arrow-up:before {
  content: "\e093";
}
.glyphicon-arrow-down:before {
  content: "\e094";
}
.glyphicon-share-alt:before {
  content: "\e095";
}
.glyphicon-resize-full:before {
  content: "\e096";
}
.glyphicon-resize-small:before {
  content: "\e097";
}
.glyphicon-exclamation-sign:before {
  content: "\e101";
}
.glyphicon-gift:before {
  content: "\e102";
}
.glyphicon-leaf:before {
  content: "\e103";
}
.glyphicon-fire:before {
  content: "\e104";
}
.glyphicon-eye-open:before {
  content: "\e105";
}
.glyphicon-eye-close:before {
  content: "\e106";
}
.glyphicon-warning-sign:before {
  content: "\e107";
}
.glyphicon-plane:before {
  content: "\e108";
}
.glyphicon-calendar:before {
  content: "\e109";
}
.glyphicon-random:before {
  content: "\e110";
}
.glyphicon-comment:before {
  content: "\e111";
}
.glyphicon-magnet:before {
  content: "\e112";
}
.glyphicon-chevron-up:before {
  content: "\e113";
}
.glyphicon-chevron-down:before {
  content: "\e114";
}
.glyphicon-retweet:before {
  content: "\e115";
}
.glyphicon-shopping-cart:before {
  content: "\e116";
}
.glyphicon-folder-close:before {
  content: "\e117";
}
.glyphicon-folder-open:before {
  content: "\e118";
}
.glyphicon-resize-vertical:before {
  content: "\e119";
}
.glyphicon-resize-horizontal:before {
  content: "\e120";
}
.glyphicon-hdd:before {
  content: "\e121";
}
.glyphicon-bullhorn:before {
  content: "\e122";
}
.glyphicon-bell:before {
  content: "\e123";
}
.glyphicon-certificate:before {
  content: "\e124";
}
.glyphicon-thumbs-up:before {
  content: "\e125";
}
.glyphicon-thumbs-down:before {
  content: "\e126";
}
.glyphicon-hand-right:before {
  content: "\e127";
}
.glyphicon-hand-left:before {
  content: "\e128";
}
.glyphicon-hand-up:before {
  content: "\e129";
}
.glyphicon-hand-down:before {
  content: "\e130";
}
.glyphicon-circle-arrow-right:before {
  content: "\e131";
}
.glyphicon-circle-arrow-left:before {
  content: "\e132";
}
.glyphicon-circle-arrow-up:before {
  content: "\e133";
}
.glyphicon-circle-arrow-down:before {
  content: "\e134";
}
.glyphicon-globe:before {
  content: "\e135";
}
.glyphicon-wrench:before {
  content: "\e136";
}
.glyphicon-tasks:before {
  content: "\e137";
}
.glyphicon-filter:before {
  content: "\e138";
}
.glyphicon-briefcase:before {
  content: "\e139";
}
.glyphicon-fullscreen:before {
  content: "\e140";
}
.glyphicon-dashboard:before {
  content: "\e141";
}
.glyphicon-paperclip:before {
  content: "\e142";
}
.glyphicon-heart-empty:before {
  content: "\e143";
}
.glyphicon-link:before {
  content: "\e144";
}
.glyphicon-phone:before {
  content: "\e145";
}
.glyphicon-pushpin:before {
  content: "\e146";
}
.glyphicon-usd:before {
  content: "\e148";
}
.glyphicon-gbp:before {
  content: "\e149";
}
.glyphicon-sort:before {
  content: "\e150";
}
.glyphicon-sort-by-alphabet:before {
  content: "\e151";
}
.glyphicon-sort-by-alphabet-alt:before {
  content: "\e152";
}
.glyphicon-sort-by-order:before {
  content: "\e153";
}
.glyphicon-sort-by-order-alt:before {
  content: "\e154";
}
.glyphicon-sort-by-attributes:before {
  content: "\e155";
}
.glyphicon-sort-by-attributes-alt:before {
  content: "\e156";
}
.glyphicon-unchecked:before {
  content: "\e157";
}
.glyphicon-expand:before {
  content: "\e158";
}
.glyphicon-collapse-down:before {
  content: "\e159";
}
.glyphicon-collapse-up:before {
  content: "\e160";
}
.glyphicon-log-in:before {
  content: "\e161";
}
.glyphicon-flash:before {
  content: "\e162";
}
.glyphicon-log-out:before {
  content: "\e163";
}
.glyphicon-new-window:before {
  content: "\e164";
}
.glyphicon-record:before {
  content: "\e165";
}
.glyphicon-save:before {
  content: "\e166";
}
.glyphicon-open:before {
  content: "\e167";
}
.glyphicon-saved:before {
  content: "\e168";
}
.glyphicon-import:before {
  content: "\e169";
}
.glyphicon-export:before {
  content: "\e170";
}
.glyphicon-send:before {
  content: "\e171";
}
.glyphicon-floppy-disk:before {
  content: "\e172";
}
.glyphicon-floppy-saved:before {
  content: "\e173";
}
.glyphicon-floppy-remove:before {
  content: "\e174";
}
.glyphicon-floppy-save:before {
  content: "\e175";
}
.glyphicon-floppy-open:before {
  content: "\e176";
}
.glyphicon-credit-card:before {
  content: "\e177";
}
.glyphicon-transfer:before {
  content: "\e178";
}
.glyphicon-cutlery:before {
  content: "\e179";
}
.glyphicon-header:before {
  content: "\e180";
}
.glyphicon-compressed:before {
  content: "\e181";
}
.glyphicon-earphone:before {
  content: "\e182";
}
.glyphicon-phone-alt:before {
  content: "\e183";
}
.glyphicon-tower:before {
  content: "\e184";
}
.glyphicon-stats:before {
  content: "\e185";
}
.glyphicon-sd-video:before {
  content: "\e186";
}
.glyphicon-hd-video:before {
  content: "\e187";
}
.glyphicon-subtitles:before {
  content: "\e188";
}
.glyphicon-sound-stereo:before {
  content: "\e189";
}
.glyphicon-sound-dolby:before {
  content: "\e190";
}
.glyphicon-sound-5-1:before {
  content: "\e191";
}
.glyphicon-sound-6-1:before {
  content: "\e192";
}
.glyphicon-sound-7-1:before {
  content: "\e193";
}
.glyphicon-copyright-mark:before {
  content: "\e194";
}
.glyphicon-registration-mark:before {
  content: "\e195";
}
.glyphicon-cloud-download:before {
  content: "\e197";
}
.glyphicon-cloud-upload:before {
  content: "\e198";
}
.glyphicon-tree-conifer:before {
  content: "\e199";
}
.glyphicon-tree-deciduous:before {
  content: "\e200";
}
.glyphicon-cd:before {
  content: "\e201";
}
.glyphicon-save-file:before {
  content: "\e202";
}
.glyphicon-open-file:before {
  content: "\e203";
}
.glyphicon-level-up:before {
  content: "\e204";
}
.glyphicon-copy:before {
  content: "\e205";
}
.glyphicon-paste:before {
  content: "\e206";
}
.glyphicon-alert:before {
  content: "\e209";
}
.glyphicon-equalizer:before {
  content: "\e210";
}
.glyphicon-king:before {
  content: "\e211";
}
.glyphicon-queen:before {
  content: "\e212";
}
.glyphicon-pawn:before {
  content: "\e213";
}
.glyphicon-bishop:before {
  content: "\e214";
}
.glyphicon-knight:before {
  content: "\e215";
}
.glyphicon-baby-formula:before {
  content: "\e216";
}
.glyphicon-tent:before {
  content: "\26fa";
}
.glyphicon-blackboard:before {
  content: "\e218";
}
.glyphicon-bed:before {
  content: "\e219";
}
.glyphicon-apple:before {
  content: "\f8ff";
}
.glyphicon-erase:before {
  content: "\e221";
}
.glyphicon-hourglass:before {
  content: "\231b";
}
.glyphicon-lamp:before {
  content: "\e223";
}
.glyphicon-duplicate:before {
  content: "\e224";
}
.glyphicon-piggy-bank:before {
  content: "\e225";
}
.glyphicon-scissors:before {
  content: "\e226";
}
.glyphicon-bitcoin:before {
  content: "\e227";
}
.glyphicon-btc:before {
  content: "\e227";
}
.glyphicon-xbt:before {
  content: "\e227";
}
.glyphicon-yen:before {
  content: "\00a5";
}
.glyphicon-jpy:before {
  content: "\00a5";
}
.glyphicon-ruble:before {
  content: "\20bd";
}
.glyphicon-rub:before {
  content: "\20bd";
}
.glyphicon-scale:before {
  content: "\e230";
}
.glyphicon-ice-lolly:before {
  content: "\e231";
}
.glyphicon-ice-lolly-tasted:before {
  content: "\e232";
}
.glyphicon-education:before {
  content: "\e233";
}
.glyphicon-option-horizontal:before {
  content: "\e234";
}
.glyphicon-option-vertical:before {
  content: "\e235";
}
.glyphicon-menu-hamburger:before {
  content: "\e236";
}
.glyphicon-modal-window:before {
  content: "\e237";
}
.glyphicon-oil:before {
  content: "\e238";
}
.glyphicon-grain:before {
  content: "\e239";
}
.glyphicon-sunglasses:before {
  content: "\e240";
}
.glyphicon-text-size:before {
  content: "\e241";
}
.glyphicon-text-color:before {
  content: "\e242";
}
.glyphicon-text-background:before {
  content: "\e243";
}
.glyphicon-object-align-top:before {
  content: "\e244";
}
.glyphicon-object-align-bottom:before {
  content: "\e245";
}
.glyphicon-object-align-horizontal:before {
  content: "\e246";
}
.glyphicon-object-align-left:before {
  content: "\e247";
}
.glyphicon-object-align-vertical:before {
  content: "\e248";
}
.glyphicon-object-align-right:before {
  content: "\e249";
}
.glyphicon-triangle-right:before {
  content: "\e250";
}
.glyphicon-triangle-left:before {
  content: "\e251";
}
.glyphicon-triangle-bottom:before {
  content: "\e252";
}
.glyphicon-triangle-top:before {
  content: "\e253";
}
.glyphicon-console:before {
  content: "\e254";
}
.glyphicon-superscript:before {
  content: "\e255";
}
.glyphicon-subscript:before {
  content: "\e256";
}
.glyphicon-menu-left:before {
  content: "\e257";
}
.glyphicon-menu-right:before {
  content: "\e258";
}
.glyphicon-menu-down:before {
  content: "\e259";
}
.glyphicon-menu-up:before {
  content: "\e260";
}
* {
  box-sizing: border-box;
}
:after,
:before {
  box-sizing: border-box;
}
html {
  font-size: 10px;
  -webkit-tap-highlight-color: transparent;
}
body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.428571429;
  color: #333;
  background-color: #fff;
}
button,
input,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
a {
  color: #337ab7;
  text-decoration: none;
}
a:focus,
a:hover {
  color: #23527c;
  text-decoration: underline;
}
a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
figure {
  margin: 0;
}
img {
  vertical-align: middle;
}
.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}
.img-rounded {
  border-radius: 6px;
}
.img-thumbnail {
  padding: 4px;
  line-height: 1.428571429;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto;
}
.img-circle {
  border-radius: 50%;
}
hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eee;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}
[role="button"] {
  cursor: pointer;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}
.h1 .small,
.h1 small,
.h2 .small,
.h2 small,
.h3 .small,
.h3 small,
.h4 .small,
.h4 small,
.h5 .small,
.h5 small,
.h6 .small,
.h6 small,
h1 .small,
h1 small,
h2 .small,
h2 small,
h3 .small,
h3 small,
h4 .small,
h4 small,
h5 .small,
h5 small,
h6 .small,
h6 small {
  font-weight: 400;
  line-height: 1;
  color: #777;
}
.h1,
.h2,
.h3,
h1,
h2,
h3 {
  margin-top: 20px;
  margin-bottom: 10px;
}
.h1 .small,
.h1 small,
.h2 .small,
.h2 small,
.h3 .small,
.h3 small,
h1 .small,
h1 small,
h2 .small,
h2 small,
h3 .small,
h3 small {
  font-size: 65%;
}
.h4,
.h5,
.h6,
h4,
h5,
h6 {
  margin-top: 10px;
  margin-bottom: 10px;
}
.h4 .small,
.h4 small,
.h5 .small,
.h5 small,
.h6 .small,
.h6 small,
h4 .small,
h4 small,
h5 .small,
h5 small,
h6 .small,
h6 small {
  font-size: 75%;
}
.h1,
h1 {
  font-size: 36px;
}
.h2,
h2 {
  font-size: 30px;
}
.h3,
h3 {
  font-size: 24px;
}
.h4,
h4 {
  font-size: 18px;
}
.h5,
h5 {
  font-size: 14px;
}
.h6,
h6 {
  font-size: 12px;
}
p {
  margin: 0 0 10px;
}
.lead {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.4;
}
@media (min-width: 768px) {
  .lead {
    font-size: 21px;
  }
}
.small,
small {
  font-size: 85%;
}
.mark,
mark {
  background-color: #fcf8e3;
  padding: 0.2em;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.text-justify {
  text-align: justify;
}
.text-nowrap {
  white-space: nowrap;
}
.text-lowercase {
  text-transform: lowercase;
}
.initialism,
.text-uppercase {
  text-transform: uppercase;
}
.text-capitalize {
  text-transform: capitalize;
}
.text-muted {
  color: #777;
}
.text-primary {
  color: #337ab7;
}
a.text-primary:hover {
  color: #286090;
}
.text-success {
  color: #3c763d;
}
a.text-success:hover {
  color: #2b542c;
}
.text-info {
  color: #31708f;
}
a.text-info:hover {
  color: #245269;
}
.text-warning {
  color: #8a6d3b;
}
a.text-warning:hover {
  color: #66512c;
}
.text-danger {
  color: #a94442;
}
a.text-danger:hover {
  color: #843534;
}
.bg-primary {
  color: #fff;
}
.bg-primary {
  background-color: #337ab7;
}
a.bg-primary:hover {
  background-color: #286090;
}
.bg-success {
  background-color: #dff0d8;
}
a.bg-success:hover {
  background-color: #c1e2b3;
}
.bg-info {
  background-color: #d9edf7;
}
a.bg-info:hover {
  background-color: #afd9ee;
}
.bg-warning {
  background-color: #fcf8e3;
}
a.bg-warning:hover {
  background-color: #f7ecb5;
}
.bg-danger {
  background-color: #f2dede;
}
a.bg-danger:hover {
  background-color: #e4b9b9;
}
.page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eee;
}
ol,
ul {
  margin-top: 0;
  margin-bottom: 10px;
}
ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0;
}
.list-unstyled {
  padding-left: 0;
  list-style: none;
}
.list-inline {
  padding-left: 0;
  list-style: none;
  margin-left: -5px;
}
.list-inline > li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px;
}
dl {
  margin-top: 0;
  margin-bottom: 20px;
}
dd,
dt {
  line-height: 1.428571429;
}
dt {
  font-weight: 700;
}
dd {
  margin-left: 0;
}
.dl-horizontal dd:after,
.dl-horizontal dd:before {
  content: " ";
  display: table;
}
.dl-horizontal dd:after {
  clear: both;
}
@media (min-width: 768px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .dl-horizontal dd {
    margin-left: 180px;
  }
}
abbr[data-original-title],
abbr[title] {
  cursor: help;
  border-bottom: 1px dotted #777;
}
.initialism {
  font-size: 90%;
}
blockquote {
  padding: 10px 20px;
  margin: 0 0 20px;
  font-size: 17.5px;
  border-left: 5px solid #eee;
}
blockquote ol:last-child,
blockquote p:last-child,
blockquote ul:last-child {
  margin-bottom: 0;
}
blockquote .small,
blockquote footer,
blockquote small {
  display: block;
  font-size: 80%;
  line-height: 1.428571429;
  color: #777;
}
blockquote .small:before,
blockquote footer:before,
blockquote small:before {
  content: "\2014 \00A0";
}
.blockquote-reverse,
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #eee;
  border-left: 0;
  text-align: right;
}
.blockquote-reverse .small:before,
.blockquote-reverse footer:before,
.blockquote-reverse small:before,
blockquote.pull-right .small:before,
blockquote.pull-right footer:before,
blockquote.pull-right small:before {
  content: "";
}
.blockquote-reverse .small:after,
.blockquote-reverse footer:after,
.blockquote-reverse small:after,
blockquote.pull-right .small:after,
blockquote.pull-right footer:after,
blockquote.pull-right small:after {
  content: "\00A0 \2014";
}
address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.428571429;
}
code,
kbd,
pre,
samp {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 4px;
}
kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: #fff;
  background-color: #333;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
}
kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
  box-shadow: none;
}
pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.428571429;
  word-break: break-all;
  word-wrap: break-word;
  color: #333;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px;
}
pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.container:after,
.container:before {
  content: " ";
  display: table;
}
.container:after {
  clear: both;
}
@media (min-width: 768px) {
  .container {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 1170px;
  }
}
.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.container-fluid:after,
.container-fluid:before {
  content: " ";
  display: table;
}
.container-fluid:after {
  clear: both;
}
.row {
  margin-left: -15px;
  margin-right: -15px;
}
.row:after,
.row:before {
  content: " ";
  display: table;
}
.row:after {
  clear: both;
}
.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-xs-1,
.col-xs-10,
.col-xs-11,
.col-xs-12,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
}
.col-xs-1,
.col-xs-10,
.col-xs-11,
.col-xs-12,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9 {
  float: left;
}
.col-xs-1 {
  width: 8.3333333333%;
}
.col-xs-2 {
  width: 16.6666666667%;
}
.col-xs-3 {
  width: 25%;
}
.col-xs-4 {
  width: 33.3333333333%;
}
.col-xs-5 {
  width: 41.6666666667%;
}
.col-xs-6 {
  width: 50%;
}
.col-xs-7 {
  width: 58.3333333333%;
}
.col-xs-8 {
  width: 66.6666666667%;
}
.col-xs-9 {
  width: 75%;
}
.col-xs-10 {
  width: 83.3333333333%;
}
.col-xs-11 {
  width: 91.6666666667%;
}
.col-xs-12 {
  width: 100%;
}
.col-xs-pull-0 {
  right: auto;
}
.col-xs-pull-1 {
  right: 8.3333333333%;
}
.col-xs-pull-2 {
  right: 16.6666666667%;
}
.col-xs-pull-3 {
  right: 25%;
}
.col-xs-pull-4 {
  right: 33.3333333333%;
}
.col-xs-pull-5 {
  right: 41.6666666667%;
}
.col-xs-pull-6 {
  right: 50%;
}
.col-xs-pull-7 {
  right: 58.3333333333%;
}
.col-xs-pull-8 {
  right: 66.6666666667%;
}
.col-xs-pull-9 {
  right: 75%;
}
.col-xs-pull-10 {
  right: 83.3333333333%;
}
.col-xs-pull-11 {
  right: 91.6666666667%;
}
.col-xs-pull-12 {
  right: 100%;
}
.col-xs-push-0 {
  left: auto;
}
.col-xs-push-1 {
  left: 8.3333333333%;
}
.col-xs-push-2 {
  left: 16.6666666667%;
}
.col-xs-push-3 {
  left: 25%;
}
.col-xs-push-4 {
  left: 33.3333333333%;
}
.col-xs-push-5 {
  left: 41.6666666667%;
}
.col-xs-push-6 {
  left: 50%;
}
.col-xs-push-7 {
  left: 58.3333333333%;
}
.col-xs-push-8 {
  left: 66.6666666667%;
}
.col-xs-push-9 {
  left: 75%;
}
.col-xs-push-10 {
  left: 83.3333333333%;
}
.col-xs-push-11 {
  left: 91.6666666667%;
}
.col-xs-push-12 {
  left: 100%;
}
.col-xs-offset-0 {
  margin-left: 0;
}
.col-xs-offset-1 {
  margin-left: 8.3333333333%;
}
.col-xs-offset-2 {
  margin-left: 16.6666666667%;
}
.col-xs-offset-3 {
  margin-left: 25%;
}
.col-xs-offset-4 {
  margin-left: 33.3333333333%;
}
.col-xs-offset-5 {
  margin-left: 41.6666666667%;
}
.col-xs-offset-6 {
  margin-left: 50%;
}
.col-xs-offset-7 {
  margin-left: 58.3333333333%;
}
.col-xs-offset-8 {
  margin-left: 66.6666666667%;
}
.col-xs-offset-9 {
  margin-left: 75%;
}
.col-xs-offset-10 {
  margin-left: 83.3333333333%;
}
.col-xs-offset-11 {
  margin-left: 91.6666666667%;
}
.col-xs-offset-12 {
  margin-left: 100%;
}
@media (min-width: 768px) {
  .col-sm-1,
  .col-sm-10,
  .col-sm-11,
  .col-sm-12,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9 {
    float: left;
  }
  .col-sm-1 {
    width: 8.3333333333%;
  }
  .col-sm-2 {
    width: 16.6666666667%;
  }
  .col-sm-3 {
    width: 25%;
  }
  .col-sm-4 {
    width: 33.3333333333%;
  }
  .col-sm-5 {
    width: 41.6666666667%;
  }
  .col-sm-6 {
    width: 50%;
  }
  .col-sm-7 {
    width: 58.3333333333%;
  }
  .col-sm-8 {
    width: 66.6666666667%;
  }
  .col-sm-9 {
    width: 75%;
  }
  .col-sm-10 {
    width: 83.3333333333%;
  }
  .col-sm-11 {
    width: 91.6666666667%;
  }
  .col-sm-12 {
    width: 100%;
  }
  .col-sm-pull-0 {
    right: auto;
  }
  .col-sm-pull-1 {
    right: 8.3333333333%;
  }
  .col-sm-pull-2 {
    right: 16.6666666667%;
  }
  .col-sm-pull-3 {
    right: 25%;
  }
  .col-sm-pull-4 {
    right: 33.3333333333%;
  }
  .col-sm-pull-5 {
    right: 41.6666666667%;
  }
  .col-sm-pull-6 {
    right: 50%;
  }
  .col-sm-pull-7 {
    right: 58.3333333333%;
  }
  .col-sm-pull-8 {
    right: 66.6666666667%;
  }
  .col-sm-pull-9 {
    right: 75%;
  }
  .col-sm-pull-10 {
    right: 83.3333333333%;
  }
  .col-sm-pull-11 {
    right: 91.6666666667%;
  }
  .col-sm-pull-12 {
    right: 100%;
  }
  .col-sm-push-0 {
    left: auto;
  }
  .col-sm-push-1 {
    left: 8.3333333333%;
  }
  .col-sm-push-2 {
    left: 16.6666666667%;
  }
  .col-sm-push-3 {
    left: 25%;
  }
  .col-sm-push-4 {
    left: 33.3333333333%;
  }
  .col-sm-push-5 {
    left: 41.6666666667%;
  }
  .col-sm-push-6 {
    left: 50%;
  }
  .col-sm-push-7 {
    left: 58.3333333333%;
  }
  .col-sm-push-8 {
    left: 66.6666666667%;
  }
  .col-sm-push-9 {
    left: 75%;
  }
  .col-sm-push-10 {
    left: 83.3333333333%;
  }
  .col-sm-push-11 {
    left: 91.6666666667%;
  }
  .col-sm-push-12 {
    left: 100%;
  }
  .col-sm-offset-0 {
    margin-left: 0;
  }
  .col-sm-offset-1 {
    margin-left: 8.3333333333%;
  }
  .col-sm-offset-2 {
    margin-left: 16.6666666667%;
  }
  .col-sm-offset-3 {
    margin-left: 25%;
  }
  .col-sm-offset-4 {
    margin-left: 33.3333333333%;
  }
  .col-sm-offset-5 {
    margin-left: 41.6666666667%;
  }
  .col-sm-offset-6 {
    margin-left: 50%;
  }
  .col-sm-offset-7 {
    margin-left: 58.3333333333%;
  }
  .col-sm-offset-8 {
    margin-left: 66.6666666667%;
  }
  .col-sm-offset-9 {
    margin-left: 75%;
  }
  .col-sm-offset-10 {
    margin-left: 83.3333333333%;
  }
  .col-sm-offset-11 {
    margin-left: 91.6666666667%;
  }
  .col-sm-offset-12 {
    margin-left: 100%;
  }
}
@media (min-width: 992px) {
  .col-md-1,
  .col-md-10,
  .col-md-11,
  .col-md-12,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9 {
    float: left;
  }
  .col-md-1 {
    width: 8.3333333333%;
  }
  .col-md-2 {
    width: 16.6666666667%;
  }
  .col-md-3 {
    width: 25%;
  }
  .col-md-4 {
    width: 33.3333333333%;
  }
  .col-md-5 {
    width: 41.6666666667%;
  }
  .col-md-6 {
    width: 50%;
  }
  .col-md-7 {
    width: 58.3333333333%;
  }
  .col-md-8 {
    width: 66.6666666667%;
  }
  .col-md-9 {
    width: 75%;
  }
  .col-md-10 {
    width: 83.3333333333%;
  }
  .col-md-11 {
    width: 91.6666666667%;
  }
  .col-md-12 {
    width: 100%;
  }
  .col-md-pull-0 {
    right: auto;
  }
  .col-md-pull-1 {
    right: 8.3333333333%;
  }
  .col-md-pull-2 {
    right: 16.6666666667%;
  }
  .col-md-pull-3 {
    right: 25%;
  }
  .col-md-pull-4 {
    right: 33.3333333333%;
  }
  .col-md-pull-5 {
    right: 41.6666666667%;
  }
  .col-md-pull-6 {
    right: 50%;
  }
  .col-md-pull-7 {
    right: 58.3333333333%;
  }
  .col-md-pull-8 {
    right: 66.6666666667%;
  }
  .col-md-pull-9 {
    right: 75%;
  }
  .col-md-pull-10 {
    right: 83.3333333333%;
  }
  .col-md-pull-11 {
    right: 91.6666666667%;
  }
  .col-md-pull-12 {
    right: 100%;
  }
  .col-md-push-0 {
    left: auto;
  }
  .col-md-push-1 {
    left: 8.3333333333%;
  }
  .col-md-push-2 {
    left: 16.6666666667%;
  }
  .col-md-push-3 {
    left: 25%;
  }
  .col-md-push-4 {
    left: 33.3333333333%;
  }
  .col-md-push-5 {
    left: 41.6666666667%;
  }
  .col-md-push-6 {
    left: 50%;
  }
  .col-md-push-7 {
    left: 58.3333333333%;
  }
  .col-md-push-8 {
    left: 66.6666666667%;
  }
  .col-md-push-9 {
    left: 75%;
  }
  .col-md-push-10 {
    left: 83.3333333333%;
  }
  .col-md-push-11 {
    left: 91.6666666667%;
  }
  .col-md-push-12 {
    left: 100%;
  }
  .col-md-offset-0 {
    margin-left: 0;
  }
  .col-md-offset-1 {
    margin-left: 8.3333333333%;
  }
  .col-md-offset-2 {
    margin-left: 16.6666666667%;
  }
  .col-md-offset-3 {
    margin-left: 25%;
  }
  .col-md-offset-4 {
    margin-left: 33.3333333333%;
  }
  .col-md-offset-5 {
    margin-left: 41.6666666667%;
  }
  .col-md-offset-6 {
    margin-left: 50%;
  }
  .col-md-offset-7 {
    margin-left: 58.3333333333%;
  }
  .col-md-offset-8 {
    margin-left: 66.6666666667%;
  }
  .col-md-offset-9 {
    margin-left: 75%;
  }
  .col-md-offset-10 {
    margin-left: 83.3333333333%;
  }
  .col-md-offset-11 {
    margin-left: 91.6666666667%;
  }
  .col-md-offset-12 {
    margin-left: 100%;
  }
}
@media (min-width: 1200px) {
  .col-lg-1,
  .col-lg-10,
  .col-lg-11,
  .col-lg-12,
  .col-lg-2,
  .col-lg-3,
  .col-lg-4,
  .col-lg-5,
  .col-lg-6,
  .col-lg-7,
  .col-lg-8,
  .col-lg-9 {
    float: left;
  }
  .col-lg-1 {
    width: 8.3333333333%;
  }
  .col-lg-2 {
    width: 16.6666666667%;
  }
  .col-lg-3 {
    width: 25%;
  }
  .col-lg-4 {
    width: 33.3333333333%;
  }
  .col-lg-5 {
    width: 41.6666666667%;
  }
  .col-lg-6 {
    width: 50%;
  }
  .col-lg-7 {
    width: 58.3333333333%;
  }
  .col-lg-8 {
    width: 66.6666666667%;
  }
  .col-lg-9 {
    width: 75%;
  }
  .col-lg-10 {
    width: 83.3333333333%;
  }
  .col-lg-11 {
    width: 91.6666666667%;
  }
  .col-lg-12 {
    width: 100%;
  }
  .col-lg-pull-0 {
    right: auto;
  }
  .col-lg-pull-1 {
    right: 8.3333333333%;
  }
  .col-lg-pull-2 {
    right: 16.6666666667%;
  }
  .col-lg-pull-3 {
    right: 25%;
  }
  .col-lg-pull-4 {
    right: 33.3333333333%;
  }
  .col-lg-pull-5 {
    right: 41.6666666667%;
  }
  .col-lg-pull-6 {
    right: 50%;
  }
  .col-lg-pull-7 {
    right: 58.3333333333%;
  }
  .col-lg-pull-8 {
    right: 66.6666666667%;
  }
  .col-lg-pull-9 {
    right: 75%;
  }
  .col-lg-pull-10 {
    right: 83.3333333333%;
  }
  .col-lg-pull-11 {
    right: 91.6666666667%;
  }
  .col-lg-pull-12 {
    right: 100%;
  }
  .col-lg-push-0 {
    left: auto;
  }
  .col-lg-push-1 {
    left: 8.3333333333%;
  }
  .col-lg-push-2 {
    left: 16.6666666667%;
  }
  .col-lg-push-3 {
    left: 25%;
  }
  .col-lg-push-4 {
    left: 33.3333333333%;
  }
  .col-lg-push-5 {
    left: 41.6666666667%;
  }
  .col-lg-push-6 {
    left: 50%;
  }
  .col-lg-push-7 {
    left: 58.3333333333%;
  }
  .col-lg-push-8 {
    left: 66.6666666667%;
  }
  .col-lg-push-9 {
    left: 75%;
  }
  .col-lg-push-10 {
    left: 83.3333333333%;
  }
  .col-lg-push-11 {
    left: 91.6666666667%;
  }
  .col-lg-push-12 {
    left: 100%;
  }
  .col-lg-offset-0 {
    margin-left: 0;
  }
  .col-lg-offset-1 {
    margin-left: 8.3333333333%;
  }
  .col-lg-offset-2 {
    margin-left: 16.6666666667%;
  }
  .col-lg-offset-3 {
    margin-left: 25%;
  }
  .col-lg-offset-4 {
    margin-left: 33.3333333333%;
  }
  .col-lg-offset-5 {
    margin-left: 41.6666666667%;
  }
  .col-lg-offset-6 {
    margin-left: 50%;
  }
  .col-lg-offset-7 {
    margin-left: 58.3333333333%;
  }
  .col-lg-offset-8 {
    margin-left: 66.6666666667%;
  }
  .col-lg-offset-9 {
    margin-left: 75%;
  }
  .col-lg-offset-10 {
    margin-left: 83.3333333333%;
  }
  .col-lg-offset-11 {
    margin-left: 91.6666666667%;
  }
  .col-lg-offset-12 {
    margin-left: 100%;
  }
}
table {
  background-color: transparent;
}
caption {
  padding-top: 8px;
  padding-bottom: 8px;
  color: #777;
  text-align: left;
}
th {
  text-align: left;
}
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}
.table > tbody > tr > td,
.table > tbody > tr > th,
.table > tfoot > tr > td,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > thead > tr > th {
  padding: 8px;
  line-height: 1.428571429;
  vertical-align: top;
  border-top: 1px solid #ddd;
}
.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #ddd;
}
.table > caption + thead > tr:first-child > td,
.table > caption + thead > tr:first-child > th,
.table > colgroup + thead > tr:first-child > td,
.table > colgroup + thead > tr:first-child > th,
.table > thead:first-child > tr:first-child > td,
.table > thead:first-child > tr:first-child > th {
  border-top: 0;
}
.table > tbody + tbody {
  border-top: 2px solid #ddd;
}
.table .table {
  background-color: #fff;
}
.table-condensed > tbody > tr > td,
.table-condensed > tbody > tr > th,
.table-condensed > tfoot > tr > td,
.table-condensed > tfoot > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > thead > tr > th {
  padding: 5px;
}
.table-bordered {
  border: 1px solid #ddd;
}
.table-bordered > tbody > tr > td,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > td,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > thead > tr > th {
  border: 1px solid #ddd;
}
.table-bordered > thead > tr > td,
.table-bordered > thead > tr > th {
  border-bottom-width: 2px;
}
.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9;
}
.table-hover > tbody > tr:hover {
  background-color: #f5f5f5;
}
table col[class*="col-"] {
  position: static;
  float: none;
  display: table-column;
}
table td[class*="col-"],
table th[class*="col-"] {
  position: static;
  float: none;
  display: table-cell;
}
.table > tbody > tr.active > td,
.table > tbody > tr.active > th,
.table > tbody > tr > td.active,
.table > tbody > tr > th.active,
.table > tfoot > tr.active > td,
.table > tfoot > tr.active > th,
.table > tfoot > tr > td.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > thead > tr.active > th,
.table > thead > tr > td.active,
.table > thead > tr > th.active {
  background-color: #f5f5f5;
}
.table-hover > tbody > tr.active:hover > td,
.table-hover > tbody > tr.active:hover > th,
.table-hover > tbody > tr:hover > .active,
.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover {
  background-color: #e8e8e8;
}
.table > tbody > tr.success > td,
.table > tbody > tr.success > th,
.table > tbody > tr > td.success,
.table > tbody > tr > th.success,
.table > tfoot > tr.success > td,
.table > tfoot > tr.success > th,
.table > tfoot > tr > td.success,
.table > tfoot > tr > th.success,
.table > thead > tr.success > td,
.table > thead > tr.success > th,
.table > thead > tr > td.success,
.table > thead > tr > th.success {
  background-color: #dff0d8;
}
.table-hover > tbody > tr.success:hover > td,
.table-hover > tbody > tr.success:hover > th,
.table-hover > tbody > tr:hover > .success,
.table-hover > tbody > tr > td.success:hover,
.table-hover > tbody > tr > th.success:hover {
  background-color: #d0e9c6;
}
.table > tbody > tr.info > td,
.table > tbody > tr.info > th,
.table > tbody > tr > td.info,
.table > tbody > tr > th.info,
.table > tfoot > tr.info > td,
.table > tfoot > tr.info > th,
.table > tfoot > tr > td.info,
.table > tfoot > tr > th.info,
.table > thead > tr.info > td,
.table > thead > tr.info > th,
.table > thead > tr > td.info,
.table > thead > tr > th.info {
  background-color: #d9edf7;
}
.table-hover > tbody > tr.info:hover > td,
.table-hover > tbody > tr.info:hover > th,
.table-hover > tbody > tr:hover > .info,
.table-hover > tbody > tr > td.info:hover,
.table-hover > tbody > tr > th.info:hover {
  background-color: #c4e3f3;
}
.table > tbody > tr.warning > td,
.table > tbody > tr.warning > th,
.table > tbody > tr > td.warning,
.table > tbody > tr > th.warning,
.table > tfoot > tr.warning > td,
.table > tfoot > tr.warning > th,
.table > tfoot > tr > td.warning,
.table > tfoot > tr > th.warning,
.table > thead > tr.warning > td,
.table > thead > tr.warning > th,
.table > thead > tr > td.warning,
.table > thead > tr > th.warning {
  background-color: #fcf8e3;
}
.table-hover > tbody > tr.warning:hover > td,
.table-hover > tbody > tr.warning:hover > th,
.table-hover > tbody > tr:hover > .warning,
.table-hover > tbody > tr > td.warning:hover,
.table-hover > tbody > tr > th.warning:hover {
  background-color: #faf2cc;
}
.table > tbody > tr.danger > td,
.table > tbody > tr.danger > th,
.table > tbody > tr > td.danger,
.table > tbody > tr > th.danger,
.table > tfoot > tr.danger > td,
.table > tfoot > tr.danger > th,
.table > tfoot > tr > td.danger,
.table > tfoot > tr > th.danger,
.table > thead > tr.danger > td,
.table > thead > tr.danger > th,
.table > thead > tr > td.danger,
.table > thead > tr > th.danger {
  background-color: #f2dede;
}
.table-hover > tbody > tr.danger:hover > td,
.table-hover > tbody > tr.danger:hover > th,
.table-hover > tbody > tr:hover > .danger,
.table-hover > tbody > tr > td.danger:hover,
.table-hover > tbody > tr > th.danger:hover {
  background-color: #ebcccc;
}
.table-responsive {
  overflow-x: auto;
  min-height: 0.01%;
}
@media screen and (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 15px;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #ddd;
  }
  .table-responsive > .table {
    margin-bottom: 0;
  }
  .table-responsive > .table > tbody > tr > td,
  .table-responsive > .table > tbody > tr > th,
  .table-responsive > .table > tfoot > tr > td,
  .table-responsive > .table > tfoot > tr > th,
  .table-responsive > .table > thead > tr > td,
  .table-responsive > .table > thead > tr > th {
    white-space: nowrap;
  }
  .table-responsive > .table-bordered {
    border: 0;
  }
  .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .table-responsive > .table-bordered > tfoot > tr > td:first-child,
  .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .table-responsive > .table-bordered > thead > tr > td:first-child,
  .table-responsive > .table-bordered > thead > tr > th:first-child {
    border-left: 0;
  }
  .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .table-responsive > .table-bordered > tfoot > tr > td:last-child,
  .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .table-responsive > .table-bordered > thead > tr > td:last-child,
  .table-responsive > .table-bordered > thead > tr > th:last-child {
    border-right: 0;
  }
  .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .table-responsive > .table-bordered > tfoot > tr:last-child > td,
  .table-responsive > .table-bordered > tfoot > tr:last-child > th {
    border-bottom: 0;
  }
}
fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0;
}
legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: inherit;
  color: #333;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: 700;
}
input[type="search"] {
  box-sizing: border-box;
}
input[type="checkbox"],
input[type="radio"] {
  margin: 4px 0 0;
  margin-top: 1px\9;
  line-height: normal;
}
input[type="file"] {
  display: block;
}
input[type="range"] {
  display: block;
  width: 100%;
}
select[multiple],
select[size] {
  height: auto;
}
input[type="file"]:focus,
input[type="checkbox"]:focus,
input[type="radio"]:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
output {
  display: block;
  padding-top: 7px;
  font-size: 14px;
  line-height: 1.428571429;
  color: #555;
}
.form-control {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428571429;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.form-control:focus {
  border-color: #66afe9;
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 8px rgba(102, 175, 233, 0.6);
}
.form-control::-moz-placeholder {
  color: #999;
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: #999;
}
.form-control::-webkit-input-placeholder {
  color: #999;
}
.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  background-color: #eee;
  opacity: 1;
}
.form-control[disabled],
fieldset[disabled] .form-control {
  cursor: not-allowed;
}
textarea.form-control {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: none;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="date"],
  input[type="time"],
  input[type="datetime-local"],
  input[type="month"] {
    line-height: 34px;
  }
  .input-group-sm input[type="date"],
  .input-group-sm input[type="time"],
  .input-group-sm input[type="datetime-local"],
  .input-group-sm input[type="month"],
  .input-group-sm > .input-group-btn > input[type="date"].btn,
  .input-group-sm > .input-group-btn > input[type="time"].btn,
  .input-group-sm > .input-group-btn > input[type="datetime-local"].btn,
  .input-group-sm > .input-group-btn > input[type="month"].btn,
  .input-group-sm > input[type="date"].form-control,
  .input-group-sm > input[type="date"].input-group-addon,
  .input-group-sm > input[type="time"].form-control,
  .input-group-sm > input[type="time"].input-group-addon,
  .input-group-sm > input[type="datetime-local"].form-control,
  .input-group-sm > input[type="datetime-local"].input-group-addon,
  .input-group-sm > input[type="month"].form-control,
  .input-group-sm > input[type="month"].input-group-addon,
  input[type="date"].input-sm,
  input[type="time"].input-sm,
  input[type="datetime-local"].input-sm,
  input[type="month"].input-sm {
    line-height: 30px;
  }
  .input-group-lg input[type="date"],
  .input-group-lg input[type="time"],
  .input-group-lg input[type="datetime-local"],
  .input-group-lg input[type="month"],
  .input-group-lg > .input-group-btn > input[type="date"].btn,
  .input-group-lg > .input-group-btn > input[type="time"].btn,
  .input-group-lg > .input-group-btn > input[type="datetime-local"].btn,
  .input-group-lg > .input-group-btn > input[type="month"].btn,
  .input-group-lg > input[type="date"].form-control,
  .input-group-lg > input[type="date"].input-group-addon,
  .input-group-lg > input[type="time"].form-control,
  .input-group-lg > input[type="time"].input-group-addon,
  .input-group-lg > input[type="datetime-local"].form-control,
  .input-group-lg > input[type="datetime-local"].input-group-addon,
  .input-group-lg > input[type="month"].form-control,
  .input-group-lg > input[type="month"].input-group-addon,
  input[type="date"].input-lg,
  input[type="time"].input-lg,
  input[type="datetime-local"].input-lg,
  input[type="month"].input-lg {
    line-height: 46px;
  }
}
.form-group {
  margin-bottom: 15px;
}
.checkbox,
.radio {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
}
.checkbox label,
.radio label {
  min-height: 20px;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: 400;
  cursor: pointer;
}
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"],
.radio input[type="radio"],
.radio-inline input[type="radio"] {
  position: absolute;
  margin-left: -20px;
  margin-top: 4px\9;
}
.checkbox + .checkbox,
.radio + .radio {
  margin-top: -5px;
}
.checkbox-inline,
.radio-inline {
  position: relative;
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: 400;
  cursor: pointer;
}
.checkbox-inline + .checkbox-inline,
.radio-inline + .radio-inline {
  margin-top: 0;
  margin-left: 10px;
}
fieldset[disabled] input[type="checkbox"],
fieldset[disabled] input[type="radio"],
input[type="checkbox"].disabled,
input[type="checkbox"][disabled],
input[type="radio"].disabled,
input[type="radio"][disabled] {
  cursor: not-allowed;
}
.checkbox-inline.disabled,
.radio-inline.disabled,
fieldset[disabled] .checkbox-inline,
fieldset[disabled] .radio-inline {
  cursor: not-allowed;
}
.checkbox.disabled label,
.radio.disabled label,
fieldset[disabled] .checkbox label,
fieldset[disabled] .radio label {
  cursor: not-allowed;
}
.form-control-static {
  padding-top: 7px;
  padding-bottom: 7px;
  margin-bottom: 0;
  min-height: 34px;
}
.form-control-static.input-lg,
.form-control-static.input-sm,
.input-group-lg > .form-control-static.form-control,
.input-group-lg > .form-control-static.input-group-addon,
.input-group-lg > .input-group-btn > .form-control-static.btn,
.input-group-sm > .form-control-static.form-control,
.input-group-sm > .form-control-static.input-group-addon,
.input-group-sm > .input-group-btn > .form-control-static.btn {
  padding-left: 0;
  padding-right: 0;
}
.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn,
.input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.input-group-sm > .input-group-btn > select.btn,
.input-group-sm > select.form-control,
.input-group-sm > select.input-group-addon,
select.input-sm {
  height: 30px;
  line-height: 30px;
}
.input-group-sm > .input-group-btn > select[multiple].btn,
.input-group-sm > .input-group-btn > textarea.btn,
.input-group-sm > select[multiple].form-control,
.input-group-sm > select[multiple].input-group-addon,
.input-group-sm > textarea.form-control,
.input-group-sm > textarea.input-group-addon,
select[multiple].input-sm,
textarea.input-sm {
  height: auto;
}
.form-group-sm .form-control {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.form-group-sm select.form-control {
  height: 30px;
  line-height: 30px;
}
.form-group-sm select[multiple].form-control,
.form-group-sm textarea.form-control {
  height: auto;
}
.form-group-sm .form-control-static {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  min-height: 32px;
}
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn,
.input-lg {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.input-group-lg > .input-group-btn > select.btn,
.input-group-lg > select.form-control,
.input-group-lg > select.input-group-addon,
select.input-lg {
  height: 46px;
  line-height: 46px;
}
.input-group-lg > .input-group-btn > select[multiple].btn,
.input-group-lg > .input-group-btn > textarea.btn,
.input-group-lg > select[multiple].form-control,
.input-group-lg > select[multiple].input-group-addon,
.input-group-lg > textarea.form-control,
.input-group-lg > textarea.input-group-addon,
select[multiple].input-lg,
textarea.input-lg {
  height: auto;
}
.form-group-lg .form-control {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.form-group-lg select.form-control {
  height: 46px;
  line-height: 46px;
}
.form-group-lg select[multiple].form-control,
.form-group-lg textarea.form-control {
  height: auto;
}
.form-group-lg .form-control-static {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  min-height: 38px;
}
.has-feedback {
  position: relative;
}
.has-feedback .form-control {
  padding-right: 42.5px;
}
.form-control-feedback {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  display: block;
  width: 34px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  pointer-events: none;
}
.input-group-lg > .form-control + .form-control-feedback,
.input-group-lg > .input-group-addon + .form-control-feedback,
.input-group-lg > .input-group-btn > .btn + .form-control-feedback,
.input-lg + .form-control-feedback {
  width: 46px;
  height: 46px;
  line-height: 46px;
}
.input-group-sm > .form-control + .form-control-feedback,
.input-group-sm > .input-group-addon + .form-control-feedback,
.input-group-sm > .input-group-btn > .btn + .form-control-feedback,
.input-sm + .form-control-feedback {
  width: 30px;
  height: 30px;
  line-height: 30px;
}
.has-success .checkbox,
.has-success .checkbox-inline,
.has-success .control-label,
.has-success .help-block,
.has-success .radio,
.has-success .radio-inline,
.has-success.checkbox label,
.has-success.checkbox-inline label,
.has-success.radio label,
.has-success.radio-inline label {
  color: #3c763d;
}
.has-success .form-control {
  border-color: #3c763d;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-success .form-control:focus {
  border-color: #2b542c;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
}
.has-success .input-group-addon {
  color: #3c763d;
  border-color: #3c763d;
  background-color: #dff0d8;
}
.has-success .form-control-feedback {
  color: #3c763d;
}
.has-warning .checkbox,
.has-warning .checkbox-inline,
.has-warning .control-label,
.has-warning .help-block,
.has-warning .radio,
.has-warning .radio-inline,
.has-warning.checkbox label,
.has-warning.checkbox-inline label,
.has-warning.radio label,
.has-warning.radio-inline label {
  color: #8a6d3b;
}
.has-warning .form-control {
  border-color: #8a6d3b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-warning .form-control:focus {
  border-color: #66512c;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
}
.has-warning .input-group-addon {
  color: #8a6d3b;
  border-color: #8a6d3b;
  background-color: #fcf8e3;
}
.has-warning .form-control-feedback {
  color: #8a6d3b;
}
.has-error .checkbox,
.has-error .checkbox-inline,
.has-error .control-label,
.has-error .help-block,
.has-error .radio,
.has-error .radio-inline,
.has-error.checkbox label,
.has-error.checkbox-inline label,
.has-error.radio label,
.has-error.radio-inline label {
  color: #a94442;
}
.has-error .form-control {
  border-color: #a94442;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-error .form-control:focus {
  border-color: #843534;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}
.has-error .input-group-addon {
  color: #a94442;
  border-color: #a94442;
  background-color: #f2dede;
}
.has-error .form-control-feedback {
  color: #a94442;
}
.has-feedback label ~ .form-control-feedback {
  top: 25px;
}
.has-feedback label.sr-only ~ .form-control-feedback {
  top: 0;
}
.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #737373;
}
@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-static {
    display: inline-block;
  }
  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .form-inline .input-group .form-control,
  .form-inline .input-group .input-group-addon,
  .form-inline .input-group .input-group-btn {
    width: auto;
  }
  .form-inline .input-group > .form-control {
    width: 100%;
  }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .checkbox,
  .form-inline .radio {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .checkbox label,
  .form-inline .radio label {
    padding-left: 0;
  }
  .form-inline .checkbox input[type="checkbox"],
  .form-inline .radio input[type="radio"] {
    position: relative;
    margin-left: 0;
  }
  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}
.form-horizontal .checkbox,
.form-horizontal .checkbox-inline,
.form-horizontal .radio,
.form-horizontal .radio-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px;
}
.form-horizontal .checkbox,
.form-horizontal .radio {
  min-height: 27px;
}
.form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px;
}
.form-horizontal .form-group:after,
.form-horizontal .form-group:before {
  content: " ";
  display: table;
}
.form-horizontal .form-group:after {
  clear: both;
}
@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: right;
    margin-bottom: 0;
    padding-top: 7px;
  }
}
.form-horizontal .has-feedback .form-control-feedback {
  right: 15px;
}
@media (min-width: 768px) {
  .form-horizontal .form-group-lg .control-label {
    padding-top: 14.33px;
  }
}
@media (min-width: 768px) {
  .form-horizontal .form-group-sm .control-label {
    padding-top: 6px;
  }
}
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428571429;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.btn.active.focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn:active:focus,
.btn:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.btn.focus,
.btn:focus,
.btn:hover {
  color: #333;
  text-decoration: none;
}
.btn.active,
.btn:active {
  outline: 0;
  background-image: none;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.65;
  filter: alpha(opacity=65);
  box-shadow: none;
}
.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}
.btn-default.active,
.btn-default.focus,
.btn-default:active,
.btn-default:focus,
.btn-default:hover,
.open > .btn-default.dropdown-toggle {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}
.btn-default.active,
.btn-default:active,
.open > .btn-default.dropdown-toggle {
  background-image: none;
}
.btn-default.disabled,
.btn-default.disabled.active,
.btn-default.disabled.focus,
.btn-default.disabled:active,
.btn-default.disabled:focus,
.btn-default.disabled:hover,
.btn-default[disabled],
.btn-default[disabled].active,
.btn-default[disabled].focus,
.btn-default[disabled]:active,
.btn-default[disabled]:focus,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default,
fieldset[disabled] .btn-default.active,
fieldset[disabled] .btn-default.focus,
fieldset[disabled] .btn-default:active,
fieldset[disabled] .btn-default:focus,
fieldset[disabled] .btn-default:hover {
  background-color: #fff;
  border-color: #ccc;
}
.btn-default .badge {
  color: #fff;
  background-color: #333;
}
.btn-primary {
  color: #fff;
  background-color: #337ab7;
  border-color: #2e6da4;
}
.btn-primary.active,
.btn-primary.focus,
.btn-primary:active,
.btn-primary:focus,
.btn-primary:hover,
.open > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #286090;
  border-color: #204d74;
}
.btn-primary.active,
.btn-primary:active,
.open > .btn-primary.dropdown-toggle {
  background-image: none;
}
.btn-primary.disabled,
.btn-primary.disabled.active,
.btn-primary.disabled.focus,
.btn-primary.disabled:active,
.btn-primary.disabled:focus,
.btn-primary.disabled:hover,
.btn-primary[disabled],
.btn-primary[disabled].active,
.btn-primary[disabled].focus,
.btn-primary[disabled]:active,
.btn-primary[disabled]:focus,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary,
fieldset[disabled] .btn-primary.active,
fieldset[disabled] .btn-primary.focus,
fieldset[disabled] .btn-primary:active,
fieldset[disabled] .btn-primary:focus,
fieldset[disabled] .btn-primary:hover {
  background-color: #337ab7;
  border-color: #2e6da4;
}
.btn-primary .badge {
  color: #337ab7;
  background-color: #fff;
}
.btn-success {
  color: #fff;
  background-color: #5cb85c;
  border-color: #4cae4c;
}
.btn-success.active,
.btn-success.focus,
.btn-success:active,
.btn-success:focus,
.btn-success:hover,
.open > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #449d44;
  border-color: #398439;
}
.btn-success.active,
.btn-success:active,
.open > .btn-success.dropdown-toggle {
  background-image: none;
}
.btn-success.disabled,
.btn-success.disabled.active,
.btn-success.disabled.focus,
.btn-success.disabled:active,
.btn-success.disabled:focus,
.btn-success.disabled:hover,
.btn-success[disabled],
.btn-success[disabled].active,
.btn-success[disabled].focus,
.btn-success[disabled]:active,
.btn-success[disabled]:focus,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success,
fieldset[disabled] .btn-success.active,
fieldset[disabled] .btn-success.focus,
fieldset[disabled] .btn-success:active,
fieldset[disabled] .btn-success:focus,
fieldset[disabled] .btn-success:hover {
  background-color: #5cb85c;
  border-color: #4cae4c;
}
.btn-success .badge {
  color: #5cb85c;
  background-color: #fff;
}
.btn-info {
  color: #fff;
  background-color: #5bc0de;
  border-color: #46b8da;
}
.btn-info.active,
.btn-info.focus,
.btn-info:active,
.btn-info:focus,
.btn-info:hover,
.open > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #31b0d5;
  border-color: #269abc;
}
.btn-info.active,
.btn-info:active,
.open > .btn-info.dropdown-toggle {
  background-image: none;
}
.btn-info.disabled,
.btn-info.disabled.active,
.btn-info.disabled.focus,
.btn-info.disabled:active,
.btn-info.disabled:focus,
.btn-info.disabled:hover,
.btn-info[disabled],
.btn-info[disabled].active,
.btn-info[disabled].focus,
.btn-info[disabled]:active,
.btn-info[disabled]:focus,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info,
fieldset[disabled] .btn-info.active,
fieldset[disabled] .btn-info.focus,
fieldset[disabled] .btn-info:active,
fieldset[disabled] .btn-info:focus,
fieldset[disabled] .btn-info:hover {
  background-color: #5bc0de;
  border-color: #46b8da;
}
.btn-info .badge {
  color: #5bc0de;
  background-color: #fff;
}
.btn-warning {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236;
}
.btn-warning.active,
.btn-warning.focus,
.btn-warning:active,
.btn-warning:focus,
.btn-warning:hover,
.open > .btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #ec971f;
  border-color: #d58512;
}
.btn-warning.active,
.btn-warning:active,
.open > .btn-warning.dropdown-toggle {
  background-image: none;
}
.btn-warning.disabled,
.btn-warning.disabled.active,
.btn-warning.disabled.focus,
.btn-warning.disabled:active,
.btn-warning.disabled:focus,
.btn-warning.disabled:hover,
.btn-warning[disabled],
.btn-warning[disabled].active,
.btn-warning[disabled].focus,
.btn-warning[disabled]:active,
.btn-warning[disabled]:focus,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning,
fieldset[disabled] .btn-warning.active,
fieldset[disabled] .btn-warning.focus,
fieldset[disabled] .btn-warning:active,
fieldset[disabled] .btn-warning:focus,
fieldset[disabled] .btn-warning:hover {
  background-color: #f0ad4e;
  border-color: #eea236;
}
.btn-warning .badge {
  color: #f0ad4e;
  background-color: #fff;
}
.btn-danger {
  color: #fff;
  background-color: #d9534f;
  border-color: #d43f3a;
}
.btn-danger.active,
.btn-danger.focus,
.btn-danger:active,
.btn-danger:focus,
.btn-danger:hover,
.open > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #c9302c;
  border-color: #ac2925;
}
.btn-danger.active,
.btn-danger:active,
.open > .btn-danger.dropdown-toggle {
  background-image: none;
}
.btn-danger.disabled,
.btn-danger.disabled.active,
.btn-danger.disabled.focus,
.btn-danger.disabled:active,
.btn-danger.disabled:focus,
.btn-danger.disabled:hover,
.btn-danger[disabled],
.btn-danger[disabled].active,
.btn-danger[disabled].focus,
.btn-danger[disabled]:active,
.btn-danger[disabled]:focus,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger,
fieldset[disabled] .btn-danger.active,
fieldset[disabled] .btn-danger.focus,
fieldset[disabled] .btn-danger:active,
fieldset[disabled] .btn-danger:focus,
fieldset[disabled] .btn-danger:hover {
  background-color: #d9534f;
  border-color: #d43f3a;
}
.btn-danger .badge {
  color: #d9534f;
  background-color: #fff;
}
.btn-link {
  color: #337ab7;
  font-weight: 400;
  border-radius: 0;
}
.btn-link,
.btn-link.active,
.btn-link:active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
  background-color: transparent;
  box-shadow: none;
}
.btn-link,
.btn-link:active,
.btn-link:focus,
.btn-link:hover {
  border-color: transparent;
}
.btn-link:focus,
.btn-link:hover {
  color: #23527c;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:focus,
.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:focus,
fieldset[disabled] .btn-link:hover {
  color: #777;
  text-decoration: none;
}
.btn-group-lg > .btn,
.btn-lg {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.btn-group-sm > .btn,
.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.btn-group-xs > .btn,
.btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.btn-block {
  display: block;
  width: 100%;
}
.btn-block + .btn-block {
  margin-top: 5px;
}
input[type="button"].btn-block,
input[type="reset"].btn-block,
input[type="submit"].btn-block {
  width: 100%;
}
.fade {
  opacity: 0;
  transition: opacity 0.15s linear;
}
.fade.in {
  opacity: 1;
}
.collapse {
  display: none;
}
.collapse.in {
  display: block;
}
tr.collapse.in {
  display: table-row;
}
tbody.collapse.in {
  display: table-row-group;
}
.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition-property: height, visibility;
  transition-duration: 0.35s;
  transition-timing-function: ease;
}
.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}
.dropdown,
.dropup {
  position: relative;
}
.dropdown-toggle:focus {
  outline: 0;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 14px;
  text-align: left;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box;
}
.dropdown-menu.pull-right {
  right: 0;
  left: auto;
}
.dropdown-menu .divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: 400;
  line-height: 1.428571429;
  color: #333;
  white-space: nowrap;
}
.dropdown-menu > li > a:focus,
.dropdown-menu > li > a:hover {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:focus,
.dropdown-menu > .active > a:hover {
  color: #fff;
  text-decoration: none;
  outline: 0;
  background-color: #337ab7;
}
.dropdown-menu > .disabled > a,
.dropdown-menu > .disabled > a:focus,
.dropdown-menu > .disabled > a:hover {
  color: #777;
}
.dropdown-menu > .disabled > a:focus,
.dropdown-menu > .disabled > a:hover {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  cursor: not-allowed;
}
.open > .dropdown-menu {
  display: block;
}
.open > a {
  outline: 0;
}
.dropdown-menu-right {
  left: auto;
  right: 0;
}
.dropdown-menu-left {
  left: 0;
  right: auto;
}
.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 12px;
  line-height: 1.428571429;
  color: #777;
  white-space: nowrap;
}
.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990;
}
.pull-right > .dropdown-menu {
  right: 0;
  left: auto;
}
.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  border-top: 0;
  border-bottom: 4px solid;
  content: "";
}
.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px;
}
@media (min-width: 768px) {
  .navbar-right .dropdown-menu {
    right: 0;
    left: auto;
  }
  .navbar-right .dropdown-menu-left {
    left: 0;
    right: auto;
  }
}
.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.btn-group-vertical > .btn,
.btn-group > .btn {
  position: relative;
  float: left;
}
.btn-group-vertical > .btn.active,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:hover,
.btn-group > .btn.active,
.btn-group > .btn:active,
.btn-group > .btn:focus,
.btn-group > .btn:hover {
  z-index: 2;
}
.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group {
  margin-left: -1px;
}
.btn-toolbar {
  margin-left: -5px;
}
.btn-toolbar:after,
.btn-toolbar:before {
  content: " ";
  display: table;
}
.btn-toolbar:after {
  clear: both;
}
.btn-toolbar .btn-group,
.btn-toolbar .input-group {
  float: left;
}
.btn-toolbar > .btn,
.btn-toolbar > .btn-group,
.btn-toolbar > .input-group {
  margin-left: 5px;
}
.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}
.btn-group > .btn:first-child {
  margin-left: 0;
}
.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn:last-child:not(:first-child),
.btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group > .btn-group {
  float: left;
}
.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0;
}
.btn-group > .btn + .dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px;
}
.btn-group-lg.btn-group > .btn + .dropdown-toggle,
.btn-group > .btn-lg + .dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px;
}
.btn-group.open .dropdown-toggle {
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn-group.open .dropdown-toggle.btn-link {
  box-shadow: none;
}
.btn .caret {
  margin-left: 0;
}
.btn-group-lg > .btn .caret,
.btn-lg .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0;
}
.dropup .btn-group-lg > .btn .caret,
.dropup .btn-lg .caret {
  border-width: 0 5px 5px;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group,
.btn-group-vertical > .btn-group > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.btn-group-vertical > .btn-group:after,
.btn-group-vertical > .btn-group:before {
  content: " ";
  display: table;
}
.btn-group-vertical > .btn-group:after {
  clear: both;
}
.btn-group-vertical > .btn-group > .btn {
  float: none;
}
.btn-group-vertical > .btn + .btn,
.btn-group-vertical > .btn + .btn-group,
.btn-group-vertical > .btn-group + .btn,
.btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0;
}
.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group-vertical
  > .btn-group:first-child:not(:last-child)
  > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical
  > .btn-group:last-child:not(:first-child)
  > .btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.btn-group-justified > .btn,
.btn-group-justified > .btn-group {
  float: none;
  display: table-cell;
  width: 1%;
}
.btn-group-justified > .btn-group .btn {
  width: 100%;
}
.btn-group-justified > .btn-group .dropdown-menu {
  left: auto;
}
[data-toggle="buttons"] > .btn input[type="checkbox"],
[data-toggle="buttons"] > .btn input[type="radio"],
[data-toggle="buttons"] > .btn-group > .btn input[type="checkbox"],
[data-toggle="buttons"] > .btn-group > .btn input[type="radio"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.input-group[class*="col-"] {
  float: none;
  padding-left: 0;
  padding-right: 0;
}
.input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
}
.input-group .form-control,
.input-group-addon,
.input-group-btn {
  display: table-cell;
}
.input-group .form-control:not(:first-child):not(:last-child),
.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.input-group-addon,
.input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.input-group-addon {
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1;
  color: #555;
  text-align: center;
  background-color: #eee;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.input-group-addon.input-sm,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .input-group-addon.btn {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 3px;
}
.input-group-addon.input-lg,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .input-group-addon.btn {
  padding: 10px 16px;
  font-size: 18px;
  border-radius: 6px;
}
.input-group-addon input[type="checkbox"],
.input-group-addon input[type="radio"] {
  margin-top: 0;
}
.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group > .btn,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn-group:not(:last-child) > .btn,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.input-group-addon:first-child {
  border-right: 0;
}
.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:first-child > .btn-group:not(:first-child) > .btn,
.input-group-btn:first-child > .btn:not(:first-child),
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group > .btn,
.input-group-btn:last-child > .dropdown-toggle {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.input-group-addon:last-child {
  border-left: 0;
}
.input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap;
}
.input-group-btn > .btn {
  position: relative;
}
.input-group-btn > .btn + .btn {
  margin-left: -1px;
}
.input-group-btn > .btn:active,
.input-group-btn > .btn:focus,
.input-group-btn > .btn:hover {
  z-index: 2;
}
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group {
  margin-right: -1px;
}
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group {
  margin-left: -1px;
}
.nav {
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}
.nav:after,
.nav:before {
  content: " ";
  display: table;
}
.nav:after {
  clear: both;
}
.nav > li {
  position: relative;
  display: block;
}
.nav > li > a {
  position: relative;
  display: block;
  padding: 10px 15px;
}
.nav > li > a:focus,
.nav > li > a:hover {
  text-decoration: none;
  background-color: #eee;
}
.nav > li.disabled > a {
  color: #777;
}
.nav > li.disabled > a:focus,
.nav > li.disabled > a:hover {
  color: #777;
  text-decoration: none;
  background-color: transparent;
  cursor: not-allowed;
}
.nav .open > a,
.nav .open > a:focus,
.nav .open > a:hover {
  background-color: #eee;
  border-color: #337ab7;
}
.nav .nav-divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.nav > li > a > img {
  max-width: none;
}
.nav-tabs {
  border-bottom: 1px solid #ddd;
}
.nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.nav-tabs > li > a {
  margin-right: 2px;
  line-height: 1.428571429;
  border: 1px solid transparent;
  border-radius: 4px 4px 0 0;
}
.nav-tabs > li > a:hover {
  border-color: #eee #eee #ddd;
}
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:focus,
.nav-tabs > li.active > a:hover {
  color: #555;
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
  cursor: default;
}
.nav-pills > li {
  float: left;
}
.nav-pills > li > a {
  border-radius: 4px;
}
.nav-pills > li + li {
  margin-left: 2px;
}
.nav-pills > li.active > a,
.nav-pills > li.active > a:focus,
.nav-pills > li.active > a:hover {
  color: #fff;
  background-color: #337ab7;
}
.nav-stacked > li {
  float: none;
}
.nav-stacked > li + li {
  margin-top: 2px;
  margin-left: 0;
}
.nav-justified,
.nav-tabs.nav-justified {
  width: 100%;
}
.nav-justified > li,
.nav-tabs.nav-justified > li {
  float: none;
}
.nav-justified > li > a,
.nav-tabs.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .nav-justified > li,
  .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-justified > li > a,
  .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs-justified,
.nav-tabs.nav-justified {
  border-bottom: 0;
}
.nav-tabs-justified > li > a,
.nav-tabs.nav-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.nav-tabs-justified > .active > a,
.nav-tabs-justified > .active > a:focus,
.nav-tabs-justified > .active > a:hover,
.nav-tabs.nav-justified > .active > a,
.nav-tabs.nav-justified > .active > a:focus,
.nav-tabs.nav-justified > .active > a:hover {
  border: 1px solid #ddd;
}
@media (min-width: 768px) {
  .nav-tabs-justified > li > a,
  .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs-justified > .active > a,
  .nav-tabs-justified > .active > a:focus,
  .nav-tabs-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a,
  .nav-tabs.nav-justified > .active > a:focus,
  .nav-tabs.nav-justified > .active > a:hover {
    border-bottom-color: #fff;
  }
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.navbar {
  position: relative;
  min-height: 50px;
  margin-bottom: 20px;
  border: 1px solid transparent;
}
.navbar:after,
.navbar:before {
  content: " ";
  display: table;
}
.navbar:after {
  clear: both;
}
@media (min-width: 768px) {
  .navbar {
    border-radius: 4px;
  }
}
.navbar-header:after,
.navbar-header:before {
  content: " ";
  display: table;
}
.navbar-header:after {
  clear: both;
}
@media (min-width: 768px) {
  .navbar-header {
    float: left;
  }
}
.navbar-collapse {
  overflow-x: visible;
  padding-right: 15px;
  padding-left: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch;
}
.navbar-collapse:after,
.navbar-collapse:before {
  content: " ";
  display: table;
}
.navbar-collapse:after {
  clear: both;
}
.navbar-collapse.in {
  overflow-y: auto;
}
@media (min-width: 768px) {
  .navbar-collapse {
    width: auto;
    border-top: 0;
    box-shadow: none;
  }
  .navbar-collapse.collapse {
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important;
  }
  .navbar-collapse.in {
    overflow-y: visible;
  }
  .navbar-fixed-bottom .navbar-collapse,
  .navbar-fixed-top .navbar-collapse,
  .navbar-static-top .navbar-collapse {
    padding-left: 0;
    padding-right: 0;
  }
}
.navbar-fixed-bottom .navbar-collapse,
.navbar-fixed-top .navbar-collapse {
  max-height: 340px;
}
@media (max-device-width: 480px) and (orientation: landscape) {
  .navbar-fixed-bottom .navbar-collapse,
  .navbar-fixed-top .navbar-collapse {
    max-height: 200px;
  }
}
.container-fluid > .navbar-collapse,
.container-fluid > .navbar-header,
.container > .navbar-collapse,
.container > .navbar-header {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .container-fluid > .navbar-collapse,
  .container-fluid > .navbar-header,
  .container > .navbar-collapse,
  .container > .navbar-header {
    margin-right: 0;
    margin-left: 0;
  }
}
.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px;
}
@media (min-width: 768px) {
  .navbar-static-top {
    border-radius: 0;
  }
}
.navbar-fixed-bottom,
.navbar-fixed-top {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
}
@media (min-width: 768px) {
  .navbar-fixed-bottom,
  .navbar-fixed-top {
    border-radius: 0;
  }
}
.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px;
}
.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0;
}
.navbar-brand {
  float: left;
  padding: 15px 15px;
  font-size: 18px;
  line-height: 20px;
  height: 50px;
}
.navbar-brand:focus,
.navbar-brand:hover {
  text-decoration: none;
}
.navbar-brand > img {
  display: block;
}
@media (min-width: 768px) {
  .navbar > .container .navbar-brand,
  .navbar > .container-fluid .navbar-brand {
    margin-left: -15px;
  }
}
.navbar-toggle {
  position: relative;
  float: right;
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}
.navbar-toggle:focus {
  outline: 0;
}
.navbar-toggle .icon-bar {
  display: block;
  width: 22px;
  height: 2px;
  border-radius: 1px;
}
.navbar-toggle .icon-bar + .icon-bar {
  margin-top: 4px;
}
@media (min-width: 768px) {
  .navbar-toggle {
    display: none;
  }
}
.navbar-nav {
  margin: 7.5px -15px;
}
.navbar-nav > li > a {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 20px;
}
@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    box-shadow: none;
  }
  .navbar-nav .open .dropdown-menu .dropdown-header,
  .navbar-nav .open .dropdown-menu > li > a {
    padding: 5px 15px 5px 25px;
  }
  .navbar-nav .open .dropdown-menu > li > a {
    line-height: 20px;
  }
  .navbar-nav .open .dropdown-menu > li > a:focus,
  .navbar-nav .open .dropdown-menu > li > a:hover {
    background-image: none;
  }
}
@media (min-width: 768px) {
  .navbar-nav {
    float: left;
    margin: 0;
  }
  .navbar-nav > li {
    float: left;
  }
  .navbar-nav > li > a {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
.navbar-form {
  margin-left: -15px;
  margin-right: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 8px;
  margin-bottom: 8px;
}
@media (min-width: 768px) {
  .navbar-form .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .navbar-form .form-control-static {
    display: inline-block;
  }
  .navbar-form .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .navbar-form .input-group .form-control,
  .navbar-form .input-group .input-group-addon,
  .navbar-form .input-group .input-group-btn {
    width: auto;
  }
  .navbar-form .input-group > .form-control {
    width: 100%;
  }
  .navbar-form .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .checkbox,
  .navbar-form .radio {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .checkbox label,
  .navbar-form .radio label {
    padding-left: 0;
  }
  .navbar-form .checkbox input[type="checkbox"],
  .navbar-form .radio input[type="radio"] {
    position: relative;
    margin-left: 0;
  }
  .navbar-form .has-feedback .form-control-feedback {
    top: 0;
  }
}
@media (max-width: 767px) {
  .navbar-form .form-group {
    margin-bottom: 5px;
  }
  .navbar-form .form-group:last-child {
    margin-bottom: 0;
  }
}
@media (min-width: 768px) {
  .navbar-form {
    width: auto;
    border: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: 0;
    padding-bottom: 0;
    box-shadow: none;
  }
}
.navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  margin-bottom: 0;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.navbar-btn {
  margin-top: 8px;
  margin-bottom: 8px;
}
.btn-group-sm > .navbar-btn.btn,
.navbar-btn.btn-sm {
  margin-top: 10px;
  margin-bottom: 10px;
}
.btn-group-xs > .navbar-btn.btn,
.navbar-btn.btn-xs {
  margin-top: 14px;
  margin-bottom: 14px;
}
.navbar-text {
  margin-top: 15px;
  margin-bottom: 15px;
}
@media (min-width: 768px) {
  .navbar-text {
    float: left;
    margin-left: 15px;
    margin-right: 15px;
  }
}
@media (min-width: 768px) {
  .navbar-left {
    float: left !important;
  }
  .navbar-right {
    float: right !important;
    margin-right: -15px;
  }
  .navbar-right ~ .navbar-right {
    margin-right: 0;
  }
}
.navbar-default {
  background-color: #f8f8f8;
  border-color: #e7e7e7;
}
.navbar-default .navbar-brand {
  color: #777;
}
.navbar-default .navbar-brand:focus,
.navbar-default .navbar-brand:hover {
  color: #5e5e5e;
  background-color: transparent;
}
.navbar-default .navbar-text {
  color: #777;
}
.navbar-default .navbar-nav > li > a {
  color: #777;
}
.navbar-default .navbar-nav > li > a:focus,
.navbar-default .navbar-nav > li > a:hover {
  color: #333;
  background-color: transparent;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:focus,
.navbar-default .navbar-nav > .active > a:hover {
  color: #555;
  background-color: #e7e7e7;
}
.navbar-default .navbar-nav > .disabled > a,
.navbar-default .navbar-nav > .disabled > a:focus,
.navbar-default .navbar-nav > .disabled > a:hover {
  color: #ccc;
  background-color: transparent;
}
.navbar-default .navbar-toggle {
  border-color: #ddd;
}
.navbar-default .navbar-toggle:focus,
.navbar-default .navbar-toggle:hover {
  background-color: #ddd;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #888;
}
.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
  border-color: #e7e7e7;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:focus,
.navbar-default .navbar-nav > .open > a:hover {
  background-color: #e7e7e7;
  color: #555;
}
@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #777;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus,
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover {
    color: #333;
    background-color: transparent;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover {
    color: #555;
    background-color: #e7e7e7;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover {
    color: #ccc;
    background-color: transparent;
  }
}
.navbar-default .navbar-link {
  color: #777;
}
.navbar-default .navbar-link:hover {
  color: #333;
}
.navbar-default .btn-link {
  color: #777;
}
.navbar-default .btn-link:focus,
.navbar-default .btn-link:hover {
  color: #333;
}
.navbar-default .btn-link[disabled]:focus,
.navbar-default .btn-link[disabled]:hover,
fieldset[disabled] .navbar-default .btn-link:focus,
fieldset[disabled] .navbar-default .btn-link:hover {
  color: #ccc;
}
.navbar-inverse {
  background-color: #222;
  border-color: #090909;
}
.navbar-inverse .navbar-brand {
  color: #9d9d9d;
}
.navbar-inverse .navbar-brand:focus,
.navbar-inverse .navbar-brand:hover {
  color: #fff;
  background-color: transparent;
}
.navbar-inverse .navbar-text {
  color: #9d9d9d;
}
.navbar-inverse .navbar-nav > li > a {
  color: #9d9d9d;
}
.navbar-inverse .navbar-nav > li > a:focus,
.navbar-inverse .navbar-nav > li > a:hover {
  color: #fff;
  background-color: transparent;
}
.navbar-inverse .navbar-nav > .active > a,
.navbar-inverse .navbar-nav > .active > a:focus,
.navbar-inverse .navbar-nav > .active > a:hover {
  color: #fff;
  background-color: #090909;
}
.navbar-inverse .navbar-nav > .disabled > a,
.navbar-inverse .navbar-nav > .disabled > a:focus,
.navbar-inverse .navbar-nav > .disabled > a:hover {
  color: #444;
  background-color: transparent;
}
.navbar-inverse .navbar-toggle {
  border-color: #333;
}
.navbar-inverse .navbar-toggle:focus,
.navbar-inverse .navbar-toggle:hover {
  background-color: #333;
}
.navbar-inverse .navbar-toggle .icon-bar {
  background-color: #fff;
}
.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
  border-color: #101010;
}
.navbar-inverse .navbar-nav > .open > a,
.navbar-inverse .navbar-nav > .open > a:focus,
.navbar-inverse .navbar-nav > .open > a:hover {
  background-color: #090909;
  color: #fff;
}
@media (max-width: 767px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
    border-color: #090909;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    background-color: #090909;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #9d9d9d;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus,
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover {
    color: #fff;
    background-color: transparent;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover {
    color: #fff;
    background-color: #090909;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover {
    color: #444;
    background-color: transparent;
  }
}
.navbar-inverse .navbar-link {
  color: #9d9d9d;
}
.navbar-inverse .navbar-link:hover {
  color: #fff;
}
.navbar-inverse .btn-link {
  color: #9d9d9d;
}
.navbar-inverse .btn-link:focus,
.navbar-inverse .btn-link:hover {
  color: #fff;
}
.navbar-inverse .btn-link[disabled]:focus,
.navbar-inverse .btn-link[disabled]:hover,
fieldset[disabled] .navbar-inverse .btn-link:focus,
fieldset[disabled] .navbar-inverse .btn-link:hover {
  color: #444;
}
.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 20px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 4px;
}
.breadcrumb > li {
  display: inline-block;
}
.breadcrumb > li + li:before {
  content: "/ ";
  padding: 0 5px;
  color: #ccc;
}
.breadcrumb > .active {
  color: #777;
}
.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px;
}
.pagination > li {
  display: inline;
}
.pagination > li > a,
.pagination > li > span {
  position: relative;
  float: left;
  padding: 6px 12px;
  line-height: 1.428571429;
  text-decoration: none;
  color: #337ab7;
  background-color: #fff;
  border: 1px solid #ddd;
  margin-left: -1px;
}
.pagination > li:first-child > a,
.pagination > li:first-child > span {
  margin-left: 0;
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}
.pagination > li:last-child > a,
.pagination > li:last-child > span {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}
.pagination > li > a:focus,
.pagination > li > a:hover,
.pagination > li > span:focus,
.pagination > li > span:hover {
  color: #23527c;
  background-color: #eee;
  border-color: #ddd;
}
.pagination > .active > a,
.pagination > .active > a:focus,
.pagination > .active > a:hover,
.pagination > .active > span,
.pagination > .active > span:focus,
.pagination > .active > span:hover {
  z-index: 2;
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
  cursor: default;
}
.pagination > .disabled > a,
.pagination > .disabled > a:focus,
.pagination > .disabled > a:hover,
.pagination > .disabled > span,
.pagination > .disabled > span:focus,
.pagination > .disabled > span:hover {
  color: #777;
  background-color: #fff;
  border-color: #ddd;
  cursor: not-allowed;
}
.pagination-lg > li > a,
.pagination-lg > li > span {
  padding: 10px 16px;
  font-size: 18px;
}
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}
.pagination-sm > li > a,
.pagination-sm > li > span {
  padding: 5px 10px;
  font-size: 12px;
}
.pagination-sm > li:first-child > a,
.pagination-sm > li:first-child > span {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
.pagination-sm > li:last-child > a,
.pagination-sm > li:last-child > span {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
.pager {
  padding-left: 0;
  margin: 20px 0;
  list-style: none;
  text-align: center;
}
.pager:after,
.pager:before {
  content: " ";
  display: table;
}
.pager:after {
  clear: both;
}
.pager li {
  display: inline;
}
.pager li > a,
.pager li > span {
  display: inline-block;
  padding: 5px 14px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 15px;
}
.pager li > a:focus,
.pager li > a:hover {
  text-decoration: none;
  background-color: #eee;
}
.pager .next > a,
.pager .next > span {
  float: right;
}
.pager .previous > a,
.pager .previous > span {
  float: left;
}
.pager .disabled > a,
.pager .disabled > a:focus,
.pager .disabled > a:hover,
.pager .disabled > span {
  color: #777;
  background-color: #fff;
  cursor: not-allowed;
}
.label {
  display: inline;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
}
.label:empty {
  display: none;
}
.btn .label {
  position: relative;
  top: -1px;
}
a.label:focus,
a.label:hover {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.label-default {
  background-color: #777;
}
.label-default[href]:focus,
.label-default[href]:hover {
  background-color: #5e5e5e;
}
.label-primary {
  background-color: #337ab7;
}
.label-primary[href]:focus,
.label-primary[href]:hover {
  background-color: #286090;
}
.label-success {
  background-color: #5cb85c;
}
.label-success[href]:focus,
.label-success[href]:hover {
  background-color: #449d44;
}
.label-info {
  background-color: #5bc0de;
}
.label-info[href]:focus,
.label-info[href]:hover {
  background-color: #31b0d5;
}
.label-warning {
  background-color: #f0ad4e;
}
.label-warning[href]:focus,
.label-warning[href]:hover {
  background-color: #ec971f;
}
.label-danger {
  background-color: #d9534f;
}
.label-danger[href]:focus,
.label-danger[href]:hover {
  background-color: #c9302c;
}
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #777;
  border-radius: 10px;
}
.badge:empty {
  display: none;
}
.btn .badge {
  position: relative;
  top: -1px;
}
.btn-group-xs > .btn .badge,
.btn-xs .badge {
  top: 0;
  padding: 1px 5px;
}
.list-group-item.active > .badge,
.nav-pills > .active > a > .badge {
  color: #337ab7;
  background-color: #fff;
}
.list-group-item > .badge {
  float: right;
}
.list-group-item > .badge + .badge {
  margin-right: 5px;
}
.nav-pills > li > a > .badge {
  margin-left: 3px;
}
a.badge:focus,
a.badge:hover {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.jumbotron {
  padding: 30px 15px;
  margin-bottom: 30px;
  color: inherit;
  background-color: #eee;
}
.jumbotron .h1,
.jumbotron h1 {
  color: inherit;
}
.jumbotron p {
  margin-bottom: 15px;
  font-size: 21px;
  font-weight: 200;
}
.jumbotron > hr {
  border-top-color: #d5d5d5;
}
.container .jumbotron,
.container-fluid .jumbotron {
  border-radius: 6px;
}
.jumbotron .container {
  max-width: 100%;
}
@media screen and (min-width: 768px) {
  .jumbotron {
    padding: 48px 0;
  }
  .container .jumbotron,
  .container-fluid .jumbotron {
    padding-left: 60px;
    padding-right: 60px;
  }
  .jumbotron .h1,
  .jumbotron h1 {
    font-size: 63px;
  }
}
.thumbnail {
  display: block;
  padding: 4px;
  margin-bottom: 20px;
  line-height: 1.428571429;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: border 0.2s ease-in-out;
}
.thumbnail a > img,
.thumbnail > img {
  display: block;
  max-width: 100%;
  height: auto;
  margin-left: auto;
  margin-right: auto;
}
.thumbnail .caption {
  padding: 9px;
  color: #333;
}
a.thumbnail.active,
a.thumbnail:focus,
a.thumbnail:hover {
  border-color: #337ab7;
}
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}
.alert h4 {
  margin-top: 0;
  color: inherit;
}
.alert .alert-link {
  font-weight: 700;
}
.alert > p,
.alert > ul {
  margin-bottom: 0;
}
.alert > p + p {
  margin-top: 5px;
}
.alert-dismissable,
.alert-dismissible {
  padding-right: 35px;
}
.alert-dismissable .close,
.alert-dismissible .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit;
}
.alert-success {
  background-color: #dff0d8;
  border-color: #d6e9c6;
  color: #3c763d;
}
.alert-success hr {
  border-top-color: #c9e2b3;
}
.alert-success .alert-link {
  color: #2b542c;
}
.alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #31708f;
}
.alert-info hr {
  border-top-color: #a6e1ec;
}
.alert-info .alert-link {
  color: #245269;
}
.alert-warning {
  background-color: #fcf8e3;
  border-color: #faebcc;
  color: #8a6d3b;
}
.alert-warning hr {
  border-top-color: #f7e1b5;
}
.alert-warning .alert-link {
  color: #66512c;
}
.alert-danger {
  background-color: #f2dede;
  border-color: #ebccd1;
  color: #a94442;
}
.alert-danger hr {
  border-top-color: #e4b9c0;
}
.alert-danger .alert-link {
  color: #843534;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
.progress {
  overflow: hidden;
  height: 20px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  background-color: #337ab7;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  transition: width 0.6s ease;
}
.progress-bar-striped,
.progress-striped .progress-bar {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 40px 40px;
}
.progress-bar.active,
.progress.active .progress-bar {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
}
.progress-bar-success {
  background-color: #5cb85c;
}
.progress-striped .progress-bar-success {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
}
.progress-bar-info {
  background-color: #5bc0de;
}
.progress-striped .progress-bar-info {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
}
.progress-bar-warning {
  background-color: #f0ad4e;
}
.progress-striped .progress-bar-warning {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
}
.progress-bar-danger {
  background-color: #d9534f;
}
.progress-striped .progress-bar-danger {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
}
.media {
  margin-top: 15px;
}
.media:first-child {
  margin-top: 0;
}
.media,
.media-body {
  zoom: 1;
  overflow: hidden;
}
.media-body {
  width: 10000px;
}
.media-object {
  display: block;
}
.media-right,
.media > .pull-right {
  padding-left: 10px;
}
.media-left,
.media > .pull-left {
  padding-right: 10px;
}
.media-body,
.media-left,
.media-right {
  display: table-cell;
  vertical-align: top;
}
.media-middle {
  vertical-align: middle;
}
.media-bottom {
  vertical-align: bottom;
}
.media-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.media-list {
  padding-left: 0;
  list-style: none;
}
.list-group {
  margin-bottom: 20px;
  padding-left: 0;
}
.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd;
}
.list-group-item:first-child {
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}
.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
a.list-group-item {
  color: #555;
}
a.list-group-item .list-group-item-heading {
  color: #333;
}
a.list-group-item:focus,
a.list-group-item:hover {
  text-decoration: none;
  color: #555;
  background-color: #f5f5f5;
}
.list-group-item.disabled,
.list-group-item.disabled:focus,
.list-group-item.disabled:hover {
  background-color: #eee;
  color: #777;
  cursor: not-allowed;
}
.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading {
  color: inherit;
}
.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text {
  color: #777;
}
.list-group-item.active,
.list-group-item.active:focus,
.list-group-item.active:hover {
  z-index: 2;
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.list-group-item.active .list-group-item-heading,
.list-group-item.active .list-group-item-heading > .small,
.list-group-item.active .list-group-item-heading > small,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading > .small,
.list-group-item.active:focus .list-group-item-heading > small,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading > .small,
.list-group-item.active:hover .list-group-item-heading > small {
  color: inherit;
}
.list-group-item.active .list-group-item-text,
.list-group-item.active:focus .list-group-item-text,
.list-group-item.active:hover .list-group-item-text {
  color: #c7ddef;
}
.list-group-item-success {
  color: #3c763d;
  background-color: #dff0d8;
}
a.list-group-item-success {
  color: #3c763d;
}
a.list-group-item-success .list-group-item-heading {
  color: inherit;
}
a.list-group-item-success:focus,
a.list-group-item-success:hover {
  color: #3c763d;
  background-color: #d0e9c6;
}
a.list-group-item-success.active,
a.list-group-item-success.active:focus,
a.list-group-item-success.active:hover {
  color: #fff;
  background-color: #3c763d;
  border-color: #3c763d;
}
.list-group-item-info {
  color: #31708f;
  background-color: #d9edf7;
}
a.list-group-item-info {
  color: #31708f;
}
a.list-group-item-info .list-group-item-heading {
  color: inherit;
}
a.list-group-item-info:focus,
a.list-group-item-info:hover {
  color: #31708f;
  background-color: #c4e3f3;
}
a.list-group-item-info.active,
a.list-group-item-info.active:focus,
a.list-group-item-info.active:hover {
  color: #fff;
  background-color: #31708f;
  border-color: #31708f;
}
.list-group-item-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
}
a.list-group-item-warning {
  color: #8a6d3b;
}
a.list-group-item-warning .list-group-item-heading {
  color: inherit;
}
a.list-group-item-warning:focus,
a.list-group-item-warning:hover {
  color: #8a6d3b;
  background-color: #faf2cc;
}
a.list-group-item-warning.active,
a.list-group-item-warning.active:focus,
a.list-group-item-warning.active:hover {
  color: #fff;
  background-color: #8a6d3b;
  border-color: #8a6d3b;
}
.list-group-item-danger {
  color: #a94442;
  background-color: #f2dede;
}
a.list-group-item-danger {
  color: #a94442;
}
a.list-group-item-danger .list-group-item-heading {
  color: inherit;
}
a.list-group-item-danger:focus,
a.list-group-item-danger:hover {
  color: #a94442;
  background-color: #ebcccc;
}
a.list-group-item-danger.active,
a.list-group-item-danger.active:focus,
a.list-group-item-danger.active:hover {
  color: #fff;
  background-color: #a94442;
  border-color: #a94442;
}
.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}
.panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.panel-body {
  padding: 15px;
}
.panel-body:after,
.panel-body:before {
  content: " ";
  display: table;
}
.panel-body:after {
  clear: both;
}
.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel-heading > .dropdown .dropdown-toggle {
  color: inherit;
}
.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 16px;
  color: inherit;
}
.panel-title > .small,
.panel-title > .small > a,
.panel-title > a,
.panel-title > small,
.panel-title > small > a {
  color: inherit;
}
.panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .list-group,
.panel > .panel-collapse > .list-group {
  margin-bottom: 0;
}
.panel > .list-group .list-group-item,
.panel > .panel-collapse > .list-group .list-group-item {
  border-width: 1px 0;
  border-radius: 0;
}
.panel > .list-group:first-child .list-group-item:first-child,
.panel
  > .panel-collapse
  > .list-group:first-child
  .list-group-item:first-child {
  border-top: 0;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel > .list-group:last-child .list-group-item:last-child,
.panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {
  border-bottom: 0;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0;
}
.list-group + .panel-footer {
  border-top-width: 0;
}
.panel > .panel-collapse > .table,
.panel > .table,
.panel > .table-responsive > .table {
  margin-bottom: 0;
}
.panel > .panel-collapse > .table caption,
.panel > .table caption,
.panel > .table-responsive > .table caption {
  padding-left: 15px;
  padding-right: 15px;
}
.panel > .table-responsive:first-child > .table:first-child,
.panel > .table:first-child {
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel
  > .table-responsive:first-child
  > .table:first-child
  > tbody:first-child
  > tr:first-child,
.panel
  > .table-responsive:first-child
  > .table:first-child
  > thead:first-child
  > tr:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child,
.panel > .table:first-child > thead:first-child > tr:first-child {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.panel
  > .table-responsive:first-child
  > .table:first-child
  > tbody:first-child
  > tr:first-child
  td:first-child,
.panel
  > .table-responsive:first-child
  > .table:first-child
  > tbody:first-child
  > tr:first-child
  th:first-child,
.panel
  > .table-responsive:first-child
  > .table:first-child
  > thead:first-child
  > tr:first-child
  td:first-child,
.panel
  > .table-responsive:first-child
  > .table:first-child
  > thead:first-child
  > tr:first-child
  th:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel
  > .table:first-child
  > thead:first-child
  > tr:first-child
  th:first-child {
  border-top-left-radius: 3px;
}
.panel
  > .table-responsive:first-child
  > .table:first-child
  > tbody:first-child
  > tr:first-child
  td:last-child,
.panel
  > .table-responsive:first-child
  > .table:first-child
  > tbody:first-child
  > tr:first-child
  th:last-child,
.panel
  > .table-responsive:first-child
  > .table:first-child
  > thead:first-child
  > tr:first-child
  td:last-child,
.panel
  > .table-responsive:first-child
  > .table:first-child
  > thead:first-child
  > tr:first-child
  th:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:last-child {
  border-top-right-radius: 3px;
}
.panel > .table-responsive:last-child > .table:last-child,
.panel > .table:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tbody:last-child
  > tr:last-child,
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tfoot:last-child
  > tr:last-child,
.panel > .table:last-child > tbody:last-child > tr:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tbody:last-child
  > tr:last-child
  td:first-child,
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tbody:last-child
  > tr:last-child
  th:first-child,
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tfoot:last-child
  > tr:last-child
  td:first-child,
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tfoot:last-child
  > tr:last-child
  th:first-child,
.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
  border-bottom-left-radius: 3px;
}
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tbody:last-child
  > tr:last-child
  td:last-child,
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tbody:last-child
  > tr:last-child
  th:last-child,
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tfoot:last-child
  > tr:last-child
  td:last-child,
.panel
  > .table-responsive:last-child
  > .table:last-child
  > tfoot:last-child
  > tr:last-child
  th:last-child,
.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
  border-bottom-right-radius: 3px;
}
.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive,
.panel > .table + .panel-body,
.panel > .table-responsive + .panel-body {
  border-top: 1px solid #ddd;
}
.panel > .table > tbody:first-child > tr:first-child td,
.panel > .table > tbody:first-child > tr:first-child th {
  border-top: 0;
}
.panel > .table-bordered,
.panel > .table-responsive > .table-bordered {
  border: 0;
}
.panel > .table-bordered > tbody > tr > td:first-child,
.panel > .table-bordered > tbody > tr > th:first-child,
.panel > .table-bordered > tfoot > tr > td:first-child,
.panel > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-bordered > thead > tr > td:first-child,
.panel > .table-bordered > thead > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:first-child {
  border-left: 0;
}
.panel > .table-bordered > tbody > tr > td:last-child,
.panel > .table-bordered > tbody > tr > th:last-child,
.panel > .table-bordered > tfoot > tr > td:last-child,
.panel > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-bordered > thead > tr > td:last-child,
.panel > .table-bordered > thead > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:last-child {
  border-right: 0;
}
.panel > .table-bordered > tbody > tr:first-child > td,
.panel > .table-bordered > tbody > tr:first-child > th,
.panel > .table-bordered > thead > tr:first-child > td,
.panel > .table-bordered > thead > tr:first-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > th {
  border-bottom: 0;
}
.panel > .table-bordered > tbody > tr:last-child > td,
.panel > .table-bordered > tbody > tr:last-child > th,
.panel > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
  border-bottom: 0;
}
.panel > .table-responsive {
  border: 0;
  margin-bottom: 0;
}
.panel-group {
  margin-bottom: 20px;
}
.panel-group .panel {
  margin-bottom: 0;
  border-radius: 4px;
}
.panel-group .panel + .panel {
  margin-top: 5px;
}
.panel-group .panel-heading {
  border-bottom: 0;
}
.panel-group .panel-heading + .panel-collapse > .list-group,
.panel-group .panel-heading + .panel-collapse > .panel-body {
  border-top: 1px solid #ddd;
}
.panel-group .panel-footer {
  border-top: 0;
}
.panel-group .panel-footer + .panel-collapse .panel-body {
  border-bottom: 1px solid #ddd;
}
.panel-default {
  border-color: #ddd;
}
.panel-default > .panel-heading {
  color: #333;
  background-color: #f5f5f5;
  border-color: #ddd;
}
.panel-default > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #ddd;
}
.panel-default > .panel-heading .badge {
  color: #f5f5f5;
  background-color: #333;
}
.panel-default > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #ddd;
}
.panel-primary {
  border-color: #337ab7;
}
.panel-primary > .panel-heading {
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.panel-primary > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #337ab7;
}
.panel-primary > .panel-heading .badge {
  color: #337ab7;
  background-color: #fff;
}
.panel-primary > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #337ab7;
}
.panel-success {
  border-color: #d6e9c6;
}
.panel-success > .panel-heading {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}
.panel-success > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #d6e9c6;
}
.panel-success > .panel-heading .badge {
  color: #dff0d8;
  background-color: #3c763d;
}
.panel-success > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #d6e9c6;
}
.panel-info {
  border-color: #bce8f1;
}
.panel-info > .panel-heading {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.panel-info > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #bce8f1;
}
.panel-info > .panel-heading .badge {
  color: #d9edf7;
  background-color: #31708f;
}
.panel-info > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #bce8f1;
}
.panel-warning {
  border-color: #faebcc;
}
.panel-warning > .panel-heading {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.panel-warning > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #faebcc;
}
.panel-warning > .panel-heading .badge {
  color: #fcf8e3;
  background-color: #8a6d3b;
}
.panel-warning > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #faebcc;
}
.panel-danger {
  border-color: #ebccd1;
}
.panel-danger > .panel-heading {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}
.panel-danger > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #ebccd1;
}
.panel-danger > .panel-heading .badge {
  color: #f2dede;
  background-color: #a94442;
}
.panel-danger > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #ebccd1;
}
.embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden;
}
.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  border: 0;
}
.embed-responsive-16by9 {
  padding-bottom: 56.25%;
}
.embed-responsive-4by3 {
  padding-bottom: 75%;
}
.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}
.well blockquote {
  border-color: #ddd;
  border-color: rgba(0, 0, 0, 0.15);
}
.well-lg {
  padding: 24px;
  border-radius: 6px;
}
.well-sm {
  padding: 9px;
  border-radius: 3px;
}
.close {
  float: right;
  font-size: 21px;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.2;
  filter: alpha(opacity=20);
}
.close:focus,
.close:hover {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
button.close {
  padding: 0;
  cursor: pointer;
  background: 0 0;
  border: 0;
  -webkit-appearance: none;
}
.modal-open {
  overflow: hidden;
}
.modal {
  display: none;
  overflow: hidden;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}
.modal.fade .modal-dialog {
  -webkit-transform: translate(0, -25%);
  -ms-transform: translate(0, -25%);
  transform: translate(0, -25%);
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
}
.modal.in .modal-dialog {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px;
}
.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid #999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: 0;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000;
}
.modal-backdrop.fade {
  opacity: 0;
  filter: alpha(opacity=0);
}
.modal-backdrop.in {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  min-height: 16.43px;
}
.modal-header .close {
  margin-top: -2px;
}
.modal-title {
  margin: 0;
  line-height: 1.428571429;
}
.modal-body {
  position: relative;
  padding: 15px;
}
.modal-footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
}
.modal-footer:after,
.modal-footer:before {
  content: " ";
  display: table;
}
.modal-footer:after {
  clear: both;
}
.modal-footer .btn + .btn {
  margin-left: 5px;
  margin-bottom: 0;
}
.modal-footer .btn-group .btn + .btn {
  margin-left: -1px;
}
.modal-footer .btn-block + .btn-block {
  margin-left: 0;
}
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto;
  }
  .modal-content {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
  .modal-sm {
    width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.4;
  opacity: 0;
  filter: alpha(opacity=0);
}
.tooltip.in {
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.tooltip.top {
  margin-top: -3px;
  padding: 5px 0;
}
.tooltip.right {
  margin-left: 3px;
  padding: 0 5px;
}
.tooltip.bottom {
  margin-top: 3px;
  padding: 5px 0;
}
.tooltip.left {
  margin-left: -3px;
  padding: 0 5px;
}
.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #fff;
  text-align: center;
  text-decoration: none;
  background-color: #000;
  border-radius: 4px;
}
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  right: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  left: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000;
}
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000;
}
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  right: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  left: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: none;
  max-width: 276px;
  padding: 1px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.428571429;
  text-align: left;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  white-space: normal;
}
.popover.top {
  margin-top: -10px;
}
.popover.right {
  margin-left: 10px;
}
.popover.bottom {
  margin-top: 10px;
}
.popover.left {
  margin-left: -10px;
}
.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0;
}
.popover-content {
  padding: 9px 14px;
}
.popover > .arrow,
.popover > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover > .arrow {
  border-width: 11px;
}
.popover > .arrow:after {
  border-width: 10px;
  content: "";
}
.popover.top > .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px;
}
.popover.top > .arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-bottom-width: 0;
  border-top-color: #fff;
}
.popover.right > .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.popover.right > .arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #fff;
}
.popover.bottom > .arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px;
}
.popover.bottom > .arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-top-width: 0;
  border-bottom-color: #fff;
}
.popover.left > .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.popover.left > .arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #fff;
  bottom: -10px;
}
.carousel {
  position: relative;
}
.carousel-inner {
  position: relative;
  overflow: hidden;
  width: 100%;
}
.carousel-inner > .item {
  display: none;
  position: relative;
  transition: 0.6s ease-in-out left;
}
.carousel-inner > .item > a > img,
.carousel-inner > .item > img {
  display: block;
  max-width: 100%;
  height: auto;
  line-height: 1;
}
@media all and (transform-3d), (-webkit-transform-3d) {
  .carousel-inner > .item {
    transition: -webkit-transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    perspective: 1000;
  }
  .carousel-inner > .item.active.right,
  .carousel-inner > .item.next {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    left: 0;
  }
  .carousel-inner > .item.active.left,
  .carousel-inner > .item.prev {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    left: 0;
  }
  .carousel-inner > .item.active,
  .carousel-inner > .item.next.left,
  .carousel-inner > .item.prev.right {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    left: 0;
  }
}
.carousel-inner > .active,
.carousel-inner > .next,
.carousel-inner > .prev {
  display: block;
}
.carousel-inner > .active {
  left: 0;
}
.carousel-inner > .next,
.carousel-inner > .prev {
  position: absolute;
  top: 0;
  width: 100%;
}
.carousel-inner > .next {
  left: 100%;
}
.carousel-inner > .prev {
  left: -100%;
}
.carousel-inner > .next.left,
.carousel-inner > .prev.right {
  left: 0;
}
.carousel-inner > .active.left {
  left: -100%;
}
.carousel-inner > .active.right {
  left: 100%;
}
.carousel-control {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 15%;
  opacity: 0.5;
  filter: alpha(opacity=50);
  font-size: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-control.left {
  background-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.5) 0,
    rgba(0, 0, 0, 0.0001) 100%
  );
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
}
.carousel-control.right {
  left: auto;
  right: 0;
  background-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.0001) 0,
    rgba(0, 0, 0, 0.5) 100%
  );
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
}
.carousel-control:focus,
.carousel-control:hover {
  outline: 0;
  color: #fff;
  text-decoration: none;
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.carousel-control .glyphicon-chevron-left,
.carousel-control .glyphicon-chevron-right,
.carousel-control .icon-next,
.carousel-control .icon-prev {
  position: absolute;
  top: 50%;
  z-index: 5;
  display: inline-block;
}
.carousel-control .glyphicon-chevron-left,
.carousel-control .icon-prev {
  left: 50%;
  margin-left: -10px;
}
.carousel-control .glyphicon-chevron-right,
.carousel-control .icon-next {
  right: 50%;
  margin-right: -10px;
}
.carousel-control .icon-next,
.carousel-control .icon-prev {
  width: 20px;
  height: 20px;
  margin-top: -10px;
  line-height: 1;
  font-family: serif;
}
.carousel-control .icon-prev:before {
  content: "\2039";
}
.carousel-control .icon-next:before {
  content: "\203a";
}
.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  margin-left: -30%;
  padding-left: 0;
  list-style: none;
  text-align: center;
}
.carousel-indicators li {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 1px;
  text-indent: -999px;
  border: 1px solid #fff;
  border-radius: 10px;
  cursor: pointer;
  background-color: #0009;
  background-color: transparent;
}
.carousel-indicators .active {
  margin: 0;
  width: 12px;
  height: 12px;
  background-color: #fff;
}
.carousel-caption {
  position: absolute;
  left: 15%;
  right: 15%;
  bottom: 20px;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-caption .btn {
  text-shadow: none;
}
@media screen and (min-width: 768px) {
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-next,
  .carousel-control .icon-prev {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    font-size: 30px;
  }
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .icon-prev {
    margin-left: -15px;
  }
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-next {
    margin-right: -15px;
  }
  .carousel-caption {
    left: 20%;
    right: 20%;
    padding-bottom: 30px;
  }
  .carousel-indicators {
    bottom: 20px;
  }
}
.clearfix:after,
.clearfix:before {
  content: " ";
  display: table;
}
.clearfix:after {
  clear: both;
}
.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.pull-right {
  float: right !important;
}
.pull-left {
  float: left !important;
}
.hide {
  display: none !important;
}
.show {
  display: block !important;
}
.invisible {
  visibility: hidden;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.hidden {
  display: none !important;
}
.affix {
  position: fixed;
}
@-ms-viewport {
  width: device-width;
}
.visible-xs {
  display: none !important;
}
.visible-sm {
  display: none !important;
}
.visible-md {
  display: none !important;
}
.visible-lg {
  display: none !important;
}
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block {
  display: none !important;
}
@media (max-width: 767px) {
  .visible-xs {
    display: block !important;
  }
  table.visible-xs {
    display: table;
  }
  tr.visible-xs {
    display: table-row !important;
  }
  td.visible-xs,
  th.visible-xs {
    display: table-cell !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-block {
    display: block !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-inline {
    display: inline !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: block !important;
  }
  table.visible-sm {
    display: table;
  }
  tr.visible-sm {
    display: table-row !important;
  }
  td.visible-sm,
  th.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-block {
    display: block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline {
    display: inline !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: block !important;
  }
  table.visible-md {
    display: table;
  }
  tr.visible-md {
    display: table-row !important;
  }
  td.visible-md,
  th.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-block {
    display: block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline {
    display: inline !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg {
    display: block !important;
  }
  table.visible-lg {
    display: table;
  }
  tr.visible-lg {
    display: table-row !important;
  }
  td.visible-lg,
  th.visible-lg {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-block {
    display: block !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-inline {
    display: inline !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-inline-block {
    display: inline-block !important;
  }
}
@media (max-width: 767px) {
  .hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}
.visible-print {
  display: none !important;
}
@media print {
  .visible-print {
    display: block !important;
  }
  table.visible-print {
    display: table;
  }
  tr.visible-print {
    display: table-row !important;
  }
  td.visible-print,
  th.visible-print {
    display: table-cell !important;
  }
}
.visible-print-block {
  display: none !important;
}
@media print {
  .visible-print-block {
    display: block !important;
  }
}
.visible-print-inline {
  display: none !important;
}
@media print {
  .visible-print-inline {
    display: inline !important;
  }
}
.visible-print-inline-block {
  display: none !important;
}
@media print {
  .visible-print-inline-block {
    display: inline-block !important;
  }
}
@media print {
  .hidden-print {
    display: none !important;
  }
}
a,
abbr,
acronym,
address,
applet,
article,
aside,
audio,
b,
big,
blockquote,
body,
canvas,
caption,
center,
cite,
code,
dd,
del,
details,
dfn,
div,
dl,
dt,
em,
embed,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
html,
i,
iframe,
img,
ins,
kbd,
label,
legend,
li,
mark,
menu,
nav,
object,
ol,
output,
p,
pre,
q,
ruby,
s,
samp,
section,
small,
span,
strike,
strong,
sub,
summary,
sup,
table,
tbody,
td,
tfoot,
th,
thead,
time,
tr,
tt,
u,
ul,
var,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
}
ul {
  list-style: none;
}
blockquote,
q {
  quotes: none;
}
blockquote:after,
blockquote:before,
q:after,
q:before {
  content: "";
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
a {
  text-decoration: none;
  color: inherit;
  -webkit-tap-highlight-color: transparent;
}
body,
html {
  height: 100%;
  -webkit-tap-highlight-color: transparent;
}
*,
:after,
:before {
  box-sizing: border-box;
}
:focus {
  outline-color: transparent;
  outline-style: none;
}
#map .amb-popup .popup__meta .button,
#map .amb-popup .popup__meta h2,
#map .amb-popup .popup__meta h3,
.about-us-awards,
.about-us-founders,
.about-us-founders h4,
.about-us-founders h5,
.about-us-info-block,
.about-us-info-block h3,
.about-us-management,
.about-us-management h4,
.about-us-management h5,
.about-us-press-mentions,
.about-us-press-mentions h3,
.about-us-press-mentions p,
.ajax-load-more-wrap.blue button.alm-load-more-btn,
.amb-full-about,
.amb-profile-soc .follow-blog a,
.amb-profile-soc ul li p,
.amb-related-articles .articles .item .date,
.amb-related-articles .articles .item a,
.ambassadors-filter .selected-item span,
.ambassadors-filter li span,
.ambassadors-list .become a,
.ambassadors-list .nothing-found,
.ambassadors-list .nothing-found span,
.ambassadors-list .user .name a,
.ambassadors-list .user .name a span,
.ambassadors-top-holder .ambassadors-top-search-block form input,
.ambassadors-top-holder p,
.ambassadors-top-holder.profile-holder .amb-profile-meta .country,
.ambassadors-top-holder.profile-holder .amb-profile-meta .doing,
.asr-container .more-res,
.asr-container .result-section .post-details .post-date,
.asr-container .result-section .post-details .post-title a,
.asr-container.no-result,
.asr-container.no-result .result-section span,
.base-font,
.become-an-ambassador h2,
.become-an-ambassador p,
.bold-font,
.btn.btn-get-started,
.btn.btn-transparent,
.categories-switch select,
.container.ebooks .ebook a,
.container.form-container p strong,
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_description,
.container.page-with-sidebar-container article.post .post-content form label,
.container.page-with-sidebar-container article.post .post-content h1,
.container.page-with-sidebar-container article.post .post-content h2,
.container.page-with-sidebar-container article.post .post-content h3,
.container.page-with-sidebar-container article.post .post-content h4,
.container.single article.post .post-content .date h6,
.container.single article.post .post-content .date p,
.container.single article.post .post-content .one-half,
.container.single article.post .post-content .p1,
.container.single article.post .post-content a,
.container.single article.post .post-content h1,
.container.single article.post .post-content h2,
.container.single article.post .post-content h3,
.container.single article.post .post-content ol li,
.container.single article.post .post-content p,
.container.single article.post .post-content p.wp-caption-text,
.container.single article.post .post-content strong,
.container.single article.post .post-content ul li,
.container.single-ebooks article.post .post-content form label,
.container.success-container p strong,
.container.thank-you-bottom-nav .blog a,
.container.thank-you-bottom-nav .customers a,
.container.thank-you-bottom-nav .ebooks a,
.container.thank-you-bottom-nav .features a,
.ebooks-top-holder p,
.ebooks-top-holder.single-top-holder h1,
.extra-bold-font,
.featured-top-holder p,
.floating-bottom-block .subscribe-form .submitted-message,
.floating-bottom-block .subscribe-form .subscribe-popup h3,
.floating-bottom-block a.title,
.floating-top-block h6,
.floating-top-block p,
.intro-ambassadors .amb .name,
.intro-ambassadors .amb .name span,
.intro-ambassadors h2,
.light-font,
.light-italic-font,
.link.link-tag,
.medium-font,
.medium-italic-font,
.more-tutorials a,
.more-tutorials h2,
.no-results h2,
.no-results p,
.page-id-208 form.hs-form input,
.page-id-208 form.hs-form label,
.page-id-208 form.hs-form legend,
.page-id-208 form.hs-form select,
.page-id-208 form.hs-form textarea,
.page-id-9524 form.hs-form input,
.page-id-9524 form.hs-form label,
.page-id-9524 form.hs-form legend,
.page-id-9524 form.hs-form select,
.page-id-9524 form.hs-form textarea,
.page-id-9641 form.hs-form input,
.page-id-9641 form.hs-form label,
.page-id-9641 form.hs-form legend,
.page-id-9641 form.hs-form select,
.page-id-9641 form.hs-form textarea,
.page-top-header .page-image .place h3,
.post-2 span.title,
.regular-font,
.regular-italic-font,
.search-block input.auto-suggest-front,
.share-block .follow-blog a,
.share-block ul li p,
.subscribe-form form.hs-form input,
.subscribe-form form.hs-form label,
.subscribe-form form.hs-form legend,
.subscribe-form form.hs-form select,
.subscribe-form form.hs-form textarea,
.subscribe-form-relative .form-holder .submitted-message,
.subscribe-form-relative form.hs-form input,
.subscribe-form-relative form.hs-form label,
.subscribe-form-relative form.hs-form legend,
.subscribe-form-relative form.hs-form select,
.subscribe-form-relative form.hs-form textarea,
.subscribe-form-relative h3,
.subscribe-form-relative p,
.thin-font,
.top-tutorials-video .title a,
.top-tutorials-video .title h2,
.tutorials-bot .num,
.tutorials-bot [class*="col-"] p,
.tutorials-bot.search-bot span,
.tutorials-list .chapter-child a,
.tutorials-list .chapter-title,
.tutorials-search-results a,
.tutorials-top-articles a,
.tutorials-top-holder .tutorials-top-search-block form input,
.tutorials-top-holder p,
a.alm-load-more-btn,
article.featured .date,
article.featured .excerpt,
article.featured .read-more,
article.featured h2,
article.post .post-content h2,
article.post footer.post-meta .date,
body,
footer.main-footer .contacts ul li .phone,
footer.main-footer .contacts ul li a,
footer.main-footer .copyright p,
footer.main-footer .footer-nav .sec_header,
footer.main-footer .footer-nav a,
h1,
h2,
h3,
header.search-header form input,
html,
nav.side-navigation {
  font-family: Roboto, sans-serif;
}
.about-us-info-block h3,
.become-an-ambassador h2,
.floating-bottom-block a.title,
.no-results h2,
.thin-font {
  font-weight: 100;
}
#map .amb-popup .popup__meta h3,
.about-us-founders h4,
.about-us-management h4,
.amb-related-articles .articles .item .date,
.ambassadors-list .user .name a span,
.asr-container .result-section .post-details .post-date,
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_description,
.container.single article.post .post-content .date h6,
.container.single article.post .post-content .date p,
.container.single article.post .post-content p.wp-caption-text,
.intro-ambassadors h2,
.light-font,
.light-italic-font,
.post-2 span.title,
.subscribe-form-relative h3,
body,
h1,
h2,
html {
  font-weight: 300;
}
.amb-related-articles .articles .item .date,
.asr-container .result-section .post-details .post-date,
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_description,
.container.single article.post .post-content .date h6,
.container.single article.post .post-content .date p,
.container.single article.post .post-content p.wp-caption-text,
.light-italic-font {
  font-style: italic;
}
#map .amb-popup .popup__meta h2,
.about-us-founders h5,
.about-us-management h5,
.about-us-press-mentions h3,
.amb-full-about,
.ambassadors-list .nothing-found span,
.ambassadors-top-holder.profile-holder .amb-profile-meta .country,
.asr-container.no-result .result-section span,
.become-an-ambassador p,
.container.single article.post .post-content .one-half,
.container.single article.post .post-content a,
.container.single article.post .post-content ol li,
.container.single article.post .post-content p,
.container.single article.post .post-content ul li,
.ebooks-top-holder.single-top-holder h1,
.floating-bottom-block .subscribe-form .submitted-message,
.floating-bottom-block .subscribe-form .subscribe-popup h3,
.intro-ambassadors .amb .name span,
.link.link-tag,
.regular-font,
.regular-italic-font,
.search-block input.auto-suggest-front,
.subscribe-form-relative .form-holder .submitted-message,
.subscribe-form-relative p,
.top-tutorials-video .title a,
.top-tutorials-video .title h2,
.tutorials-bot .num,
.tutorials-list .chapter-title,
.tutorials-search-results a,
.tutorials-top-articles a,
article.featured .date,
article.featured .excerpt,
article.post .post-content h2,
article.post footer.post-meta .date {
  font-weight: 400;
}
.about-us-founders h5,
.about-us-management h5,
.regular-italic-font,
article.featured .date,
article.post footer.post-meta .date {
  font-weight: 400;
  font-style: italic;
}
#map .amb-popup .popup__meta .button,
.about-us-awards,
.about-us-founders,
.about-us-info-block,
.about-us-management,
.about-us-press-mentions,
.about-us-press-mentions p,
.ajax-load-more-wrap.blue button.alm-load-more-btn,
.amb-profile-soc .follow-blog a,
.amb-profile-soc ul li p,
.amb-related-articles .articles .item a,
.ambassadors-filter .selected-item span,
.ambassadors-filter li span,
.ambassadors-list .become a,
.ambassadors-list .nothing-found,
.ambassadors-list .user .name a,
.ambassadors-top-holder .ambassadors-top-search-block form input,
.ambassadors-top-holder p,
.ambassadors-top-holder.profile-holder .amb-profile-meta .doing,
.asr-container .more-res,
.asr-container .result-section .post-details .post-title a,
.asr-container.no-result,
.btn.btn-get-started,
.btn.btn-transparent,
.categories-switch select,
.container.ebooks .ebook a,
.container.form-container p strong,
.container.page-with-sidebar-container article.post .post-content form label,
.container.page-with-sidebar-container article.post .post-content h1,
.container.page-with-sidebar-container article.post .post-content h2,
.container.page-with-sidebar-container article.post .post-content h3,
.container.page-with-sidebar-container article.post .post-content h4,
.container.single article.post .post-content .p1,
.container.single article.post .post-content h1,
.container.single article.post .post-content h2,
.container.single article.post .post-content h3,
.container.single article.post .post-content strong,
.container.single-ebooks article.post .post-content form label,
.container.success-container p strong,
.container.thank-you-bottom-nav .blog a,
.container.thank-you-bottom-nav .customers a,
.container.thank-you-bottom-nav .ebooks a,
.container.thank-you-bottom-nav .features a,
.ebooks-top-holder p,
.featured-top-holder p,
.floating-top-block h6,
.floating-top-block p,
.intro-ambassadors .amb .name,
.medium-font,
.medium-italic-font,
.more-tutorials a,
.more-tutorials h2,
.no-results p,
.page-id-208 form.hs-form input,
.page-id-208 form.hs-form label,
.page-id-208 form.hs-form legend,
.page-id-208 form.hs-form select,
.page-id-208 form.hs-form textarea,
.page-id-9524 form.hs-form input,
.page-id-9524 form.hs-form label,
.page-id-9524 form.hs-form legend,
.page-id-9524 form.hs-form select,
.page-id-9524 form.hs-form textarea,
.page-id-9641 form.hs-form input,
.page-id-9641 form.hs-form label,
.page-id-9641 form.hs-form legend,
.page-id-9641 form.hs-form select,
.page-id-9641 form.hs-form textarea,
.page-top-header .page-image .place h3,
.share-block .follow-blog a,
.share-block ul li p,
.subscribe-form form.hs-form input,
.subscribe-form form.hs-form label,
.subscribe-form form.hs-form legend,
.subscribe-form form.hs-form select,
.subscribe-form form.hs-form textarea,
.subscribe-form-relative form.hs-form input,
.subscribe-form-relative form.hs-form label,
.subscribe-form-relative form.hs-form legend,
.subscribe-form-relative form.hs-form select,
.subscribe-form-relative form.hs-form textarea,
.tutorials-bot [class*="col-"] p,
.tutorials-bot.search-bot span,
.tutorials-list .chapter-child a,
.tutorials-top-holder .tutorials-top-search-block form input,
.tutorials-top-holder p,
a.alm-load-more-btn,
article.featured .read-more,
article.featured h2,
footer.main-footer .contacts ul li .phone,
footer.main-footer .contacts ul li a,
footer.main-footer .copyright p,
footer.main-footer .footer-nav .sec_header,
footer.main-footer .footer-nav a,
h3,
header.search-header form input,
nav.side-navigation {
  font-weight: 500;
}
.bold-font {
  font-weight: 700;
}
.extra-bold-font {
  font-weight: 900;
}
.about-us-press-mentions p,
.medium-italic-font {
  font-style: italic;
}
.about-us-founders h5,
.about-us-management h5,
.regular-italic-font,
article.featured .date,
article.post footer.post-meta .date {
  font-style: italic;
}
h1 {
  font-size: 26px;
  line-height: 34px;
  max-width: 100%;
  margin: 0 auto 27px;
}
@media only screen and (min-width: 768px) {
  h1 {
    max-width: 620px;
    font-size: 49px;
    line-height: 64px;
  }
}
h2 {
  font-size: 29px;
  line-height: 38px;
  color: #656565;
  margin-bottom: 20px;
}
h3 {
  font-size: 15px;
  line-height: 20px;
  /* color: #656565; */
  margin-bottom: 25px;
}
.btn.active:focus,
.btn:active:focus,
.btn:focus {
  outline: 0;
  box-shadow: none;
}
.btn {
  border-radius: 4px;
  display: inline-block;
}
.btn.btn-get-started {
  height: 40px;
  color: #31624b;
  background-color: #fff;
  font-size: 15px;
  padding: 8px 32px 10px;
  opacity: 0.7;
  transition: all 0.4s;
}
.btn.btn-get-started:hover {
  opacity: 1;
}
.btn.btn-transparent {
  border: 2px solid #fff;
  padding: 5px 23px 7px;
  font-size: 13px;
  opacity: 0.7;
  color: #fff;
  transition: opacity 0.4s;
}
.btn.btn-transparent:hover {
  color: #fff;
  opacity: 1;
}
.btn.btn-login {
  height: 40px;
  padding: 7px 23px;
  font-size: 15px;
}
.btn.btn-search {
  position: absolute;
  top: 10px;
  right: 0;
  width: 30px;
  height: 30px;
  padding: 14px;
  background: url(../images/ico-search.svg) no-repeat;
  background-position: center;
  z-index: 1;
  transition: all 0.4s;
  transition-timing-function: ease-out;
}
@media only screen and (min-width: 768px) {
  .btn.btn-search {
    width: 85px;
    background-position: left center;
  }
}
.btn.btn-search span {
  position: absolute;
  left: 30px;
  top: 2px;
  font-size: 17px;
}
.btn.btn-load-more {
  padding: 7px 52px;
  border: 2px solid #ebebee;
  color: #656565;
  font-size: 15px;
  margin-top: 40px;
  height: 40px;
}
.btn.btn-load-more:hover {
  color: #656565;
}
.btn.btn-fill {
  opacity: 1;
}
.btn.btn-fill:hover {
  background-color: #fff;
  border: 2px solid #fff;
  color: #3796c9;
  transition: all 0.4s;
}
.btn.btn-fill-grey {
  opacity: 1;
}
.btn.btn-fill-grey:hover {
  color: #656565;
}
.btn.btn-fill-dark:hover {
  background: #656565;
  border: 2px solid #656565;
  color: #fff;
}
.btn.btn-big {
  height: 50px;
  padding: 10px 85px;
  font-size: 17px;
  line-height: 23px;
}
@media only screen and (min-width: 768px) {
  .btn.sm-btn-hidden {
    opacity: 0;
  }
}
.btn.btn-blue-small {
  background: #3796c9;
  height: 30px;
  padding: 5px 23px 7px;
  color: #fff;
  font-size: 13px;
  transition: all 0.4s;
}
.btn.btn-blue-small:hover {
  background-color: #5fb7e5;
}
.btn.btn-blue-medium {
  background: #3796c9;
  height: 40px;
  padding: 9px 23px 11px;
  color: #fff;
  font-size: 15px;
  transition: all 0.4s;
}
.btn.btn-blue-medium:hover {
  background-color: #5fb7e5;
}
.ajax-load-more-wrap.blue button.alm-load-more-btn,
a.alm-load-more-btn {
  display: inline-block;
  padding: 6px 52px;
  border: 2px solid #ebebee;
  color: #656565;
  font-size: 15px;
  margin-top: 20px;
  background: 0 0;
  border-radius: 4px;
  font-size: 15px;
  line-height: inherit;
  height: 40px;
  transition: all 0.4s;
}
.ajax-load-more-wrap.blue button.alm-load-more-btn:hover,
a.alm-load-more-btn:hover {
  background: #656565;
  border: 2px solid #656565;
  color: #fff;
}
button.alm-load-more-btn.loading:before {
  display: none;
  height: 20px;
  background: #656565;
}
.ajax-load-more-wrap.blue button.alm-load-more-btn.done {
  display: none;
}
.alm-btn-wrap {
  text-align: center;
  padding: 10px 10px 60px;
}
.cta_button:hover {
  background-color: #656565;
  color: #fff;
}
.button.gform_button,
.cta_button_blue,
.to-form {
  display: inline-block;
  font-weight: 500;
  border-radius: 4px;
  background: 0 0;
  font-size: 17px;
  color: #fff;
  font-family: Roboto, sans-serif;
  border: 2px solid #3094ca;
  background: #3094ca;
  padding: 13px 60px;
  transition: all 0.4s;
}
.button.gform_button:hover,
.cta_button_blue:hover,
.to-form:hover {
  text-decoration: none;
  color: #fff;
  background: #5fb7e5;
  border: 2px solid #5fb7e5;
}
.btn-hubspot-light {
  font-weight: 500 !important;
  border-radius: 4px !important;
  background: 0 0 !important;
  font-size: 17px !important;
  color: #656565 !important;
  font-family: Roboto, sans-serif !important;
  border: 2px solid #ebebee !important;
  padding: 13px 60px !important;
}
.btn-hubspot-light:hover {
  background: #656565 !important;
  color: #fff !important;
  border: 2px solid #656565 !important;
}
::-webkit-input-placeholder {
  font-weight: 400;
  font-size: 16px;
}
:-moz-placeholder {
  font-weight: 400;
  font-size: 16px;
}
::-moz-placeholder {
  font-weight: 400;
  font-size: 16px;
}
:-ms-input-placeholder {
  font-weight: 400;
  font-size: 16px;
}
.link:active:focus,
.link:focus {
  outline: 0;
}
.link.link-tag {
  color: #9b9b9b;
  text-transform: uppercase;
  font-size: 11px;
  line-height: 15px;
  letter-spacing: 2px;
  transition: color 0.4s;
}
.link.link-tag:hover {
  color: #9b9b9b;
}
.link.link-tag.link-white {
  color: #fff;
}
.link.link-tag.link-white:hover {
  color: #fff;
}
.popup {
  display: none;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 9000;
}
.popup.popup-mob-search-form {
  position: fixed;
  top: 0;
}
.popup.popup-mob-search-form .search-block {
  position: relative;
  width: 100%;
  height: 60px;
  background: #f5f5f5;
  overflow: visible;
  margin-top: 0;
}
.popup.popup-mob-search-form .search-block form {
  padding-left: 30px;
}
.popup.popup-mob-search-form .search-block input {
  width: 100%;
  background: 0 0;
  display: inline-block;
  font-size: 17px;
  padding: 18px 18px 18px 10px;
}
.popup.popup-mob-search-form .search-block .back {
  position: relative;
  z-index: 10;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-top: 22px;
  background: url(../images/arrow-left.svg) no-repeat;
  float: left;
  margin-right: 12px;
}
.popup.popup-mob-search-form .search-block .clear {
  position: absolute;
  z-index: 10;
  top: 9px;
  right: 20px;
  width: 30px;
  height: 30px;
  padding: 20px;
  opacity: 1;
}
.popup.popup-mob-search-form .search-block .clear:after,
.popup.popup-mob-search-form .search-block .clear:before {
  top: 15px;
  left: 20px;
  position: absolute;
  content: " ";
  width: 2px;
  height: 10px;
  background: #656565;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.popup.popup-mob-search-form .search-block .clear:after {
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.popup.popup-mob-search-form .asr-container {
  width: 95%;
  border: none;
  margin-left: -10px;
}
.popup.popup-mob-search-form .asr-container:after {
  display: none;
}
.popup.video-popup {
  position: fixed;
  top: 10px;
  padding: 0 20px;
  left: 0;
  width: 100%;
  height: 200px;
  background: 0 0;
}
@media only screen and (min-width: 768px) {
  .popup.video-popup {
    top: 100px;
    padding: 0;
    margin-left: -320px;
    left: 50%;
    width: 640px;
    height: 360px;
  }
}
@media only screen and (min-width: 1200px) {
  .popup.video-popup {
    margin-left: -480px;
    left: 50%;
    width: 960px;
    height: 540px;
  }
}
.popup.video-popup iframe {
  width: 100%;
  height: 200px;
}
@media only screen and (min-width: 768px) {
  .popup.video-popup iframe {
    width: 640px;
    height: 360px;
  }
}
@media only screen and (min-width: 1200px) {
  .popup.video-popup iframe {
    width: 960px;
    height: 540px;
  }
}
#sumome-smartbar-popup {
  display: none !important;
}
header.top-bar-header {
  position: fixed;
  z-index: 1000;
  top: 0;
  width: 100%;
  height: 80px;
  padding: 0 20px;
  background: rgba(41, 101, 83, 0.8);
}
header.top-bar-header .top-bar-nav {
  position: relative;
  margin-top: 20px;
}
header.top-bar-header .menu-open {
  position: relative;
  display: inline-block;
  width: 37px;
  height: 37px;
  padding: 10px;
  text-align: left;
  margin-left: 20px;
}
header.top-bar-header .menu-open span {
  position: absolute;
  width: 17px;
  height: 3px;
  background: #fff;
  left: 10px;
  top: 17px;
}
header.top-bar-header .menu-open:after,
header.top-bar-header .menu-open:before {
  content: " ";
  position: absolute;
  background: #fff;
  width: 17px;
  height: 3px;
  top: 11px;
  left: 10px;
}
header.top-bar-header .menu-open:after {
  top: auto;
  bottom: 11px;
  left: 10px;
}
nav.side-navigation {
  z-index: 9999;
  position: fixed;
  top: 0;
  right: -272px;
  width: 100%;
  height: 100%;
  max-width: 272px;
  padding: 20px 0 29px;
  background: #434343;
}
nav.side-navigation .menu-close {
  position: absolute;
  z-index: 10;
  top: 20px;
  right: 15px;
  display: block;
  width: 30px;
  height: 30px;
  padding: 20px;
  opacity: 1;
}
nav.side-navigation .menu-close:before {
  content: "×";
  top: 14px;
  left: 16px;
  position: absolute;
  width: 10px;
  height: 10px;
  line-height: 10px;
  font-size: 16px;
  color: #fff;
  font-weight: 400;
}
nav.side-navigation .menu {
  height: 100%;
  padding: 0 33px;
  overflow: auto;
}
nav.side-navigation .menu li {
  position: relative;
  height: 40px;
  padding: 10px 0;
}
nav.side-navigation .menu li a {
  color: #969696;
  text-decoration: none;
  line-height: 18px;
}
nav.side-navigation .menu li a:hover {
  color: #fff;
  text-decoration: underline;
}
nav.side-navigation .menu li.current-menu-item:before {
  content: " ";
  position: absolute;
  bottom: 0;
  left: -33px;
  width: 5px;
  height: 100%;
  background: #3195cb;
}
nav.side-navigation .menu li.current-menu-item a {
  color: #fff;
}
nav.side-navigation .blog-menu-holder {
  margin-top: 20px;
}
nav.side-navigation .blog-menu-holder .menu li {
  font-size: 13px;
  height: 35px;
}
nav.side-navigation .blog-menu-holder .menu li:first-child {
  border-top: 2px solid #8ec3a7;
  height: 55px;
  padding-top: 30px;
}
nav.side-navigation .blog-menu-holder .menu li.current-menu-item:before,
nav.side-navigation .blog-menu-holder .menu li.current_page_parent:before {
  max-height: 33px;
}
nav.side-navigation .btn-holder {
  margin-top: 25px;
  padding: 0 33px;
}
nav.side-navigation .btn-holder .btn {
  width: 100%;
  max-width: 350px;
  margin-bottom: 10px;
}
header.top-bar-header .logo {
  width: 136px;
  height: 49px;
  position: absolute;
  top: 15px;
  left: 87px;
  z-index: 1;
  margin-left: -68px;
}
header.top-bar-header .logo::after {
  content: "";
  position: absolute;
  width: 146px;
  height: 54px;
  top: 50%;
  left: 50%;
  margin-left: -73px;
  margin-top: -20px;
  background-image: url(../images/white.svg);
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 146px 54px;
}
header .menu_items {
  display: none;
  float: left;
  height: 80px;
  margin-left: 170px;
  font-weight: 500;
}
header .menu_items a,
header .menu_items div {
  position: relative;
  float: right;
  font-size: 15px;
  line-height: 17px;
  color: #fff;
  padding: 32px 13px 31px;
  transition: all 0.4s;
  text-decoration: none;
}
header .menu_items a:hover,
header .menu_items div:hover {
  opacity: 1;
  background: #3f7c6b;
  text-decoration: none;
}
header .menu_items a:hover span,
header .menu_items div:hover span {
  text-decoration: none;
}
header .menu_items a.current-menu-item:after,
header .menu_items div.current-menu-item:after {
  position: absolute;
  left: 0;
  bottom: 0;
  content: "";
  display: block;
  width: 100%;
  height: 5px;
  background: #fff;
}
header .menu_items a.has-submenu,
header .menu_items div.has-submenu {
  cursor: pointer;
  padding: 32px 38px 31px 22px;
  min-width: 134px;
  text-align: center;
}
header .menu_items a.has-submenu:before,
header .menu_items div.has-submenu:before {
  position: absolute;
  right: 20px;
  top: 38px;
  content: "";
  display: block;
  width: 10px;
  height: 6px;
  background: url(../images/arrow-down-white.svg) no-repeat;
  transition: all 0.4s;
}
header .menu_items a.has-submenu .submenu,
header .menu_items div.has-submenu .submenu {
  opacity: 0;
  position: absolute;
  height: 0;
  top: 80px;
  left: 0;
  min-width: 200px;
  padding: 0;
  background: #3f7c6b;
  transition: all 0.4s;
  overflow: hidden;
}
header .menu_items a.has-submenu .submenu a,
header .menu_items div.has-submenu .submenu a {
  display: block;
  width: 100%;
  padding: 10px 12px 10px 22px;
  text-align: left;
}
header .menu_items a.has-submenu .submenu a:hover,
header .menu_items div.has-submenu .submenu a:hover {
  background: #5ba38d;
  text-decoration: none;
}
header .menu_items a.has-submenu:hover:before,
header .menu_items div.has-submenu:hover:before {
  -webkit-transform: rotate(-180deg);
  -ms-transform: rotate(-180deg);
  transform: rotate(-180deg);
}
header .menu_items a.has-submenu:hover .submenu,
header .menu_items div.has-submenu:hover .submenu {
  opacity: 1;
  height: auto;
}
@media only screen and (min-width: 1230px) {
  header .menu_items {
    display: block;
  }
}
@media only screen and (min-width: 1200px) {
  header .menu_items a {
    padding: 32px 10px 31px;
    min-width: 94px;
    text-align: center;
  }
}
@media only screen and (min-width: 1340px) {
  header .menu_items a {
    padding: 32px 21px 31px;
    min-width: 116px;
  }
}
.log_custom a span {
  display: block;
}
.log_custom {
  height: 40px;
  min-width: 136px;
  border: 2px solid #dff6e9;
  border-radius: 4px;
  float: right;
  margin-right: 10px;
  transition: border-color 0.1s ease-out;
}
.log_custom:hover {
  border-color: #fff;
}
.lc_local {
  width: 126px;
  height: 36px;
  float: right;
  text-decoration: none;
}
.lc_local:hover {
  text-decoration: none;
}
.lc_btn {
  width: 38px;
  height: 26px;
  float: right;
  border-left: 2px solid #89b5a6;
  border-color: rgba(255, 255, 255, 0.4);
  margin-top: 5px;
}
.lc_fb .lc_ico {
  width: 7px;
  height: 15px;
  background-image: url(../images/img_sprite.png);
  background-position: -104px -475px;
  margin: 6px 0 0 14px;
}
.lc_gp .lc_ico {
  width: 18px;
  height: 15px;
  background-image: url(../images/img_sprite.png);
  background-position: -113px -475px;
  margin: 6px 0 0 12px;
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .lc_fb .lc_ico,
  .lc_gp .lc_ico {
    background-image: url(../images/retina_sprite.png);
    background-size: 500px 511px;
  }
}
.lc_local {
  text-align: center;
}
.lc_txt {
  font-size: 15px;
  font-weight: 500;
  line-height: 17px;
  color: #dff6e9;
  margin-top: 9px;
  transition: color 0.1s ease-out;
}
.lc_local:hover .lc_txt {
  color: #fff;
}
body,
html {
  height: 100%;
  color: #656565;
  font-size: 15px;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility !important;
}
.main-container {
  position: relative;
}
.container {
  position: relative;
  padding-left: 20px;
  padding-right: 20px;
  max-width: 1000px;
}
.none {
  display: none;
}
.left {
  float: left;
}
.alignleft {
  float: left;
  margin: 5px 20px 20px 0;
}
.right {
  float: right;
}
.clearfix:after {
  content: " ";
  display: table;
  clear: both;
}
.main-overlay {
  display: none;
  position: fixed;
  z-index: 4000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}
.search-block {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 38px;
  overflow: hidden;
  border-radius: 4px;
  margin-top: -15px;
}
.search-block .search-holder {
  overflow: visible;
}
@media only screen and (min-width: 768px) {
  .search-block.active {
    border-right: 1px solid #ccc;
  }
}
@media only screen and (min-width: 768px) {
  .search-block.active .search-form {
    right: 0;
  }
}
@media only screen and (min-width: 768px) {
  .search-block.active .btn-search {
    right: 100%;
    margin-right: -40px;
  }
}
@media only screen and (min-width: 768px) {
  .search-block.active .auto-suggest-submit {
    left: 8px;
  }
}
.search-block.ambassadors-search {
  margin-bottom: 10px;
  margin-top: 0;
  min-height: 48px;
}
.search-block.ambassadors-search.active {
  border-right: 1px solid #ccc;
  background: #fff;
}
.search-block.ambassadors-search.active .btn-search {
  right: 100%;
  margin-right: -40px;
}
.search-block.ambassadors-search.active .auto-suggest-submit {
  left: 8px;
}
.search-block.ambassadors-search.active form {
  display: block;
  right: 0;
}
.search-block.ambassadors-search.active .auto-suggest-submit {
  left: 9px;
}
.search-block.ambassadors-search.active .btn-search {
  width: 30px;
  background-position: center;
}
.search-block.ambassadors-search.active .btn-search span {
  display: none;
}
.search-block.ambassadors-search.sm-active-visible {
  overflow: visible;
}
.search-block.ambassadors-search.border-right {
  border-right: 1px solid #ccc;
}
@media only screen and (min-width: 768px) {
  .search-block.sm-active-visible {
    overflow: visible;
  }
}
@media only screen and (min-width: 768px) {
  .search-block.border-right {
    border-right: 1px solid #ccc;
  }
}
.search-block .search-form {
  position: relative;
  border-radius: 4px;
  border: 1px solid #ccc;
  border-right: none;
  padding: 11px 12px 12px 57px;
  right: -100%;
  transition: right 0.4s;
  transition-timing-function: ease-out;
}
.search-block input {
  border: none;
  width: 100%;
  font-size: 17px;
  padding: 0;
  margin-top: 0;
  height: 23px;
}
.floating-top-block {
  position: fixed;
  top: -200px;
  width: 100%;
  background: rgba(55, 150, 201, 0.9);
  z-index: 1500;
  padding: 12px;
  color: #fff;
  transition: top 0.6s;
  transition-timing-function: ease-out;
}
.floating-top-block h6,
.floating-top-block p {
  font-size: 15px;
}
.floating-top-block h6 a,
.floating-top-block p a {
  margin-left: 25px;
}
.floating-top-block .close {
  position: absolute;
  top: 8px;
  right: 15px;
  width: 30px;
  height: 30px;
  padding: 20px;
  opacity: 1;
}
.floating-top-block .close:after,
.floating-top-block .close:before {
  top: 15px;
  left: 20px;
  position: absolute;
  content: " ";
  width: 2px;
  height: 10px;
  background: #fff;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.floating-top-block .close:after {
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.floating-top-block.sticky {
  top: 0;
  transition: top 0.6s;
  transition-timing-function: ease-out;
}
.floating-bottom-block {
  position: fixed;
  z-index: 16000003;
  bottom: 0;
  width: 100%;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-top: 1px solid #ebebee;
  transition: bottom 0.2s;
  transition-timing-function: ease-out;
  display: none;
}
@media only screen and (min-width: 768px) {
  .floating-bottom-block {
    display: block;
  }
}
.floating-bottom-block .container {
  max-width: 1220px;
  padding-top: 20px;
}
@media only screen and (min-width: 768px) {
  .floating-bottom-block .container {
    margin-left: 170px;
  }
}
@media only screen and (min-width: 992px) {
  .floating-bottom-block .container {
    margin-left: auto;
  }
}
.floating-bottom-block a.title {
  position: absolute;
  top: 12px;
  left: 20px;
  display: inline-block;
  background: url(../images/infogram-logo2.svg) no-repeat;
  background-size: 89px 26px;
  background-position: left center;
  padding-left: 97px;
  font-size: 25px;
  line-height: 33px;
  color: #656565;
  text-decoration: none;
}
.floating-bottom-block.hide-bottom {
  bottom: -300px;
  transition: bottom 1s;
  transition-timing-function: ease-out;
}
@media only screen and (min-width: 768px) {
  .floating-bottom-block.tutorials-floating-bottom .container {
    margin-left: 190px;
  }
}
@media only screen and (min-width: 1200px) {
  .floating-bottom-block.tutorials-floating-bottom .container {
    margin-left: auto;
  }
}
.floating-bottom-block .subscribe-form {
  position: absolute;
  width: 399px;
  height: 70px;
  margin-right: 10px;
  padding-top: 10px;
  right: 15px;
  bottom: 0;
}
.floating-bottom-block .subscribe-form .subscribe-popup {
  display: none;
  position: absolute;
  bottom: 85px;
  width: 100%;
  background: #184d6e;
  padding: 20px;
  border-radius: 4px;
}
.floating-bottom-block .subscribe-form .subscribe-popup .close {
  position: absolute;
  top: -5px;
  right: -3px;
  width: 30px;
  height: 30px;
  padding: 20px;
  opacity: 1;
}
.floating-bottom-block .subscribe-form .subscribe-popup .close:after,
.floating-bottom-block .subscribe-form .subscribe-popup .close:before {
  top: 15px;
  left: 20px;
  position: absolute;
  content: " ";
  width: 2px;
  height: 13px;
  background: #fff;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.floating-bottom-block .subscribe-form .subscribe-popup .close:after {
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.floating-bottom-block .subscribe-form .subscribe-popup .ico {
  width: 93px;
  height: 93px;
  float: left;
  margin-right: 24px;
  background-image: url(../images/ico-subscribe.svg);
  background-size: 100%;
}
.floating-bottom-block .subscribe-form .subscribe-popup h3 {
  float: left;
  width: 242px;
  font-size: 16px;
  line-height: 23px;
  margin-bottom: 0;
  color: #fff;
}
.floating-bottom-block .subscribe-form .subscribe-popup:after {
  content: "";
  position: absolute;
  bottom: -6px;
  right: 50px;
  width: 14px;
  height: 14px;
  background: #184d6e;
  border-top: 1px solid #184d6e;
  border-right: 1px solid #184d6e;
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.floating-bottom-block .subscribe-form .hs-form label {
  display: none;
}
.floating-bottom-block .subscribe-form .hs-form .field {
  float: left;
  margin-bottom: 0;
}
.floating-bottom-block .subscribe-form .hs-form input {
  width: 248px;
  height: 19px;
  margin-top: 0;
  border: 1px solid #aaa;
  font-size: 17px;
  box-shadow: none;
  margin-top: 10px;
}
.floating-bottom-block .subscribe-form .hs-form .hs_submit {
  width: 120px;
  float: left;
}
.floating-bottom-block .subscribe-form .hs-form .hs-error-msgs {
  display: none !important;
}
.floating-bottom-block .subscribe-form .hs-form .actions {
  margin-top: 10px;
  margin-left: 8px;
  padding-top: 0;
  padding-left: 0;
}
.floating-bottom-block .subscribe-form .hs-form input[type="submit"] {
  font-weight: 500 !important;
  margin: 0;
  padding: 10px 20px 11px !important;
  width: 80px;
  text-shadow: none;
  box-shadow: none;
  border: none;
  transition: all 0.4s;
}
.floating-bottom-block .subscribe-form .hs-form input[type="submit"]:active,
.floating-bottom-block .subscribe-form .hs-form input[type="submit"]:hover {
  box-shadow: none;
  border: none;
  background-color: #5fb7e5;
  background-image: none;
}
.floating-bottom-block .subscribe-form .hs-form .error {
  border: 1px solid #ce2533;
  box-shadow: none;
}
.floating-bottom-block .subscribe-form .submitted-message {
  font-size: 22px;
  padding-top: 12px;
  text-align: right;
}
.subscribe-form-relative {
  margin-top: 48px;
  width: 100%;
  padding: 40px 20px 47px;
  background-color: #184d6e;
  border-radius: 4px;
  text-align: center;
}
.subscribe-form-relative h3 {
  color: #fff;
  font-size: 29px;
  line-height: 34px;
  margin-bottom: 8px;
}
.subscribe-form-relative p {
  color: #fff;
  font-size: 15px;
  line-height: 25px;
  margin-bottom: 20px;
}
.subscribe-form-relative .form-holder {
  width: 100%;
  height: 120px;
  margin: 0 auto;
}
@media only screen and (min-width: 768px) {
  .subscribe-form-relative .form-holder {
    width: 399px;
    height: 50px;
  }
}
.subscribe-form-relative .form-holder .hs-form label {
  display: none;
}
.subscribe-form-relative .form-holder .hs-form .field {
  margin-bottom: 10px;
}
@media only screen and (min-width: 768px) {
  .subscribe-form-relative .form-holder .hs-form .field {
    height: 50px;
    float: left;
    margin-bottom: 0;
  }
}
.subscribe-form-relative .form-holder .hs-form input {
  width: calc(100% - 20px);
  height: 29px;
  margin-top: 0;
  border: 1px solid #aaa;
  font-size: 17px;
  box-shadow: none;
}
@media only screen and (min-width: 768px) {
  .subscribe-form-relative .form-holder .hs-form input {
    width: 248px;
    float: left;
  }
}
.subscribe-form-relative .form-holder .hs-form .hs_submit {
  width: 100%;
}
@media only screen and (min-width: 768px) {
  .subscribe-form-relative .form-holder .hs-form .hs_submit {
    width: 120px;
    float: left;
  }
}
.subscribe-form-relative .form-holder .hs-form .hs-error-msgs {
  display: none !important;
}
.subscribe-form-relative .form-holder .hs-form .actions {
  margin-top: 0;
  margin-left: 0;
  padding-top: 0;
  padding-left: 0;
}
@media only screen and (min-width: 768px) {
  .subscribe-form-relative .form-holder .hs-form .actions {
    float: left;
    margin-left: 8px;
  }
}
.subscribe-form-relative .form-holder .hs-form input[type="submit"] {
  font-weight: 500 !important;
  margin: 0;
  padding: 10px 20px 11px !important;
  width: calc(100% - 20px);
  max-width: 100%;
  box-shadow: none;
  text-shadow: none;
  border: none;
  transition: all 0.4s;
}
@media only screen and (min-width: 768px) {
  .subscribe-form-relative .form-holder .hs-form input[type="submit"] {
    width: 80px;
  }
}
.subscribe-form-relative .form-holder .hs-form input[type="submit"]:active,
.subscribe-form-relative .form-holder .hs-form input[type="submit"]:hover {
  box-shadow: none;
  border: none;
  background-color: #5fb7e5;
  background-image: none;
}
.subscribe-form-relative .form-holder .hs-form .error {
  border: 1px solid #ce2533;
  box-shadow: none;
}
.subscribe-form-relative .form-holder .submitted-message {
  font-size: 22px;
  color: #fff;
}
.amb-profile-soc ul li,
.share-block ul li {
  height: 30px;
  float: left;
}
.amb-profile-soc ul li p,
.share-block ul li p {
  margin-right: 27px;
  line-height: 30px;
}
.amb-profile-soc ul li a,
.share-block ul li a {
  position: relative;
  width: 30px;
  height: 30px;
  display: block;
  background-position: center center;
  background-repeat: no-repeat;
  margin-right: 10px;
}
.amb-profile-soc ul li a:before,
.share-block ul li a:before {
  content: " ";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 0.4s;
  background-position: center center;
  background-repeat: no-repeat;
}
.amb-profile-soc ul li a:hover:before,
.share-block ul li a:hover:before {
  opacity: 1;
}
@media only screen and (min-width: 768px) {
  .amb-profile-soc ul li a,
  .share-block ul li a {
    margin-right: 30px;
  }
}
.amb-profile-soc ul li a.fb,
.share-block ul li a.fb {
  background-image: url(../images/ico-facebook.svg);
}
.amb-profile-soc ul li a.fb:hover:before,
.share-block ul li a.fb:hover:before {
  background-image: url(../images/ico-facebook-hover.svg);
}
.amb-profile-soc ul li a.tw,
.share-block ul li a.tw {
  background-image: url(../images/ico-twitter.svg);
}
.amb-profile-soc ul li a.tw:hover:before,
.share-block ul li a.tw:hover:before {
  background-image: url(../images/ico-twitter-hover.svg);
}
.amb-profile-soc ul li a.in,
.share-block ul li a.in {
  background-image: url(../images/ico-linkedin.svg);
}
.amb-profile-soc ul li a.in:hover:before,
.share-block ul li a.in:hover:before {
  background-image: url(../images/ico-linkedin-hover.svg);
}
.amb-profile-soc ul li a.gp,
.share-block ul li a.gp {
  background-image: url(../images/ico-google.svg);
  background-size: 22px;
}
.amb-profile-soc ul li a.gp:hover:before,
.share-block ul li a.gp:hover:before {
  background-image: url(../images/ico-google-hover.svg);
  background-size: 22px;
}
.amb-profile-soc .follow-blog a,
.share-block .follow-blog a {
  line-height: 30px;
  display: inline-block;
  background-image: url(../images/medium-logo.png);
  background-size: 31px 27px;
  background-repeat: no-repeat;
  background-position: right center;
  padding-right: 47px;
}
.amb-profile-soc .follow-blog a:hover,
.share-block .follow-blog a:hover {
  color: #9b9b9b;
  text-decoration: none;
}
.share-block.tutorials-share-block,
.tutorials-share-block.amb-profile-soc {
  margin-bottom: 140px;
}
.related-articles .alm-btn-wrap {
  display: none;
}
.categories-switch select {
  position: relative;
  top: 0;
  width: 140px;
  height: 35px;
  line-height: 20px;
  color: #3796c9;
  background-color: #fff;
  background-image: url(../images/arrow-down-blue.svg);
  background-repeat: no-repeat;
  -webkit-appearance: none;
  background-position: center right 10px;
  background-size: 10px 6px;
  border: 1px solid #ccc;
  padding: 5px 20px;
  cursor: pointer;
}
@media only screen and (max-width: 767px) {
  .col-xs-full {
    width: 100%;
  }
}
.cta-block {
  height: 53px;
}
.cta-block.button-blue {
  height: 53px;
  margin-bottom: 70px;
}
.page-id-208 .container,
.page-id-9524 .container,
.page-id-9641 .container,
.subscribe-form .container,
.subscribe-form-relative .container {
  max-width: 780px;
}
.page-id-208 form.hs-form,
.page-id-9524 form.hs-form,
.page-id-9641 form.hs-form,
.subscribe-form form.hs-form,
.subscribe-form-relative form.hs-form {
  background: 0 0;
  padding: 0 !important;
  max-width: 480px;
  margin: 0 auto;
}
.page-id-208 form.hs-form .field,
.page-id-9524 form.hs-form .field,
.page-id-9641 form.hs-form .field,
.subscribe-form form.hs-form .field,
.subscribe-form-relative form.hs-form .field {
  margin-bottom: 20px;
}
.page-id-208 form.hs-form ul li,
.page-id-9524 form.hs-form ul li,
.page-id-9641 form.hs-form ul li,
.subscribe-form form.hs-form ul li,
.subscribe-form-relative form.hs-form ul li {
  padding-left: 0;
}
.page-id-208 form.hs-form ul li:before,
.page-id-9524 form.hs-form ul li:before,
.page-id-9641 form.hs-form ul li:before,
.subscribe-form form.hs-form ul li:before,
.subscribe-form-relative form.hs-form ul li:before {
  display: none;
}
.page-id-208 form.hs-form .hs-error-msgs,
.page-id-9524 form.hs-form .hs-error-msgs,
.page-id-9641 form.hs-form .hs-error-msgs,
.subscribe-form form.hs-form .hs-error-msgs,
.subscribe-form-relative form.hs-form .hs-error-msgs {
  color: #ce2533;
}
.page-id-208 form.hs-form .hs-error-msgs li label,
.page-id-9524 form.hs-form .hs-error-msgs li label,
.page-id-9641 form.hs-form .hs-error-msgs li label,
.subscribe-form form.hs-form .hs-error-msgs li label,
.subscribe-form-relative form.hs-form .hs-error-msgs li label {
  font-size: 16px;
}
.page-id-208 form.hs-form .actions,
.page-id-9524 form.hs-form .actions,
.page-id-9641 form.hs-form .actions,
.subscribe-form form.hs-form .actions,
.subscribe-form-relative form.hs-form .actions {
  text-align: left !important;
}
.page-id-208 form.hs-form input,
.page-id-208 form.hs-form label,
.page-id-208 form.hs-form select,
.page-id-208 form.hs-form textarea,
.page-id-9524 form.hs-form input,
.page-id-9524 form.hs-form label,
.page-id-9524 form.hs-form select,
.page-id-9524 form.hs-form textarea,
.page-id-9641 form.hs-form input,
.page-id-9641 form.hs-form label,
.page-id-9641 form.hs-form select,
.page-id-9641 form.hs-form textarea,
.subscribe-form form.hs-form input,
.subscribe-form form.hs-form label,
.subscribe-form form.hs-form select,
.subscribe-form form.hs-form textarea,
.subscribe-form-relative form.hs-form input,
.subscribe-form-relative form.hs-form label,
.subscribe-form-relative form.hs-form select,
.subscribe-form-relative form.hs-form textarea {
  width: 100%;
}
.page-id-208 form.hs-form label,
.page-id-208 form.hs-form legend,
.page-id-9524 form.hs-form label,
.page-id-9524 form.hs-form legend,
.page-id-9641 form.hs-form label,
.page-id-9641 form.hs-form legend,
.subscribe-form form.hs-form label,
.subscribe-form form.hs-form legend,
.subscribe-form-relative form.hs-form label,
.subscribe-form-relative form.hs-form legend {
  font-size: 15px;
  line-height: 21px;
  background: 0 0;
}
.page-id-208 form.hs-form legend,
.page-id-9524 form.hs-form legend,
.page-id-9641 form.hs-form legend,
.subscribe-form form.hs-form legend,
.subscribe-form-relative form.hs-form legend {
  color: #656565 !important;
}
.page-id-208 form.hs-form input,
.page-id-208 form.hs-form select,
.page-id-208 form.hs-form textarea,
.page-id-9524 form.hs-form input,
.page-id-9524 form.hs-form select,
.page-id-9524 form.hs-form textarea,
.page-id-9641 form.hs-form input,
.page-id-9641 form.hs-form select,
.page-id-9641 form.hs-form textarea,
.subscribe-form form.hs-form input,
.subscribe-form form.hs-form select,
.subscribe-form form.hs-form textarea,
.subscribe-form-relative form.hs-form input,
.subscribe-form-relative form.hs-form select,
.subscribe-form-relative form.hs-form textarea {
  border-radius: 4px;
  padding: 9px 10px 10px;
  border: 1px solid #7d7d7d;
  margin-top: 5px;
  margin-bottom: 0;
  max-width: 480px;
}
.page-id-208 form.hs-form select,
.page-id-9524 form.hs-form select,
.page-id-9641 form.hs-form select,
.subscribe-form form.hs-form select,
.subscribe-form-relative form.hs-form select {
  background: 0 0;
  height: 51px;
}
.page-id-208 form.hs-form input[type="submit"],
.page-id-9524 form.hs-form input[type="submit"],
.page-id-9641 form.hs-form input[type="submit"],
.subscribe-form form.hs-form input[type="submit"],
.subscribe-form-relative form.hs-form input[type="submit"] {
  display: block;
  width: 100%;
  max-width: 300px;
  background: #3195cb;
  color: #fff;
  border: none;
  padding: 10px 30px 11px !important;
  font-size: 17px !important;
  margin: 30px auto 25px;
}
.ui-autocomplete-loading {
  background: url(../images/ajax-loader_ec082d5cec1e454aa46387c0887b3eb5.gif)
    right center no-repeat !important;
}
.typeform-widget .typeform-modal {
  z-index: 2000 !important;
}
footer.main-footer {
  position: relative;
}
footer.main-footer .footer-nav {
  text-align: center;
  background: #ebebee;
  padding: 40px 0 30px;
  margin: 0 auto 40px;
}
@media only screen and (min-width: 768px) {
  footer.main-footer .footer-nav {
    text-align: left;
  }
}
footer.main-footer .footer-nav .sec_header {
  font-size: 17px;
  padding-bottom: 20px;
}
footer.main-footer .footer-nav a {
  display: block;
  font-size: 15px;
  line-height: 17px;
  margin-bottom: 13px;
  color: #3796c9;
}
footer.main-footer .footer-nav a:hover {
  color: #5fb7e5;
  text-decoration: underline;
}
footer.main-footer .socialicons {
  margin: 0 auto 20px;
}
footer.main-footer .share-list li {
  display: inline-block;
  line-height: 0.75rem;
  margin-top: 0.3125rem;
}
footer.main-footer .share-list li + li {
  margin-left: 0.3125rem;
}
footer.main-footer .soc_icons {
  text-align: center;
  padding-bottom: 30px;
}
footer.main-footer .soc_icons a {
  display: inline-block;
  width: 40px;
  height: 40px;
  margin: 0 3px;
  background-image: url(../images/img_sprite_2900c94a6ec94815b82dc88d612170f9.png);
  background-repeat: no-repeat;
  background-color: #ebebee;
  border-radius: 4px;
  transition: all 0.4s;
}
footer.main-footer .soc_icons a:hover {
  background-color: rgba(235, 235, 238, 0.6);
}
footer.main-footer .soc_icons a.fb {
  background-position: -4px -570px;
}
footer.main-footer .soc_icons a.tw {
  background-position: -60px -567px;
}
footer.main-footer .soc_icons a.gp {
  background-position: -171px -565px;
}
footer.main-footer .soc_icons a.in {
  background-position: -211px -565px;
}
footer.main-footer .copyright p {
  font-size: 15px;
  color: #656565;
  line-height: 20px;
  padding: 30px 20px 25px;
}
footer.main-footer .copyright p span {
  font-size: 11px;
  line-height: 20px;
}
footer.main-footer .copyright p a {
  color: #3796c9;
}
footer.main-footer .copyright p a:hover {
  color: #5fb7e5;
}
footer.main-footer .copyright img {
  width: 65px;
  height: 20px;
  margin-bottom: 33px;
}
footer.main-footer .contacts {
  font-size: 15px;
  text-align: center;
  line-height: 28px;
}
footer.main-footer .contacts ul li {
  display: inline;
  padding: 0 10px;
}
footer.main-footer .contacts ul li a {
  color: #3796c9;
}
footer.main-footer .contacts ul li a:hover {
  color: #5fb7e5;
}
footer.main-footer .contacts ul li .chat-now,
footer.main-footer .contacts ul li .email,
footer.main-footer .contacts ul li .phone {
  margin: 0 3px;
  background-image: url(../images/img_sprite_2900c94a6ec94815b82dc88d612170f9.png);
  height: 16px;
  padding-left: 23px;
}
footer.main-footer .contacts ul li .email {
  background-position: -7px -653px;
}
footer.main-footer .contacts ul li .phone {
  background-position: -7px -625px;
}
footer.main-footer .contacts ul li .chat-now {
  background-position: -7px -679px;
}
.top-article-holder {
  width: 100%;
  min-height: 90px;
  background-color: #2c5e79;
  margin-bottom: 50px;
}
@media only screen and (min-width: 1200px) {
  .top-article-holder {
    margin-bottom: 45px;
  }
}
.top-article-holder .tags {
  margin-bottom: 16px;
}
article.top-article {
  position: relative;
  min-height: 370px;
  height: auto;
  overflow: hidden;
  background-size: cover;
  background-position: 50% 50%;
  padding: 118px 20px 0;
  color: #fff;
}
@media only screen and (min-width: 768px) {
  article.top-article {
    min-height: 448px;
  }
}
@media only screen and (min-width: 1200px) {
  article.top-article {
    min-height: 492px;
    padding-top: 144px;
  }
}
article.top-article img {
  width: 100%;
}
article.top-article .background-image {
  position: absolute;
  top: 0;
  left: 0;
  height: calc(100% + 16px);
  background-size: cover;
  background-position: 50% 50%;
  -webkit-filter: blur(3px);
  filter: blur(3px);
  width: calc(100% + 16px);
  margin: -8px 0 0 -8px;
}
article.top-article .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #656565;
  opacity: 0.4;
}
article.top-article .post-content {
  position: relative;
  z-index: 1;
}
article.featured {
  margin-bottom: 48px;
}
@media only screen and (min-width: 1200px) {
  article.featured {
    margin-bottom: 38px;
  }
}
@media only screen and (min-width: 1200px) {
  article.featured:last-of-type {
    margin-bottom: 50px;
  }
}
article.featured .pennant {
  position: absolute;
  top: 0;
  right: 25px;
  width: 16px;
  height: 26px;
  background: url(../images/pennant.svg) no-repeat;
}
@media only screen and (min-width: 768px) {
  article.featured .pennant {
    right: 38px;
  }
}
@media only screen and (min-width: 1200px) {
  article.featured .pennant {
    right: 33px;
  }
}
article.featured img {
  width: 100%;
  height: auto;
  margin-bottom: 12px;
}
@media only screen and (min-width: 768px) {
  article.featured img {
    margin-bottom: auto;
  }
}
article.featured h2 {
  font-size: 24px;
  line-height: 32px;
  margin-bottom: 11px;
  color: #656565;
}
article.featured h2 a:hover {
  text-decoration: none;
  color: #9b9b9b;
}
article.featured .excerpt {
  line-height: 23px;
  margin-bottom: 15px;
}
article.featured .tags {
  margin-bottom: 10px;
}
article.featured .date {
  font-size: 13px;
  line-height: 30px;
  color: #9b9b9b;
}
article.featured .read-more {
  display: block;
  font-size: 13px;
  margin-bottom: 13px;
  color: #3796c9;
}
article.featured .read-more:hover {
  color: #5fb7e5;
  text-decoration: underline;
}
article.post {
  margin-bottom: 41px;
}
@media only screen and (min-width: 768px) {
  article.post {
    min-height: 420px;
    margin-bottom: 0;
  }
}
@media only screen and (min-width: 1200px) {
  article.post {
    min-height: 400px;
  }
}
article.post .image-holder {
  width: 100%;
  height: auto;
  min-height: 195px;
  margin-bottom: 10px;
  overflow: hidden;
  background-size: cover;
  background-position: 50% 50%;
}
@media only screen and (min-width: 768px) {
  article.post .image-holder {
    height: 230px;
  }
}
@media only screen and (min-width: 1200px) {
  article.post .image-holder {
    height: 200px;
  }
}
article.post .image-holder a {
  width: 100%;
  height: 195px;
  display: block;
  background: #000;
  opacity: 0;
  transition: opacity 0.4s;
}
@media only screen and (min-width: 768px) {
  article.post .image-holder a {
    height: 230px;
  }
}
@media only screen and (min-width: 1200px) {
  article.post .image-holder a {
    height: 200px;
  }
}
@media only screen and (min-width: 1200px) {
  article.post .image-holder a:hover {
    opacity: 0.3;
  }
}
article.post .image-holder img {
  width: 100%;
  height: auto;
  display: inline-block;
  vertical-align: middle;
}
article.post .post-content h2 {
  font-size: 21px;
  line-height: 29px;
  color: #656565;
  margin-top: 10px;
  margin-bottom: 5px;
}
article.post .post-content h2 a:hover {
  text-decoration: none;
  color: #9b9b9b;
}
article.post .post-content a {
  color: inherit;
  text-decoration: none;
}
article.post .post-content .tags {
  padding-top: 5px;
}
article.post .post-content .tags a {
  color: #9b9b9b;
}
article.post .post-content .tags a:hover {
  text-decoration: underline;
}
article.post footer.post-meta .tags {
  margin-bottom: 5px;
}
article.post footer.post-meta .date {
  font-size: 13px;
  line-height: 30px;
  color: #9b9b9b;
}
.post-2 {
  width: 100%;
  min-height: 353px;
  margin-bottom: 31px;
  background-size: cover;
  background-position: 50% 50%;
  padding-top: 20px;
}
@media only screen and (min-width: 768px) {
  .post-2 {
    min-height: 420px;
    margin-bottom: 38px;
  }
}
@media only screen and (min-width: 1200px) {
  .post-2 {
    min-height: 400px;
  }
}
.post-2 span.logo {
  width: 156px;
  height: 57px;
  display: block;
  margin: 20px auto 20px;
  background: url(../images/infogram-logo.svg) no-repeat;
}
@media only screen and (min-width: 768px) {
  .post-2 span.logo {
    width: 178px;
    height: 65px;
    margin-bottom: 40px;
  }
}
@media only screen and (min-width: 1200px) {
  .post-2 span.logo {
    width: 156px;
    height: 57px;
  }
}
.post-2 span.title {
  display: block;
  font-size: 29px;
  line-height: 38px;
  color: #fff;
  max-width: 267px;
  margin: 0 auto 20px;
}
@media only screen and (min-width: 768px) {
  .post-2 span.title {
    margin-bottom: 47px;
  }
}
.post-2 a.btn {
  display: inline-block;
  color: #fff;
}
.container.single {
  max-width: 1220px;
  margin-bottom: 60px;
}
.container.single img {
  max-width: 100%;
  height: auto;
}
.container.single article.post .post-content div {
  max-width: 100% !important;
}
.container.single article.post .post-content i {
  font-style: italic;
}
.container.single article.post .post-content .p1 {
  font-size: 21px;
  line-height: 34px;
}
.container.single article.post .post-content .p3 {
  line-height: 29px;
}
.container.single article.post .post-content p {
  font-size: 20px;
  line-height: 29px;
  margin-bottom: 20px;
}
.container.single article.post .post-content p img {
  margin-bottom: 25px;
}
.container.single article.post .post-content p.wp-caption-text {
  text-align: center;
  font-size: 13px;
  color: #9b9b9b;
  margin-top: 10px;
}
.container.single article.post .post-content ul {
  margin-bottom: 20px;
  line-height: 29px;
  font-size: 16px;
}
.container.single article.post .post-content ul li {
  position: relative;
  padding-left: 22px;
}
.container.single article.post .post-content ul li:before {
  position: absolute;
  left: 0;
  content: "•";
  color: #b2b2b2;
  font-size: 40px;
}
.container.single article.post .post-content ol {
  margin-left: 22px;
  list-style-position: outside;
  line-height: 29px;
  font-size: 16px;
}
.container.single article.post .post-content ol li {
  padding-left: 5px;
}
.container.single article.post .post-content a {
  color: #3796c9;
}
.container.single article.post .post-content a:hover {
  text-decoration: underline;
}
.container.single article.post .post-content a.to-form {
  color: #fff;
  cursor: pointer;
}
.container.single article.post .post-content a.to-form:hover {
  text-decoration: none;
}
.container.single article.post .post-content h1,
.container.single article.post .post-content h2,
.container.single article.post .post-content h3 {
  font-size: 30px;
  line-height: 38px;
}
.container.single article.post .post-content h1.p1,
.container.single article.post .post-content h1.p2,
.container.single article.post .post-content h1.p3,
.container.single article.post .post-content h2.p1,
.container.single article.post .post-content h2.p2,
.container.single article.post .post-content h2.p3,
.container.single article.post .post-content h3.p1,
.container.single article.post .post-content h3.p2,
.container.single article.post .post-content h3.p3 {
  line-height: 38px;
}
.container.single article.post .post-content h1.p1 a,
.container.single article.post .post-content h1.p2 a,
.container.single article.post .post-content h1.p3 a,
.container.single article.post .post-content h2.p1 a,
.container.single article.post .post-content h2.p2 a,
.container.single article.post .post-content h2.p3 a,
.container.single article.post .post-content h3.p1 a,
.container.single article.post .post-content h3.p2 a,
.container.single article.post .post-content h3.p3 a {
  color: inherit;
}
.container.single article.post .post-content .date h6,
.container.single article.post .post-content .date p {
  font-size: 13px;
  line-height: 13px;
  color: #9b9b9b;
}
.container.single article.post .post-content .date h6 {
  margin-bottom: 20px;
}
.container.single article.post .amb-profile-soc,
.container.single article.post .share-block {
  margin-top: 50px;
}
.container.single-half-margin {
  margin-bottom: 30px;
}
header.search-header {
  background-color: #8ec3a7;
  width: 100%;
  padding: 85px 0 35px;
  margin-bottom: 35px;
}
@media only screen and (min-width: 768px) {
  header.search-header {
    padding: 85px 0 75px;
  }
}
header.search-header h1 {
  color: #fff;
  text-align: center;
  margin-top: 40px;
}
header.search-header form {
  width: 100%;
  max-width: 780px;
  margin: 0 auto;
}
header.search-header form input {
  font-size: 15px;
  line-height: 20px;
  width: 100%;
  height: 50px;
  border-radius: 4px;
  border: none;
  padding: 0 20px 0 53px;
  background-image: url(../images/ico-search.svg);
  background-repeat: no-repeat;
  background-position: left 20px center;
}
.no-results {
  margin: 60px auto 100px;
}
.no-results .img {
  width: 103px;
  height: 88px;
  background-image: url(../images/nothing-found.svg);
  display: inline-block;
  margin-bottom: 33px;
}
.no-results h2 {
  font-size: 29px;
  margin-bottom: 10px;
}
.no-results p {
  margin-bottom: 20px;
}
.tutorials-search-results {
  margin-bottom: 50px;
}
.tutorials-search-results .post {
  margin-bottom: 28px;
}
.tutorials-search-results a {
  font-size: 21px;
  line-height: 29px;
}
.tutorials-search-results a span {
  display: block;
  font-size: 13px;
  line-height: 18px;
  margin-bottom: 6px;
}
.tutorials-search-results a:hover {
  text-decoration: none;
  color: #9b9b9b;
}
header.archive-header {
  background-color: #8ec3a7;
  width: 100%;
  padding: 130px 0 30px;
  margin-bottom: 35px;
}
@media only screen and (min-width: 768px) {
  header.archive-header {
    padding: 130px 0 30px;
  }
}
header.archive-header h1 {
  color: #fff;
  text-align: center;
  text-transform: inherit;
}
header.archive-header h1:first-letter {
  text-transform: uppercase;
}
.container.amb-single {
  max-width: 1220px;
}
.container.amb-single h2 {
  font-size: 21px;
  line-height: 29px;
  margin-bottom: 24px;
}
.container.amb-single .mapbox {
  height: 380px;
}
.ambassadors-top-holder {
  position: relative;
  width: 100%;
  min-height: 400px;
  height: auto;
  overflow: hidden;
  background: url(../images/ambassadors-top.jpg) no-repeat;
  background-size: cover;
  background-position: 50% 50%;
  padding-top: 118px;
  padding-bottom: 120px;
}
@media only screen and (min-width: 768px) {
  .ambassadors-top-holder {
    padding-top: 160px;
  }
}
@media only screen and (min-width: 1200px) {
  .ambassadors-top-holder {
    padding-top: 190px;
  }
}
.ambassadors-top-holder.profile-holder {
  min-height: 370px;
  background: #8ec3a7;
  padding-top: 86px;
  padding-bottom: 35px;
  margin-bottom: 50px;
}
@media only screen and (min-width: 768px) {
  .ambassadors-top-holder.profile-holder {
    padding-top: 93px;
  }
}
@media only screen and (min-width: 1200px) {
  .ambassadors-top-holder.profile-holder {
    padding-top: 123px;
    padding-bottom: 55px;
  }
}
.ambassadors-top-holder.profile-holder .amb-profile-img img {
  width: 130px;
  height: 130px;
  border-radius: 50%;
}
@media only screen and (min-width: 768px) {
  .ambassadors-top-holder.profile-holder .amb-profile-img img {
    width: 200px;
    height: 200px;
  }
}
.ambassadors-top-holder.profile-holder .amb-profile-meta {
  color: #fff;
}
.ambassadors-top-holder.profile-holder .amb-profile-meta .doing {
  font-size: 15px;
  line-height: 25px;
}
.ambassadors-top-holder.profile-holder .amb-profile-meta .country {
  font-size: 13px;
  line-height: 18px;
}
.ambassadors-top-holder.become-an-ambassador-top {
  min-height: 300px;
  color: #fff;
  padding-top: 120px;
  padding-bottom: 100px;
  margin-bottom: 40px;
}
.ambassadors-top-holder h1 {
  color: #fff;
  margin-bottom: 5px;
}
@media only screen and (min-width: 768px) {
  .ambassadors-top-holder h1 {
    margin-bottom: 10px;
  }
}
.ambassadors-top-holder p {
  color: #fff;
  margin-bottom: 35px;
}
.ambassadors-top-holder .ambassadors-top-search-block {
  width: 100%;
  max-width: 780px;
  margin: 0 auto;
}
.ambassadors-top-holder .ambassadors-top-search-block form {
  width: 100%;
}
.ambassadors-top-holder .ambassadors-top-search-block form input {
  font-size: 15px;
  width: 100%;
  height: 50px;
  border-radius: 4px;
  border: none;
  padding: 0 20px 0 53px;
  background-image: url(../images/ico-search.svg);
  background-repeat: no-repeat;
  background-position: left 20px center;
}
.become-an-ambassador {
  background: #3195cb;
  color: #fff;
  padding: 45px 0;
}
.become-an-ambassador h2 {
  color: #fff;
  font-size: 25px;
  line-height: 33px;
  margin-bottom: 12px;
}
@media only screen and (min-width: 768px) {
  .become-an-ambassador h2 {
    font-size: 29px;
    line-height: 38px;
    margin-bottom: 18px;
  }
}
.become-an-ambassador .img {
  text-align: center;
  margin-bottom: 10px;
}
@media only screen and (min-width: 768px) {
  .become-an-ambassador .img {
    margin-bottom: auto;
    text-align: left;
  }
}
@media only screen and (min-width: 768px) {
  .become-an-ambassador .img img {
    width: 100%;
    max-width: 196px;
  }
}
.become-an-ambassador p {
  line-height: 25px;
  margin-bottom: 23px;
}
.become-an-ambassador .btn-big {
  display: block;
  max-width: 300px;
  margin: 0 auto;
}
@media only screen and (min-width: 768px) {
  .become-an-ambassador .btn-big {
    margin: 0;
  }
}
.intro-ambassadors {
  padding: 50px 0;
}
.intro-ambassadors h2 {
  font-size: 25px;
  line-height: 33px;
  margin-bottom: 30px;
}
@media only screen and (min-width: 768px) {
  .intro-ambassadors h2 {
    font-size: 29px;
    line-height: 38px;
  }
}
.intro-ambassadors .amb {
  text-align: center;
  margin-bottom: 30px;
  min-height: 240px;
}
@media only screen and (min-width: 768px) {
  .intro-ambassadors .amb {
    min-height: 1px;
  }
}
.intro-ambassadors .amb .img {
  margin-bottom: 16px;
}
.intro-ambassadors .amb .img img {
  width: 100%;
  max-width: 200px;
  border-radius: 50%;
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}
.intro-ambassadors .amb .name {
  margin-bottom: 8px;
  line-height: 21px;
}
.intro-ambassadors .amb .name a:hover {
  text-decoration: none;
  color: #9b9b9b;
}
.intro-ambassadors .amb .name span {
  font-size: 13px;
  line-height: 18px;
}
.intro-ambassadors .btn-fill-dark {
  margin-top: 0;
  display: inline-block;
}
.mapbox {
  position: relative;
  height: 452px;
  margin-bottom: 40px;
}
@media only screen and (min-width: 768px) {
  .mapbox {
    height: 516px;
  }
}
#map {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  background: #fff;
  font: 12px/20px "Helvetica Neue", Arial, Helvetica, sans-serif;
  color: #404040;
  color: rgba(0, 0, 0, 0.75);
  outline: 0;
  overflow: hidden;
}
#map .amb-popup .leaflet-popup-close-button {
  z-index: 20;
  top: 5px;
  right: 5px;
}
#map .amb-popup .leaflet-popup-close-button:hover {
  background-color: transparent;
}
#map .amb-popup .leaflet-popup-content-wrapper {
  background: #fff;
  color: #000;
  font-size: 14px;
  line-height: 18px;
}
#map .amb-popup .leaflet-popup-content-wrapper .row {
  margin: 0 auto;
  max-width: 60rem;
  width: 100%;
}
#map .amb-popup .leaflet-popup-content-wrapper .row :before,
#map .amb-popup .leaflet-popup-content-wrapper .row:after {
  content: " ";
  display: table;
}
#map .amb-popup .leaflet-popup-content-wrapper,
#map .amb-popup .map-legends,
#map .amb-popup .map-tooltip {
  box-shadow: none;
  border-radius: 5px;
  border: 2px solid #ebebee;
}
#map .amb-popup .leaflet-popup-tip {
  border-left: none;
  border-right: none;
  border-top: none;
}
#map .amb-popup .leaflet-popup-tip:before {
  content: "";
  display: block;
  position: absolute;
  bottom: -2px;
  left: 50%;
  margin-left: -12px;
  width: 0;
  height: 0;
  border-color: #ebebee transparent transparent transparent;
  border-style: solid;
  border-width: 12px;
}
#map .amb-popup .leaflet-popup-tip:after {
  content: "";
  display: block;
  position: absolute;
  bottom: 3px;
  left: 50%;
  margin-left: -10px;
  width: 0;
  height: 0;
  border-color: #fff transparent transparent transparent;
  border-style: solid;
  border-width: 10px;
}
#map .amb-popup .columns {
  position: relative;
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  float: left;
}
#map .amb-popup .amb-profile-img--100 {
  position: absolute;
  width: 100px;
  min-height: 112px;
}
#map .amb-popup .amb-profile-img--100 img {
  max-width: 91px;
  border-radius: 50%;
}
#map .amb-popup .popup__meta {
  padding-left: 7.8125rem;
  min-height: 80px;
}
#map .amb-popup .popup__meta h2 {
  font-size: 15px;
  line-height: 21px;
  color: #656565;
  margin-bottom: 0;
}
#map .amb-popup .popup__meta h3 {
  color: #656565;
  font-size: 13px;
  line-height: 18px;
  margin-bottom: 4px;
}
#map .amb-popup .popup__meta p {
  font-size: 0.8125rem;
}
#map .amb-popup .popup__meta .button {
  height: 30px;
  min-height: 30px;
  border-style: solid;
  border-width: 0;
  line-height: 19px;
  position: relative;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  padding: 5px 23px 7px;
  font-size: 13px;
  background-color: #3796c9;
  border-color: #5fb7e5;
  color: #fff;
  transition: background-color 0.3s ease-out;
  margin-bottom: 0;
}
#map .amb-popup .popup__meta .button.radius {
  border-radius: 4px;
}
#map .amb-popup .popup {
  display: block;
}
.ambassadors-filter nav {
  width: 100%;
  position: relative;
  margin-bottom: 15px;
}
@media only screen and (min-width: 768px) {
  .ambassadors-filter nav {
    display: table;
  }
}
.ambassadors-filter .selected-item {
  position: relative;
  height: 50px;
  padding: 15px 17px;
  height: 50px;
  border: 1px solid #7d7d7d;
  border-radius: 4px;
  line-height: 16px;
  font-size: 13px;
  cursor: pointer;
}
.ambassadors-filter .selected-item .arrow {
  width: 11px;
  height: 11px;
  position: absolute;
  background-image: url(../images/arrow-down.svg);
  background-size: 11px 11px;
  background-repeat: no-repeat;
  background-position: center;
  right: 17px;
  top: 18px;
  transition: all 0.4s;
}
.ambassadors-filter .selected-item span {
  font-size: 15px;
  display: inline-block;
  float: left;
  margin-right: 3px;
}
.ambassadors-filter .selected-item.active .arrow {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.ambassadors-filter ul {
  position: absolute;
  z-index: 10;
  transition: all 0.4s;
  opacity: 0;
  width: 100%;
  height: 0;
  overflow: hidden;
  margin-top: 5px;
  border: 1px solid #7d7d7d;
  background: #fff;
  border-radius: 4px;
  padding: 0 17px;
}
@media only screen and (min-width: 768px) {
  .ambassadors-filter ul {
    position: relative;
    opacity: 1;
    height: auto;
    border: none;
    padding: 0;
  }
}
@media only screen and (max-width: 767px) {
  .ambassadors-filter ul.active {
    opacity: 1;
    height: 260px;
  }
}
.ambassadors-filter li {
  position: relative;
  display: block;
  padding: 13px 0;
  font-size: 13px;
}
@media only screen and (min-width: 768px) {
  .ambassadors-filter li {
    display: inline-block;
    padding: 17px 0;
  }
}
.ambassadors-filter li a {
  position: relative;
  color: #656565;
  display: inline-block;
  text-decoration: none;
  transition: all 0.4s;
}
@media only screen and (min-width: 768px) {
  .ambassadors-filter li a {
    padding: 0 11px;
  }
}
@media only screen and (min-width: 992px) {
  .ambassadors-filter li a {
    padding: 0 25px;
  }
}
@media only screen and (min-width: 1200px) {
  .ambassadors-filter li a {
    margin-right: 16px;
  }
}
.ambassadors-filter li a:hover {
  color: #9b9b9b;
  text-decoration: none;
}
.ambassadors-filter li a:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -7px;
  transition: all 0.4s;
  width: 0;
  height: 4px;
  background: #3796c9;
}
@media only screen and (min-width: 768px) {
  .ambassadors-filter li a:after {
    bottom: -14px;
  }
}
@media only screen and (max-width: 767px) {
  .ambassadors-filter li:first-child {
    padding-top: 17px;
  }
}
.ambassadors-filter li:last-child a {
  margin-right: 0;
}
.ambassadors-filter li span {
  font-size: 15px;
  display: inline-block;
  float: left;
  margin-right: 3px;
}
@media only screen and (min-width: 768px) {
  .ambassadors-filter li.active a {
    color: #656565;
  }
}
.ambassadors-filter li.active a:after {
  transition: width 0.4s;
  width: 100%;
}
@media only screen and (max-width: 767px) {
  .ambassadors-filter li.active a:after {
    background: #fff;
  }
}
@media only screen and (min-width: 768px) {
  .ambassadors-filter li.active a:after {
    color: #656565;
  }
}
.ambassadors-list {
  margin-bottom: 72px;
}
@media only screen and (min-width: 1200px) {
  .ambassadors-list .row {
    min-height: 570px;
  }
}
.ambassadors-list .user {
  border-bottom: 1px solid #ebebee;
  padding: 15px 0;
  min-height: 95px;
}
.ambassadors-list .user .img {
  margin-right: 15px;
}
.ambassadors-list .user .img img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
}
.ambassadors-list .user .name a {
  line-height: 21px;
  margin-bottom: 6px;
  text-decoration: none;
}
.ambassadors-list .user .name a:hover {
  color: #9b9b9b;
}
.ambassadors-list .user .name a span {
  font-size: 13px;
  display: block;
}
.ambassadors-list .become {
  border-bottom: 1px solid #ebebee;
  padding: 15px 0;
  min-height: 95px;
}
.ambassadors-list .become a {
  display: block;
  height: 64px;
  color: #3796c9;
  text-decoration: none;
}
.ambassadors-list .become a:hover {
  color: #5fb7e5;
}
.ambassadors-list .become span.img {
  position: relative;
  display: block;
  width: 64px;
  height: 64px;
  background: #3796c9;
  border-radius: 50%;
  margin-right: 15px;
}
.ambassadors-list .become span.img img {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 20px;
  height: 20px;
  margin-left: -9px;
  margin-top: -10px;
}
.ambassadors-list .become span.text {
  margin-top: 25px;
}
.ambassadors-list .nothing-found {
  display: none;
  padding: 15px;
}
.ambassadors-list .nothing-found span {
  display: block;
  font-size: 13px;
  line-height: 18px;
  margin-top: 5px;
}
.amb-full-about {
  font-size: 16px;
  line-height: 29px;
  margin-bottom: 50px;
}
.amb-related-articles {
  margin-bottom: 18px;
  overflow: hidden;
}
.amb-related-articles .articles .item {
  margin-bottom: 27px;
}
.amb-related-articles .articles .item .img {
  width: 50px;
  height: 33px;
  margin-right: 13px;
  display: table-cell;
  float: left;
}
.amb-related-articles .articles .item .img img {
  width: 100%;
}
.amb-related-articles .articles .item a {
  font-size: 15px;
  line-height: 21px;
}
.amb-related-articles .articles .item a:hover {
  color: #9b9b9b;
}
.amb-related-articles .articles .item .date {
  font-size: 13px;
  line-height: 30px;
  color: #9b9b9b;
}
.amb-related-articles .articles .item .article-meta {
  display: table-cell;
}
.amb-profile-soc {
  margin-bottom: 45px;
  overflow: hidden;
}
.amb-profile-soc ul li {
  height: 47px;
}
.amb-profile-soc ul li a {
  width: 47px;
  height: 47px;
  border-radius: 4px;
  border: 2px solid #ebebee;
  margin-right: 12px;
}
.container.page-with-sidebar-container {
  max-width: 1220px;
}
.container.page-with-sidebar-container .ambassador-logo {
  width: 100%;
  margin-bottom: 33px;
}
.container.page-with-sidebar-container .ambassador-logo img {
  max-width: 250px;
}
.container.page-with-sidebar-container article.post .post-content h1,
.container.page-with-sidebar-container article.post .post-content h2,
.container.page-with-sidebar-container article.post .post-content h3,
.container.page-with-sidebar-container article.post .post-content h4 {
  font-size: 24px;
  line-height: 32px;
  margin-bottom: 14px;
}
.container.page-with-sidebar-container article.post .post-content form li {
  padding-left: 0;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  li:before {
  display: none;
}
.container.page-with-sidebar-container article.post .post-content form label {
  font-size: 15px;
  line-height: 21px;
  margin-bottom: 6px;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  input[type="checkbox"],
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  input[type="radio"] {
  margin-right: 15px;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  input[type="text"],
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  input[type="email"],
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  input[type="url"],
.container.page-with-sidebar-container article.post .post-content form select,
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  textarea {
  width: 100%;
  max-width: 460px;
  height: 50px;
  border-radius: 4px;
  border: 1px solid #7d7d7d;
  margin-bottom: 16px;
  padding: 9px 20px 10px;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  input[type="radio"] {
  float: left;
  position: relative;
  width: 21px;
  height: 21px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid #656565;
  border-radius: 50%;
  outline: 0;
  transition: all 0.4s;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  input[type="radio"]:before {
  content: " ";
  width: 19px;
  height: 19px;
  position: absolute;
  border: 6px solid transparent;
  border-radius: 50%;
  transition: all 0.4s;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  input[type="radio"]:checked {
  border: 1px solid #3796c9;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  input[type="radio"]:checked:before {
  border: 6px solid #184d6e;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_description {
  max-width: 460px !important;
  text-align: center;
  font-size: 21px;
  line-height: 34px;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  textarea {
  min-height: 185px;
}
.container.page-with-sidebar-container article.post .post-content form select {
  background: #fff;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_error
  label {
  color: #ce2533;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_error
  input[type="text"],
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_error
  input[type="email"],
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_error
  input[type="url"],
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_error
  select,
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_error
  textarea {
  border: 1px solid #ce2533;
}
.container.page-with-sidebar-container
  article.post
  .post-content
  form
  .gfield_error
  .gfield_description.validation_message {
  display: none;
}
.container.single article.post .post-content .one-half {
  font-size: 16px;
  line-height: 29px;
}
.container.single
  article.post
  .post-content.tutorials-post-content
  .alignleft[class*="wp-image-"] {
  float: none;
  display: block;
}
.container.single
  article.post
  .post-content.tutorials-post-content
  .aligncenter[class*="wp-image-"] {
  float: none;
  display: block;
}
.container.single article.post .post-content.tutorials-post-content ol {
  margin-bottom: 20px;
}
.container.single
  article.post
  .post-content.tutorials-post-content
  .hbspt-form {
  display: none;
}
.tutorials-top-holder {
  position: relative;
  width: 100%;
  min-height: 400px;
  height: auto;
  overflow: hidden;
  background: url(../images/tutorials-top.jpg) no-repeat;
  background-size: cover;
  background-position: 50% 50%;
  padding-top: 118px;
  padding-bottom: 120px;
  margin-bottom: 50px;
}
@media only screen and (min-width: 768px) {
  .tutorials-top-holder {
    padding-top: 160px;
  }
}
@media only screen and (min-width: 1200px) {
  .tutorials-top-holder {
    padding-top: 190px;
  }
}
.tutorials-top-holder h1 {
  color: #fff;
  margin-bottom: 5px;
}
@media only screen and (min-width: 768px) {
  .tutorials-top-holder h1 {
    margin-bottom: 10px;
  }
}
.tutorials-top-holder p {
  color: #fff;
  margin-bottom: 35px;
}
.tutorials-top-holder .tutorials-top-search-block {
  width: 100%;
  max-width: 780px;
  margin: 0 auto;
}
.tutorials-top-holder .tutorials-top-search-block form {
  width: 100%;
}
.tutorials-top-holder .tutorials-top-search-block form input {
  font-size: 15px;
  width: 100%;
  height: 50px;
  border-radius: 4px;
  border: none;
  padding: 0 20px 0 53px;
  background-image: url(../images/ico-search.svg);
  background-repeat: no-repeat;
  background-position: left 20px center;
}
.tutorials-single-top-holder {
  min-height: 300px;
  background: #8ec3a7;
  padding-top: 86px;
  padding-bottom: 35px;
  margin-bottom: 50px;
}
@media only screen and (min-width: 768px) {
  .tutorials-single-top-holder {
    padding-top: 134px;
    padding-bottom: 83px;
  }
}
.tutorials-single-top-holder h1 {
  color: #fff;
  margin-bottom: 5px;
}
@media only screen and (min-width: 768px) {
  .tutorials-single-top-holder h1 {
    margin-bottom: 10px;
  }
}
.tutorials-list {
  margin-bottom: 20px;
}
.tutorials-list .chapter-title {
  font-size: 21px;
  line-height: 30px;
  height: 30px;
  margin-bottom: 18px;
}
.tutorials-list .chapter-title span {
  height: 30px;
  display: block;
  margin-right: 10px;
}
.tutorials-list .chapter-title span img {
  height: 100%;
  width: auto;
}
.tutorials-list .chapter-children {
  text-align: left;
  margin-bottom: 20px;
}
@media only screen and (min-width: 768px) {
  .tutorials-list .chapter-children {
    min-height: 175px;
    margin-bottom: 55px;
  }
}
.tutorials-list .chapter-child {
  margin-bottom: 5px;
}
.tutorials-list .chapter-child a {
  color: #3796c9;
  font-size: 15px;
  line-height: 25px;
}
.tutorials-list .chapter-child a:hover {
  color: #5fb7e5;
  text-decoration: none;
}
.top-tutorials-video {
  margin-bottom: 20px;
}
.top-tutorials-video .tutorial {
  min-height: 220px;
  margin-bottom: 30px;
}
.top-tutorials-video .img a {
  position: relative;
  margin-bottom: 15px;
  display: block;
}
.top-tutorials-video .img a span {
  transition: all 0.4s;
}
.top-tutorials-video .img a span.play {
  position: absolute;
  z-index: 1;
  left: 50%;
  top: 50%;
  width: 62px;
  height: 62px;
  margin-left: -31px;
  margin-top: -41px;
  background-color: #dc5356;
  border-radius: 50%;
}
.top-tutorials-video .img a span.play:before {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 0 10px 13px;
  border-color: transparent transparent transparent #fff;
  content: "";
  display: block;
  position: relative;
  margin: -10px -4px;
  top: 50%;
  left: 50%;
}
.top-tutorials-video .img a span.video-overlay {
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(49, 98, 75, 0.4);
}
.top-tutorials-video .img a:hover span.play {
  background-color: #ce2533;
}
.top-tutorials-video .img a:hover span.video-overlay {
  background: rgba(49, 98, 75, 0);
}
.top-tutorials-video .img img {
  max-width: 100%;
  vertical-align: top;
}
.top-tutorials-video .title h2 {
  font-size: 21px;
  line-height: 29px;
  margin-bottom: 0;
}
.top-tutorials-video .title a {
  font-size: 21px;
  line-height: 29px;
  transition: all 0.4s;
}
.top-tutorials-video .title a:hover {
  text-decoration: none;
  color: #9b9b9b;
}
.tutorials-top-articles {
  margin-bottom: 40px;
}
.tutorials-top-articles h3 {
  margin-bottom: 12px;
}
.tutorials-top-articles h2 {
  font-size: 21px;
  line-height: 29px;
  border-bottom: 1px solid #ebebee;
  padding: 13px 0;
  margin-bottom: 0;
}
.tutorials-top-articles a {
  color: #3796c9;
  font-size: 21px;
  line-height: 29px;
  transition: all 0.4s;
}
.tutorials-top-articles a:hover {
  text-decoration: none;
  color: #5fb7e5;
}
.tutorials-bot {
  background: #f5f5f5;
  padding: 50px 0 50px;
}
.tutorials-bot [class*="col-"] {
  margin-bottom: 20px;
  text-align: center;
}
.tutorials-bot [class*="col-"] p {
  line-height: 21px;
}
.tutorials-bot [class*="col-"] a {
  margin-bottom: 14px;
}
.tutorials-bot .chat,
.tutorials-bot .mail,
.tutorials-bot .phone {
  display: block;
  width: 42px;
  height: 45px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0 auto 20px;
}
.tutorials-bot .mail {
  background-image: url(../images/ico-mail.svg);
}
.tutorials-bot .chat {
  background-image: url(../images/ico-chat.svg);
}
.tutorials-bot .phone {
  background-image: url(../images/ico-phone.svg);
}
.tutorials-bot .num {
  height: 30px;
  line-height: 30px;
  font-size: 21px;
  line-height: 29px;
  margin-bottom: 14px;
}
@media only screen and (min-width: 768px) {
  .tutorials-bot.search-bot [class*="col-"] {
    margin-bottom: 0;
    text-align: left;
  }
}
.tutorials-bot.search-bot img {
  width: 100%;
  max-width: 187px;
  border-radius: 50%;
}
.tutorials-bot.search-bot span {
  display: block;
  line-height: 21px;
  margin-bottom: 20px;
}
.more-tutorials {
  margin-bottom: 30px;
}
.more-tutorials h2 {
  font-size: 30px;
  line-height: 38px;
}
.more-tutorials a {
  display: block;
  color: #3796c9;
  font-size: 15px;
  line-height: 25px;
}
.more-tutorials a:hover {
  color: #5fb7e5;
}
.featured-top-holder {
  position: relative;
  width: 100%;
  min-height: 400px;
  height: auto;
  overflow: hidden;
  background: url(../images/featured-top.jpg) no-repeat;
  background-size: cover;
  background-position: 50% 50%;
  padding-top: 118px;
  padding-bottom: 120px;
  margin-bottom: 50px;
}
@media only screen and (min-width: 768px) {
  .featured-top-holder {
    padding-top: 160px;
  }
}
@media only screen and (min-width: 1200px) {
  .featured-top-holder {
    padding-top: 190px;
  }
}
.featured-top-holder h1 {
  color: #fff;
  margin-bottom: 5px;
}
@media only screen and (min-width: 768px) {
  .featured-top-holder h1 {
    margin-bottom: 10px;
  }
}
.featured-top-holder p {
  color: #fff;
  margin-bottom: 35px;
}
.featured-infographics {
  margin-bottom: 75px;
}
.featured-infographics .featured-sort-container {
  position: relative;
}
.featured-infographics a {
  display: block;
  width: 100%;
  height: auto;
  overflow: hidden;
  margin-bottom: 40px;
  border: 1px solid #9b9b9b;
  transition: all 0.4s;
  border-radius: 4px;
}
.featured-infographics a:hover {
  border: 1px solid #282727;
}
.featured-infographics img {
  width: 100%;
  max-width: 100%;
  min-height: 200px;
}
.not-found-top-holder {
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility !important;
}
.not-found-top-holder .er_404 {
  position: absolute;
}
.not-found-top-holder .body_inner {
  min-height: 100%;
  position: relative;
  background-color: #fff;
}
.not-found-top-holder .er_head {
  min-height: 600px;
  background-color: rgba(255, 255, 255, 0.5);
  background: linear-gradient(112.36deg, #7694b0 0, #19906b 100%);
  text-align: center;
  overflow: hidden;
}
.not-found-top-holder .er_img,
.not-found-top-holder .er_img_happy {
  width: 110px;
  height: 145px;
  margin: 150px auto 0;
  background-image: url(../images/404-sad.svg);
}
.not-found-top-holder .er_head h1 {
  font-size: 29px;
  line-height: 34px;
  color: #fff;
  font-weight: 300;
  text-align: center;
  padding: 20px 20px 10px;
  max-width: 100%;
  margin-bottom: 0;
}
.not-found-top-holder .er_head h2 {
  font-size: 15px;
  line-height: 20px;
  color: #fff;
  font-weight: 500;
  text-align: center;
  padding: 0 20px 20px;
  margin-bottom: 0;
}
.not-found-top-holder .er_head a {
  color: #4ee2d5;
}
.not-found-top-holder .er_head .goto-home {
  display: block;
  width: 100%;
  max-width: 300px;
  height: 46px;
  border: 2px solid #fff;
  border-radius: 4px;
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  line-height: 46px;
  color: #fff;
  text-decoration: none;
  margin: 0 auto;
}
@media only screen and (min-width: 599px) {
  .not-found-top-holder .er_head {
    min-height: 740px;
  }
  .not-found-top-holder .er_img {
    margin: 250px auto 0;
  }
}
.not-found-top-holder .er_404_infographics {
  background: 0 0;
}
.not-found-top-holder .er_404_infographics .logo:after {
  background-image: url(../images/logo_dark.svg);
}
.not-found-top-holder .er_404_infographics .er_head {
  background: 0 0;
}
.not-found-top-holder .er_404_infographics .er_img {
  background-image: url(../images/404-sad-dark.svg);
  margin-top: 170px;
}
.not-found-top-holder .er_404_infographics .er_img_happy {
  display: none;
  background-image: url(../images/404-happy-dark.svg);
  margin-top: 170px;
}
.not-found-top-holder .er_404_infographics .er_head_active .er_img {
  display: none;
}
.not-found-top-holder .er_404_infographics .er_head_active .er_img_happy {
  display: block;
}
.not-found-top-holder .er_404_infographics .er_head h1 {
  color: #656565;
}
.not-found-top-holder .er_404_infographics .er_head h2 {
  color: #656565;
}
.not-found-top-holder .er_404_infographics .er_head a {
  color: #3796c9;
}
.not-found-top-holder .er_404_infographics .er_head .goto-home {
  border: 2px solid #c13134;
  background-color: #c13134;
  color: #fff;
  margin-bottom: 22px;
  transition: all 0.4s;
}
.not-found-top-holder .er_404_infographics .er_head .goto-home:hover {
  opacity: 0.8;
}
.page-top-header {
  min-height: 440px;
  background: #8ec3a7;
}
@media only screen and (min-width: 768px) {
  .page-top-header {
    height: 792px;
  }
}
.page-top-header .page-image {
  height: 440px;
  min-width: 100%;
  position: relative;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  padding-top: 50px;
}
@media only screen and (min-width: 768px) {
  .page-top-header .page-image {
    padding-top: 120px;
    height: 800px;
  }
}
.page-top-header .page-image h1 {
  color: #fff;
  margin-bottom: 5px;
  text-align: center;
}
@media only screen and (min-width: 768px) {
  .page-top-header .page-image h1 {
    margin-bottom: 10px;
  }
}
.page-top-header .page-image .place {
  padding: 0 20px;
}
.page-top-header .page-image .place h3 {
  font-size: 13px;
  color: #fff;
}
.page-top-header .page-image .place img {
  width: 100%;
  max-width: 354px;
}
@media only screen and (min-width: 768px) {
  .page-top-header .page-image .place img {
    margin-right: -45px;
  }
}
.page-top-header .page-image.about-us-page-image {
  padding-top: 145px;
  background-position: center;
}
@media only screen and (min-width: 768px) {
  .page-top-header .page-image.about-us-page-image {
    padding-top: 120px;
  }
}
.page-top-header .press-subtitle h3 {
  color: #fff;
}
.page-top-header.page-top-header-small {
  padding: 85px 0 35px;
  margin-bottom: 35px;
  height: auto;
  min-height: 1px;
}
@media only screen and (min-width: 768px) {
  .page-top-header.page-top-header-small {
    padding: 85px 0 75px;
  }
}
.page-top-header.page-top-header-small h1 {
  color: #fff;
  margin-bottom: 5px;
  text-align: center;
}
@media only screen and (min-width: 768px) {
  .page-top-header.page-top-header-small h1 {
    margin-bottom: 10px;
  }
}
.page-top-header.page-top-header-trial {
  margin-bottom: 50px;
  min-height: 300px;
}
@media only screen and (min-width: 768px) {
  .page-top-header.page-top-header-trial {
    height: 492px;
  }
}
.page-top-header.page-top-header-trial .page-image {
  height: 300px;
}
@media only screen and (min-width: 768px) {
  .page-top-header.page-top-header-trial .page-image {
    padding-top: 190px;
    height: 492px;
  }
}
.page-top-header.page-top-header-trial .page-image .place h3 {
  max-width: 800px;
  margin: 0 auto;
  font-size: 15px;
  line-height: 25px;
}
.page-top-header.page-top-header-trial h1 {
  margin-top: 40px;
}
@media only screen and (min-width: 768px) {
  .page-top-header.page-top-header-trial h1 {
    margin-top: 0;
  }
}
.page-top-header.page-top-header-trial-success {
  margin-bottom: 35px;
  height: auto;
  min-height: 1px;
}
.page-top-header.page-top-header-trial-success .page-image {
  min-height: 1px;
  height: 100%;
  min-width: 100%;
  position: relative;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  padding-top: 0;
}
@media only screen and (min-width: 768px) {
  .page-top-header.page-top-header-trial-success .page-image {
    height: 100%;
  }
}
.page-top-header.page-top-header-trial-success .page-image h1 {
  padding: 85px 0 75px;
}
@media only screen and (min-width: 768px) {
  .page-top-header.page-top-header-trial-success .page-image h1 {
    padding: 85px 0 75px;
  }
}
.about-us-awards,
.about-us-founders,
.about-us-info-block,
.about-us-management,
.about-us-press-mentions {
  margin-bottom: 0 !important;
  font-size: 15px;
  line-height: 25px;
  padding-top: 55px;
}
.about-us-awards .container,
.about-us-founders .container,
.about-us-info-block .container,
.about-us-management .container,
.about-us-press-mentions .container {
  margin-left: auto;
  margin-right: auto;
}
.about-us-awards.vc_row,
.about-us-founders.vc_row,
.about-us-info-block.vc_row,
.about-us-management.vc_row,
.about-us-press-mentions.vc_row {
  margin-left: 0;
  margin-right: 0;
}
.about-us-awards .vc_col-sm-4,
.about-us-founders .vc_col-sm-4,
.about-us-info-block .vc_col-sm-4,
.about-us-management .vc_col-sm-4,
.about-us-press-mentions .vc_col-sm-4 {
  padding: 0 15px;
  margin-bottom: 15px;
}
.about-us-info-block h3 {
  font-size: 29px;
  margin-bottom: 26px;
  line-height: 35px;
}
.about-us-info-block a {
  color: #3796c9;
}
.about-us-info-block a:hover {
  color: #5fb7e5;
}
.about-us-info-block .cta-box__header {
  background: #ebebee;
  text-align: center;
  padding: 0 10px 10px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.about-us-info-block .cta-box__header img {
  max-width: 140px;
  margin-top: 27px;
}
.about-us-info-block .cta-box__footer {
  background: #3195cb;
  padding: 13px 10px 12px;
  text-align: center;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.about-us-info-block .cta-box__footer a {
  color: #fff;
}
.about-us-info-block .cta-box__footer a:hover {
  color: #fff;
}
.about-us-founders,
.about-us-management {
  background: #ebebee;
}
.about-us-founders img,
.about-us-management img {
  margin: 0 auto 18px !important;
  display: block;
}
.about-us-founders h4,
.about-us-management h4 {
  font-size: 21px;
  line-height: 25px;
  margin-bottom: 15px;
}
.about-us-founders h5,
.about-us-management h5 {
  font-size: 15px;
  line-height: 25px;
  margin-bottom: 17px;
}
.about-us-founders {
  background: #8db1ca;
  color: #fff;
}
.about-us-founders h2 {
  color: #fff;
}
.about-us-awards {
  background: #476579;
}
.about-us-awards img {
  max-width: 100%;
  height: auto !important;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.about-us-press-mentions h3 {
  font-size: 21px;
  line-height: 25px;
  margin-bottom: 5px;
}
.about-us-press-mentions p {
  font-size: 15px;
  line-height: 25px;
  margin-bottom: 27px;
}
.about-us-press-mentions p a {
  font-style: normal;
  color: #3796c9;
}
.about-us-press-mentions p a:hover {
  color: #5fb7e5;
}
.vc_row-full-width:last-of-type {
  height: 5px;
}
.page-content {
  line-height: 30px;
}
.page-content input {
  margin-bottom: 20px;
}
.page-content input[type="radio"] {
  margin-right: 10px;
}
.page-content input[type="text"],
.page-content input[type="email"],
.page-content input[type="tel"] {
  width: 100%;
  max-width: 380px;
}
.page-content li.gfield_error {
  color: red;
}
.page-content li.gfield_error input {
  border: 1px solid red;
}
.ebooks-top-holder {
  position: relative;
  width: 100%;
  min-height: 300px;
  height: auto;
  overflow: hidden;
  background: url(../images/ebooks-top.jpg) no-repeat;
  background-size: cover;
  background-position: 50% 50%;
  padding-top: 120px;
  margin-bottom: 50px;
}
@media only screen and (min-width: 1200px) {
  .ebooks-top-holder {
    padding-top: 110px;
  }
}
.ebooks-top-holder h1 {
  color: #fff;
  margin-bottom: 5px;
}
@media only screen and (min-width: 768px) {
  .ebooks-top-holder h1 {
    margin-bottom: 10px;
  }
}
.ebooks-top-holder p {
  color: #fff;
  margin-bottom: 35px;
  line-height: 25px;
}
.ebooks-top-holder.single-top-holder {
  min-height: 200px;
  background: #3195cb;
  padding-top: 95px;
  padding-bottom: 50px;
}
.ebooks-top-holder.single-top-holder h1 {
  font-size: 26px;
}
@media only screen and (min-width: 768px) {
  .ebooks-top-holder.single-top-holder h1 {
    font-weight: 300;
    font-size: 49px;
  }
}
.ebooks-top-holder.single-top-holder p {
  margin-bottom: 5px;
}
.container.ebooks {
  margin-bottom: 30px;
}
.container.ebooks .ebook {
  margin-bottom: 46px;
  text-align: center;
}
@media only screen and (min-width: 768px) {
  .container.ebooks .ebook {
    text-align: left;
    margin-bottom: 30px;
    min-height: 400px;
  }
}
.container.ebooks .ebook img {
  max-width: 100%;
  margin-bottom: 15px;
}
@media only screen and (min-width: 768px) {
  .container.ebooks .ebook img {
    margin-bottom: 20px;
    max-width: 220px;
    width: 220px;
  }
}
.container.ebooks .ebook a {
  font-size: 15px;
  line-height: 21px;
  transition: all 0.4s;
}
.container.ebooks .ebook a:hover {
  color: #9b9b9b;
  text-decoration: none;
  opacity: 0.8;
}
@media only screen and (min-width: 768px) {
  .container.ebooks .ebook a {
    font-size: 17px;
  }
}
.container.ebooks .ebook a span {
  display: block;
}
.container.single-ebooks .image img {
  width: 100%;
}
.container.single-ebooks article.post .post-content h2 {
  font-size: 24px;
  line-height: 33px;
}
@media only screen and (min-width: 768px) {
  .container.single-ebooks article.post .post-content h2 {
    font-size: 30px;
    line-height: 38px;
    margin-top: 0;
  }
}
.container.single-ebooks article.post .post-content h3 {
  font-size: 24px;
  line-height: 32px;
  margin-top: 40px;
  margin-bottom: 15px;
}
.container.single-ebooks
  article.post
  .post-content
  .wpcf7-response-output.wpcf7-display-none.wpcf7-mail-sent-ok,
.container.single-ebooks
  article.post
  .post-content
  .wpcf7-response-output.wpcf7-display-none.wpcf7-validation-errors {
  display: none !important;
}
.container.single-ebooks article.post .post-content .ajax-loader {
  position: absolute;
  right: 50%;
  margin-right: -8px;
  margin-top: 70px;
}
.container.single-ebooks article.post .post-content .download-ebook {
  display: none;
  font-size: 14px;
}
.container.single-ebooks article.post .post-content form.hs-form {
  background: 0 0;
  padding: 0 !important;
}
.container.single-ebooks article.post .post-content form.hs-form .field {
  margin-bottom: 20px;
}
.container.single-ebooks article.post .post-content form.hs-form ul li {
  padding-left: 0;
}
.container.single-ebooks article.post .post-content form.hs-form ul li:before {
  display: none;
}
.container.single-ebooks
  article.post
  .post-content
  form.hs-form
  .hs-error-msgs {
  color: #ce2533;
}
.container.single-ebooks article.post .post-content form input,
.container.single-ebooks article.post .post-content form label,
.container.single-ebooks article.post .post-content form textarea {
  width: 100%;
}
.container.single-ebooks article.post .post-content form label {
  font-size: 15px;
  line-height: 21px;
}
.container.single-ebooks article.post .post-content form input {
  border-radius: 4px;
  padding: 9px 10px 10px;
  border: 1px solid #7d7d7d;
  margin-top: 5px;
}
.container.single-ebooks article.post .post-content form input[type="submit"] {
  background: #3195cb;
  color: #fff;
  border: none;
  padding: 10px 10px 11px !important;
  font-size: 17px !important;
}
.container.thank-you-bottom-nav {
  margin-bottom: 30px;
}
.container.thank-you-bottom-nav .blog a,
.container.thank-you-bottom-nav .customers a,
.container.thank-you-bottom-nav .ebooks a,
.container.thank-you-bottom-nav .features a {
  position: relative;
  display: block;
  padding: 25px;
  max-width: 220px;
  min-height: 255px;
  border-radius: 4px;
  text-align: center;
  font-size: 15px;
  line-height: 19px;
  color: #fff;
  transition: all 0.4s;
  margin: 0 auto 30px;
}
@media only screen and (min-width: 992px) {
  .container.thank-you-bottom-nav .blog a,
  .container.thank-you-bottom-nav .customers a,
  .container.thank-you-bottom-nav .ebooks a,
  .container.thank-you-bottom-nav .features a {
    max-width: 100%;
  }
}
.container.thank-you-bottom-nav .blog a:hover,
.container.thank-you-bottom-nav .customers a:hover,
.container.thank-you-bottom-nav .ebooks a:hover,
.container.thank-you-bottom-nav .features a:hover {
  opacity: 0.7;
  text-decoration: none;
}
.container.thank-you-bottom-nav .blog a img,
.container.thank-you-bottom-nav .customers a img,
.container.thank-you-bottom-nav .ebooks a img,
.container.thank-you-bottom-nav .features a img {
  max-width: 100%;
  display: block;
  margin: 0 auto;
}
.container.thank-you-bottom-nav .features a {
  background: #8ec3a7;
  padding: 60px 36px;
}
.container.thank-you-bottom-nav .features a img {
  margin-bottom: 42px;
}
.container.thank-you-bottom-nav .ebooks a {
  background: #3796c9;
  padding: 50px 57px;
}
.container.thank-you-bottom-nav .ebooks a img {
  margin-bottom: 35px;
}
.container.thank-you-bottom-nav .blog a {
  background: #9c82b6;
  padding: 44px 51px;
}
.container.thank-you-bottom-nav .blog a img {
  margin-bottom: 32px;
}
.container.thank-you-bottom-nav .customers a {
  background: #f09b69;
}
.container.thank-you-bottom-nav .customers a img {
  margin-bottom: 38px;
  max-width: 100px;
}
.search-section {
  width: 100%;
}
.search-section .auto-suggest-submit {
  display: none;
  border-radius: 4px;
  background: 0 0;
  position: absolute;
  top: 9px;
  right: 0;
  width: 30px;
  height: 30px;
  padding: 14px;
  background: url(../images/ico-search.svg) no-repeat;
  background-position: center;
  z-index: 1;
  transition: all 0.4s;
  transition-timing-function: ease-out;
  border: none;
}
.search-section .auto-suggest-submit:focus,
.search-section .auto-suggest-submit:hover,
.search-section .auto-suggest-submit:visited {
  background: 0 0;
  background: url(../images/ico-search.svg) no-repeat;
  background-position: center;
}
.search-section .auto-suggest-submit span {
  display: none;
}
.asr-container {
  position: absolute;
  overflow: visible;
  z-index: 100;
  width: 100%;
  border-radius: 4px;
  border: 1px solid #ccc;
  margin-top: 5px;
}
.asr-container:after {
  content: "";
  display: none;
  position: absolute;
  top: -8px;
  left: 50px;
  width: 14px;
  height: 14px;
  background: #fff;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.asr-container .result-section {
  border-radius: 4px;
  background: #fff;
  max-height: 150px;
}
@media only screen and (min-width: 768px) {
  .asr-container .result-section {
    max-height: 400px;
  }
}
.asr-container .result-section .post-details {
  border: none;
  padding: 25px 25px 0;
}
.asr-container .result-section .post-details:last-child {
  padding-bottom: 25px;
}
.asr-container .result-section .post-details .col-left {
  max-width: 70px;
}
.asr-container .result-section .post-details .post-img {
  max-width: 60px;
}
.asr-container .result-section .post-details .post-img img {
  width: 100%;
  height: auto;
}
.asr-container .result-section .post-details .post-title a {
  font-size: 15px;
  line-height: 21px;
  color: #656565;
}
.asr-container .result-section .post-details .post-title a:hover {
  color: #9b9b9b;
  text-decoration: none;
  box-shadow: none;
}
.asr-container .result-section .post-details .post-date {
  font-size: 13px;
  line-height: 20px;
  color: #9b9b9b;
  padding: 0;
}
.asr-container .more-res {
  font-size: 15px;
  background: #f5f5f5;
  padding: 17px 25px;
  border-radius: 4px;
  text-align: left;
}
.asr-container .more-res a {
  color: #3796c9;
  text-decoration: none;
}
.asr-container .more-res a:hover {
  color: #5fb7e5;
  text-decoration: underline;
}
.asr-container.no-result {
  background: #fff;
  padding: 20px;
}
.asr-container.no-result .result-section {
  color: #656565;
  background: 0 0;
  overflow: hidden;
}
.asr-container.no-result .result-section span {
  display: block;
  font-size: 13px;
  line-height: 18px;
  margin-top: 5px;
}
@media only screen and (min-width: 768px) {
  .search-block.active form {
    right: 0;
  }
}
@media only screen and (min-width: 768px) {
  .search-block.active .auto-suggest-submit {
    left: 9px;
  }
}
@media only screen and (min-width: 768px) {
  .search-block.active .btn-search {
    width: 30px;
    background-position: center;
  }
}
.search-block.active .btn-search span {
  display: none;
}
.search-block form {
  display: none;
  position: relative;
  border-radius: 4px;
  border: 1px solid #ccc;
  border-right: none;
  padding: 11px 12px 12px 47px;
  right: -120%;
  transition: right 0.4s;
  transition-timing-function: ease-out;
}
@media only screen and (min-width: 768px) {
  .search-block form {
    display: block;
  }
}
.search-block input.auto-suggest-front {
  border: none;
  width: 100%;
  font-size: 17px;
  padding: 0;
  margin-top: 0;
  height: 23px;
  line-height: 23px;
}
.popup-mob-search-form .search-block form {
  display: block;
  right: auto;
  left: 0;
  border: none;
} /*# sourceMappingURL=style.min.css.map */
